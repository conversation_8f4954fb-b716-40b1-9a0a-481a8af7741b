import{d as o,h as r,o as a,u as t,S as n,w as s,q as d}from"./app-BxByyVXe.js";const h=o({__name:"TextLink",props:{href:{},tabindex:{},method:{},as:{}},setup(i){return(e,u)=>(a(),r(t(n),{href:e.href,tabindex:e.tabindex,method:e.method,as:e.as,class:"text-foreground underline decoration-neutral-300 underline-offset-4 transition-colors duration-300 ease-out hover:decoration-current! dark:decoration-neutral-500"},{default:s(()=>[d(e.$slots,"default")]),_:3},8,["href","tabindex","method","as"]))}});export{h as _};
