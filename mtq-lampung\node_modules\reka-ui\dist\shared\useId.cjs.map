{"version": 3, "file": "useId.cjs", "sources": ["../../src/shared/useId.ts"], "sourcesContent": ["import * as vue from 'vue'\n// Inspired from https://github.com/tailwindlabs/headlessui/issues/2913\n// as the alternative, and a fallback for Vue version < 3.5\nimport { injectConfigProviderContext } from '@/ConfigProvider/ConfigProvider.vue'\n\nlet count = 0\n/**\n * The `useId` function generates a unique identifier using a provided deterministic ID or a default\n * one prefixed with \"reka-\", or the provided one via `useId` props from `<ConfigProvider>`.\n * @param {string | null | undefined} [deterministicId] - The `useId` function you provided takes an\n * optional parameter `deterministicId`, which can be a string, null, or undefined. If\n * `deterministicId` is provided, the function will return it. Otherwise, it will generate an id using\n * the `useId` function obtained\n */\nexport function useId(deterministicId?: string | null | undefined, prefix = 'reka') {\n  if (deterministicId)\n    return deterministicId\n\n  const configProviderContext = injectConfigProviderContext({ useId: undefined })\n\n  if (Object.hasOwn(vue, 'useId')) {\n    return `${prefix}-${vue.useId?.()}`\n  }\n  else if (configProviderContext.useId) {\n    return `${prefix}-${configProviderContext.useId()}`\n  }\n\n  return `${prefix}-${++count}`\n}\n"], "names": ["injectConfigProviderContext", "vue"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAKA,IAAI,KAAQ,GAAA,CAAA;AASI,SAAA,KAAA,CAAM,eAA6C,EAAA,MAAA,GAAS,MAAQ,EAAA;AAClF,EAAI,IAAA,eAAA;AACF,IAAO,OAAA,eAAA;AAET,EAAA,MAAM,qBAAwB,GAAAA,yDAAA,CAA4B,EAAE,KAAA,EAAO,QAAW,CAAA;AAE9E,EAAA,IAAI,MAAO,CAAA,MAAA,CAAOC,cAAK,EAAA,OAAO,CAAG,EAAA;AAC/B,IAAA,OAAO,CAAG,EAAA,MAAM,CAAI,CAAA,EAAAA,cAAA,CAAI,SAAS,CAAA,CAAA;AAAA,GACnC,MAAA,IACS,sBAAsB,KAAO,EAAA;AACpC,IAAA,OAAO,CAAG,EAAA,MAAM,CAAI,CAAA,EAAA,qBAAA,CAAsB,OAAO,CAAA,CAAA;AAAA;AAGnD,EAAA,OAAO,CAAG,EAAA,MAAM,CAAI,CAAA,EAAA,EAAE,KAAK,CAAA,CAAA;AAC7B;;;;"}