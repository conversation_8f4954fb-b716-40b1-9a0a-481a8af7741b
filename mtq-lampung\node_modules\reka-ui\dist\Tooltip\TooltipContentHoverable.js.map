{"version": 3, "file": "TooltipContentHoverable.js", "sources": ["../../src/Tooltip/TooltipContentHoverable.vue"], "sourcesContent": ["<script setup lang=\"ts\">\nimport type { TooltipContentImplProps } from './TooltipContentImpl.vue'\nimport { useForwardExpose, useForwardProps, useGraceArea } from '@/shared'\nimport TooltipContentImpl from './TooltipContentImpl.vue'\nimport { injectTooltipProviderContext } from './TooltipProvider.vue'\nimport { injectTooltipRootContext } from './TooltipRoot.vue'\n\nconst props = defineProps<TooltipContentImplProps>()\nconst forwardedProps = useForwardProps(props)\nconst { forwardRef, currentElement } = useForwardExpose()\n\nconst { trigger, onClose } = injectTooltipRootContext()\nconst providerContext = injectTooltipProviderContext()\n\nconst { isPointerInTransit, onPointerExit } = useGraceArea(trigger, currentElement)\n\nproviderContext.isPointerInTransitRef = isPointerInTransit\nonPointerExit(() => {\n  onClose()\n})\n</script>\n\n<template>\n  <TooltipContentImpl\n    :ref=\"forwardRef\"\n    v-bind=\"forwardedProps\"\n  >\n    <slot />\n  </TooltipContentImpl>\n</template>\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOA,IAAA,MAAM,KAAQ,GAAA,OAAA;AACd,IAAM,MAAA,cAAA,GAAiB,gBAAgB,KAAK,CAAA;AAC5C,IAAA,MAAM,EAAE,UAAA,EAAY,cAAe,EAAA,GAAI,gBAAiB,EAAA;AAExD,IAAA,MAAM,EAAE,OAAA,EAAS,OAAQ,EAAA,GAAI,wBAAyB,EAAA;AACtD,IAAA,MAAM,kBAAkB,4BAA6B,EAAA;AAErD,IAAA,MAAM,EAAE,kBAAoB,EAAA,aAAA,EAAkB,GAAA,YAAA,CAAa,SAAS,cAAc,CAAA;AAElF,IAAA,eAAA,CAAgB,qBAAwB,GAAA,kBAAA;AACxC,IAAA,aAAA,CAAc,MAAM;AAClB,MAAQ,OAAA,EAAA;AAAA,KACT,CAAA;;;;;;;;;;;;;;"}