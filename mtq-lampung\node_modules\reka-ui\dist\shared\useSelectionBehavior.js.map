{"version": 3, "file": "useSelectionBehavior.js", "sources": ["../../src/shared/useSelectionBehavior.ts"], "sourcesContent": ["import type { Ref, UnwrapNestedRefs } from 'vue'\nimport { ref } from 'vue'\nimport { findValuesBetween } from './arrays'\n\nexport function useSelectionBehavior<T>(\n  modelValue: Ref<T | T[]>,\n  props: UnwrapNestedRefs<{ multiple?: boolean, selectionBehavior?: 'toggle' | 'replace' }>,\n) {\n  const firstValue = ref()\n\n  const onSelectItem = (val: T, condition: (existingValue: T) => boolean) => {\n    // multiple select\n    if (props.multiple && Array.isArray(modelValue.value)) {\n      if (props.selectionBehavior === 'replace') {\n        modelValue.value = [val]\n        firstValue.value = val\n      }\n      else {\n        const index = modelValue.value.findIndex(v => condition(v))\n        if (index !== -1)\n          modelValue.value = modelValue.value.filter((_, i) => i !== index)\n        else\n          modelValue.value = [...modelValue.value, val]\n      }\n    }\n    // single select\n    else {\n      if (props.selectionBehavior === 'replace') {\n        modelValue.value = { ...val }\n      }\n      else {\n        if (!Array.isArray(modelValue.value) && condition(modelValue.value))\n          modelValue.value = undefined as any\n        else\n          modelValue.value = { ...val }\n      }\n    }\n    return modelValue.value\n  }\n\n  function handleMultipleReplace(intent: 'first' | 'last' | 'prev' | 'next', currentElement: HTMLElement | Element | null, getItems: () => { ref: HTMLElement, value?: any }[], options: any[]) {\n    if (!firstValue?.value || !props.multiple || !Array.isArray(modelValue.value))\n      return\n\n    const collection = getItems().filter(i => i.ref.dataset.disabled !== '')\n    const lastValue = collection.find(i => i.ref === currentElement)?.value\n    if (!lastValue)\n      return\n\n    let value: T[] | null = null\n    switch (intent) {\n      case 'prev':\n      case 'next': {\n        value = findValuesBetween(options, firstValue.value, lastValue)\n        break\n      }\n      case 'first': {\n        value = findValuesBetween(options, firstValue.value, options?.[0])\n        break\n      }\n      case 'last': {\n        value = findValuesBetween(options, firstValue.value, options?.[options.length - 1])\n        break\n      }\n    }\n\n    modelValue.value = value\n  }\n\n  return {\n    firstValue,\n    onSelectItem,\n    handleMultipleReplace,\n  }\n}\n"], "names": [], "mappings": ";;;AAIgB,SAAA,oBAAA,CACd,YACA,KACA,EAAA;AACA,EAAA,MAAM,aAAa,GAAI,EAAA;AAEvB,EAAM,MAAA,YAAA,GAAe,CAAC,GAAA,EAAQ,SAA6C,KAAA;AAEzE,IAAA,IAAI,MAAM,QAAY,IAAA,KAAA,CAAM,OAAQ,CAAA,UAAA,CAAW,KAAK,CAAG,EAAA;AACrD,MAAI,IAAA,KAAA,CAAM,sBAAsB,SAAW,EAAA;AACzC,QAAW,UAAA,CAAA,KAAA,GAAQ,CAAC,GAAG,CAAA;AACvB,QAAA,UAAA,CAAW,KAAQ,GAAA,GAAA;AAAA,OAEhB,MAAA;AACH,QAAA,MAAM,QAAQ,UAAW,CAAA,KAAA,CAAM,UAAU,CAAK,CAAA,KAAA,SAAA,CAAU,CAAC,CAAC,CAAA;AAC1D,QAAA,IAAI,KAAU,KAAA,EAAA;AACZ,UAAW,UAAA,CAAA,KAAA,GAAQ,WAAW,KAAM,CAAA,MAAA,CAAO,CAAC,CAAG,EAAA,CAAA,KAAM,MAAM,KAAK,CAAA;AAAA;AAEhE,UAAA,UAAA,CAAW,KAAQ,GAAA,CAAC,GAAG,UAAA,CAAW,OAAO,GAAG,CAAA;AAAA;AAChD,KAGG,MAAA;AACH,MAAI,IAAA,KAAA,CAAM,sBAAsB,SAAW,EAAA;AACzC,QAAW,UAAA,CAAA,KAAA,GAAQ,EAAE,GAAG,GAAI,EAAA;AAAA,OAEzB,MAAA;AACH,QAAI,IAAA,CAAC,MAAM,OAAQ,CAAA,UAAA,CAAW,KAAK,CAAK,IAAA,SAAA,CAAU,WAAW,KAAK,CAAA;AAChE,UAAA,UAAA,CAAW,KAAQ,GAAA,MAAA;AAAA;AAEnB,UAAW,UAAA,CAAA,KAAA,GAAQ,EAAE,GAAG,GAAI,EAAA;AAAA;AAChC;AAEF,IAAA,OAAO,UAAW,CAAA,KAAA;AAAA,GACpB;AAEA,EAAA,SAAS,qBAAsB,CAAA,MAAA,EAA4C,cAA8C,EAAA,QAAA,EAAqD,OAAgB,EAAA;AAC5L,IAAI,IAAA,CAAC,UAAY,EAAA,KAAA,IAAS,CAAC,KAAA,CAAM,YAAY,CAAC,KAAA,CAAM,OAAQ,CAAA,UAAA,CAAW,KAAK,CAAA;AAC1E,MAAA;AAEF,IAAM,MAAA,UAAA,GAAa,UAAW,CAAA,MAAA,CAAO,OAAK,CAAE,CAAA,GAAA,CAAI,OAAQ,CAAA,QAAA,KAAa,EAAE,CAAA;AACvE,IAAA,MAAM,YAAY,UAAW,CAAA,IAAA,CAAK,OAAK,CAAE,CAAA,GAAA,KAAQ,cAAc,CAAG,EAAA,KAAA;AAClE,IAAA,IAAI,CAAC,SAAA;AACH,MAAA;AAEF,IAAA,IAAI,KAAoB,GAAA,IAAA;AACxB,IAAA,QAAQ,MAAQ;AAAA,MACd,KAAK,MAAA;AAAA,MACL,KAAK,MAAQ,EAAA;AACX,QAAA,KAAA,GAAQ,iBAAkB,CAAA,OAAA,EAAS,UAAW,CAAA,KAAA,EAAO,SAAS,CAAA;AAC9D,QAAA;AAAA;AACF,MACA,KAAK,OAAS,EAAA;AACZ,QAAA,KAAA,GAAQ,kBAAkB,OAAS,EAAA,UAAA,CAAW,KAAO,EAAA,OAAA,GAAU,CAAC,CAAC,CAAA;AACjE,QAAA;AAAA;AACF,MACA,KAAK,MAAQ,EAAA;AACX,QAAQ,KAAA,GAAA,iBAAA,CAAkB,SAAS,UAAW,CAAA,KAAA,EAAO,UAAU,OAAQ,CAAA,MAAA,GAAS,CAAC,CAAC,CAAA;AAClF,QAAA;AAAA;AACF;AAGF,IAAA,UAAA,CAAW,KAAQ,GAAA,KAAA;AAAA;AAGrB,EAAO,OAAA;AAAA,IACL,UAAA;AAAA,IACA,YAAA;AAAA,IACA;AAAA,GACF;AACF;;;;"}