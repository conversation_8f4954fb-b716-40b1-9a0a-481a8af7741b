{"version": 3, "file": "useFocusGuards.js", "sources": ["../../src/shared/useFocusGuards.ts"], "sourcesContent": ["import { isClient } from '@vueuse/shared'\nimport { watchEffect } from 'vue'\n\n/** Number of components which have requested interest to have focus guards */\nlet count = 0\n\n/**\n * Injects a pair of focus guards at the edges of the whole DOM tree\n * to ensure `focusin` & `focusout` events can be caught consistently.\n */\nexport function useFocusGuards() {\n  watchEffect((cleanupFn) => {\n    if (!isClient)\n      return\n    const edgeGuards = document.querySelectorAll('[data-reka-focus-guard]')\n    document.body.insertAdjacentElement(\n      'afterbegin',\n      edgeGuards[0] ?? createFocusGuard(),\n    )\n    document.body.insertAdjacentElement(\n      'beforeend',\n      edgeGuards[1] ?? createFocusGuard(),\n    )\n    count++\n\n    cleanupFn(() => {\n      if (count === 1) {\n        document\n          .querySelectorAll('[data-reka-focus-guard]')\n          .forEach(node => node.remove())\n      }\n      count--\n    })\n  })\n}\n\nfunction createFocusGuard() {\n  const element = document.createElement('span')\n  element.setAttribute('data-reka-focus-guard', '')\n  element.tabIndex = 0\n  element.style.outline = 'none'\n  element.style.opacity = '0'\n  element.style.position = 'fixed'\n  element.style.pointerEvents = 'none'\n  return element\n}\n"], "names": [], "mappings": ";;;AAIA,IAAI,KAAQ,GAAA,CAAA;AAML,SAAS,cAAiB,GAAA;AAC/B,EAAA,WAAA,CAAY,CAAC,SAAc,KAAA;AACzB,IAAA,IAAI,CAAC,QAAA;AACH,MAAA;AACF,IAAM,MAAA,UAAA,GAAa,QAAS,CAAA,gBAAA,CAAiB,yBAAyB,CAAA;AACtE,IAAA,QAAA,CAAS,IAAK,CAAA,qBAAA;AAAA,MACZ,YAAA;AAAA,MACA,UAAA,CAAW,CAAC,CAAA,IAAK,gBAAiB;AAAA,KACpC;AACA,IAAA,QAAA,CAAS,IAAK,CAAA,qBAAA;AAAA,MACZ,WAAA;AAAA,MACA,UAAA,CAAW,CAAC,CAAA,IAAK,gBAAiB;AAAA,KACpC;AACA,IAAA,KAAA,EAAA;AAEA,IAAA,SAAA,CAAU,MAAM;AACd,MAAA,IAAI,UAAU,CAAG,EAAA;AACf,QAAA,QAAA,CACG,iBAAiB,yBAAyB,CAAA,CAC1C,QAAQ,CAAQ,IAAA,KAAA,IAAA,CAAK,QAAQ,CAAA;AAAA;AAElC,MAAA,KAAA,EAAA;AAAA,KACD,CAAA;AAAA,GACF,CAAA;AACH;AAEA,SAAS,gBAAmB,GAAA;AAC1B,EAAM,MAAA,OAAA,GAAU,QAAS,CAAA,aAAA,CAAc,MAAM,CAAA;AAC7C,EAAQ,OAAA,CAAA,YAAA,CAAa,yBAAyB,EAAE,CAAA;AAChD,EAAA,OAAA,CAAQ,QAAW,GAAA,CAAA;AACnB,EAAA,OAAA,CAAQ,MAAM,OAAU,GAAA,MAAA;AACxB,EAAA,OAAA,CAAQ,MAAM,OAAU,GAAA,GAAA;AACxB,EAAA,OAAA,CAAQ,MAAM,QAAW,GAAA,OAAA;AACzB,EAAA,OAAA,CAAQ,MAAM,aAAgB,GAAA,MAAA;AAC9B,EAAO,OAAA,OAAA;AACT;;;;"}