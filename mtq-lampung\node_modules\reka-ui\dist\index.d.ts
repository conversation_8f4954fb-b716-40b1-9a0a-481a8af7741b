import { AllowedComponentProps } from 'vue';
import { CalendarDateTime } from '@internationalized/date';
import { Component } from 'vue';
import { ComponentCustomProperties } from 'vue';
import { ComponentCustomProps } from 'vue';
import { ComponentInternalInstance } from 'vue';
import { ComponentOptionsBase } from 'vue';
import { ComponentOptionsMixin } from 'vue';
import { ComponentProps } from 'vue-component-type-helpers';
import { ComponentProvideOptions } from 'vue';
import { ComponentPublicInstance } from 'vue';
import { ComputedRef } from 'vue';
import { CreateComponentPublicInstanceWithMixins } from 'vue';
import { CSSProperties } from 'vue';
import { DateValue } from '@internationalized/date';
import { DebuggerEvent } from 'vue';
import { DefineComponent } from 'vue';
import { EventHook } from '@vueuse/core';
import { EventHookOn } from '@vueuse/core';
import { ExtractPropTypes } from 'vue';
import { GlobalComponents } from 'vue';
import { GlobalDirectives } from 'vue';
import { HTMLAttributes } from 'vue';
import { ImgHTMLAttributes } from 'vue';
import { MaybeRef } from 'vue';
import { MaybeRefOrGetter } from 'vue';
import { nextTick } from 'vue';
import { OnCleanup } from '@vue/reactivity';
import { PropType } from 'vue';
import { PublicProps } from 'vue';
import { Ref } from 'vue';
import { ReferenceElement } from '@floating-ui/vue';
import { RendererElement } from 'vue';
import { RendererNode } from 'vue';
import { ShallowUnwrapRef } from 'vue';
import { Slot as Slot_2 } from 'vue';
import { SlotsType } from 'vue';
import { Time } from '@internationalized/date';
import { UnwrapNestedRefs } from 'vue';
import { VirtualItem } from '@tanstack/vue-virtual';
import { Virtualizer } from '@tanstack/vue-virtual';
import { VNode } from 'vue';
import { VNodeProps } from 'vue';
import { VNodeRef } from 'vue';
import { WatchOptions } from 'vue';
import { WatchStopHandle } from 'vue';
import { WritableComputedRef } from 'vue';
import { ZonedDateTime } from '@internationalized/date';

declare const __VLS_component: DefineComponent<ContextMenuPortalProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<ContextMenuPortalProps> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>;

declare type __VLS_PrettifyLocal<T> = {
    [K in keyof T]: T[K];
} & {};

declare type __VLS_PrettifyLocal_10<T> = {
    [K in keyof T]: T[K];
} & {};

declare type __VLS_PrettifyLocal_11<T> = {
    [K in keyof T]: T[K];
} & {};

declare type __VLS_PrettifyLocal_12<T> = {
    [K in keyof T]: T[K];
} & {};

declare type __VLS_PrettifyLocal_13<T> = {
    [K in keyof T]: T[K];
} & {};

declare type __VLS_PrettifyLocal_14<T> = {
    [K in keyof T]: T[K];
} & {};

declare type __VLS_PrettifyLocal_15<T> = {
    [K in keyof T]: T[K];
} & {};

declare type __VLS_PrettifyLocal_2<T> = {
    [K in keyof T]: T[K];
} & {};

declare type __VLS_PrettifyLocal_3<T> = {
    [K in keyof T]: T[K];
} & {};

declare type __VLS_PrettifyLocal_4<T> = {
    [K in keyof T]: T[K];
} & {};

declare type __VLS_PrettifyLocal_5<T> = {
    [K in keyof T]: T[K];
} & {};

declare type __VLS_PrettifyLocal_6<T> = {
    [K in keyof T]: T[K];
} & {};

declare type __VLS_PrettifyLocal_7<T> = {
    [K in keyof T]: T[K];
} & {};

declare type __VLS_PrettifyLocal_8<T> = {
    [K in keyof T]: T[K];
} & {};

declare type __VLS_PrettifyLocal_9<T> = {
    [K in keyof T]: T[K];
} & {};

declare function __VLS_template(): {
    attrs: Partial<{}>;
    slots: {
        default?(_: {}): any;
    };
    refs: {};
    rootEl: any;
};

declare type __VLS_TemplateResult = ReturnType<typeof __VLS_template>;

declare type __VLS_WithTemplateSlots<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_10<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_100<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_101<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_102<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_103<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_104<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_105<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_106<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_107<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_108<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_109<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_11<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_110<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_111<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_112<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_113<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_114<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_115<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_116<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_117<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_118<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_119<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_12<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_120<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_121<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_122<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_123<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_124<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_125<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_126<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_127<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_128<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_129<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_13<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_130<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_131<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_132<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_133<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_134<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_135<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_136<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_137<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_138<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_139<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_14<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_140<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_141<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_142<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_143<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_144<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_145<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_146<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_147<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_148<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_149<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_15<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_150<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_151<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_152<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_153<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_154<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_155<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_156<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_157<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_158<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_159<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_16<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_160<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_161<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_162<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_163<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_164<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_165<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_166<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_167<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_168<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_169<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_17<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_170<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_171<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_172<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_173<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_174<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_175<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_176<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_177<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_178<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_179<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_18<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_180<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_181<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_182<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_183<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_184<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_185<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_186<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_187<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_188<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_189<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_19<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_190<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_191<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_192<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_193<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_194<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_195<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_196<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_197<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_198<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_199<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_2<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_20<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_200<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_201<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_202<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_203<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_204<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_205<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_206<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_207<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_208<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_209<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_21<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_210<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_211<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_212<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_213<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_214<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_215<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_216<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_217<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_218<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_219<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_22<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_220<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_221<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_222<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_223<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_224<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_225<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_226<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_227<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_228<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_229<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_23<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_230<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_231<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_232<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_233<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_234<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_235<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_236<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_237<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_238<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_239<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_24<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_240<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_241<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_242<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_243<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_244<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_245<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_246<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_247<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_248<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_249<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_25<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_250<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_251<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_252<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_253<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_254<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_255<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_256<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_257<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_258<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_259<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_26<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_260<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_261<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_262<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_263<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_264<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_265<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_266<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_267<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_268<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_269<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_27<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_270<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_271<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_272<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_273<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_274<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_275<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_276<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_277<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_278<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_279<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_28<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_280<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_281<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_282<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_283<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_284<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_285<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_286<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_287<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_288<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_289<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_29<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_3<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_30<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_31<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_32<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_33<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_34<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_35<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_36<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_37<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_38<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_39<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_4<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_40<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_41<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_42<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_43<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_44<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_45<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_46<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_47<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_48<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_49<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_5<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_50<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_51<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_52<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_53<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_54<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_55<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_56<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_57<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_58<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_59<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_6<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_60<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_61<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_62<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_63<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_64<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_65<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_66<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_67<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_68<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_69<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_7<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_70<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_71<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_72<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_73<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_74<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_75<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_76<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_77<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_78<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_79<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_8<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_80<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_81<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_82<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_83<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_84<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_85<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_86<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_87<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_88<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_89<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_9<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_90<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_91<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_92<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_93<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_94<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_95<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_96<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_97<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_98<T, S> = T & {
    new (): {
        $slots: S;
    };
};

declare type __VLS_WithTemplateSlots_99<T, S> = T & {
    new (): {
        $slots: S;
    };
};

export declare type AcceptableInputValue = string | Record<string, any>;

export declare type AcceptableValue = string | number | bigint | Record<string, any> | null;

export declare const AccordionContent: __VLS_WithTemplateSlots_2<DefineComponent<AccordionContentProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<AccordionContentProps> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface AccordionContentProps extends CollapsibleContentProps {
}

export declare const AccordionHeader: __VLS_WithTemplateSlots_3<DefineComponent<AccordionHeaderProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<AccordionHeaderProps> & Readonly<{}>, {
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface AccordionHeaderProps extends PrimitiveProps {
}

export declare const AccordionItem: __VLS_WithTemplateSlots_4<DefineComponent<AccordionItemProps, {
open: ComputedRef<boolean>;
dataDisabled: ComputedRef<"" | undefined>;
}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<AccordionItemProps> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, Readonly<{
    default?: (props: {
        /** Current open state */
        open: boolean;
    }) => any;
}> & {
    default?: (props: {
        /** Current open state */
        open: boolean;
    }) => any;
}>;

declare interface AccordionItemContext {
    open: ComputedRef<boolean>;
    dataState: ComputedRef<AccordionItemState>;
    disabled: ComputedRef<boolean>;
    dataDisabled: ComputedRef<'' | undefined>;
    triggerId: string;
    currentRef: VNodeRef;
    currentElement: ComputedRef<HTMLElement | undefined>;
    value: ComputedRef<string>;
}

export declare interface AccordionItemProps extends Omit<CollapsibleRootProps, 'open' | 'defaultOpen' | 'onOpenChange'> {
    /**
     * Whether or not an accordion item is disabled from user interaction.
     * When `true`, prevents the user from interacting with the item.
     *
     * @defaultValue false
     */
    disabled?: boolean;
    /**
     * A string value for the accordion item. All items within an accordion should use a unique value.
     */
    value: string;
}

declare enum AccordionItemState {
    Open = "open",
    Closed = "closed"
}

export declare const AccordionRoot: <T extends (string | string[]), ExplicitType extends SingleOrMultipleType>(__VLS_props: NonNullable<Awaited<typeof __VLS_setup>>["props"], __VLS_ctx?: __VLS_PrettifyLocal<Pick<NonNullable<Awaited<typeof __VLS_setup>>, "attrs" | "emit" | "slots">>, __VLS_expose?: NonNullable<Awaited<typeof __VLS_setup>>["expose"], __VLS_setup?: Promise<{
    props: __VLS_PrettifyLocal<Pick<Partial<{}> & Omit<{
        readonly "onUpdate:modelValue"?: ((value: (ExplicitType extends "single" ? string : string[]) | undefined) => any) | undefined;
    } & VNodeProps & AllowedComponentProps & ComponentCustomProps, never>, "onUpdate:modelValue"> & AccordionRootProps<T> & Partial<{}>> & PublicProps;
    expose(exposed: ShallowUnwrapRef<    {}>): void;
    attrs: any;
    slots: Readonly<{
        default?: (props: {
            /** Current active value */
            modelValue: AcceptableValue | AcceptableValue[] | undefined;
        }) => any;
    }> & {
        default?: (props: {
            /** Current active value */
            modelValue: AcceptableValue | AcceptableValue[] | undefined;
        }) => any;
    };
    emit: (evt: "update:modelValue", value: (ExplicitType extends "single" ? string : string[]) | undefined) => void;
}>) => VNode & {
    __ctx?: Awaited<typeof __VLS_setup>;
};

declare type AccordionRootContext<P extends AccordionRootProps> = {
    disabled: Ref<P['disabled']>;
    direction: Ref<P['dir']>;
    orientation: P['orientation'];
    parentElement: Ref<HTMLElement | undefined>;
    changeModelValue: (value: string) => void;
    isSingle: ComputedRef<boolean>;
    modelValue: Ref<AcceptableValue | AcceptableValue[] | undefined>;
    collapsible: boolean;
    unmountOnHide: Ref<boolean>;
};

export declare type AccordionRootEmits<T extends SingleOrMultipleType = SingleOrMultipleType> = {
    /**
     * Event handler called when the expanded state of an item changes
     */
    'update:modelValue': [value: (T extends 'single' ? string : string[]) | undefined];
};

export declare interface AccordionRootProps<T = string | string[]> extends PrimitiveProps, SingleOrMultipleProps<T> {
    /**
     * When type is "single", allows closing content when clicking trigger for an open item.
     * When type is "multiple", this prop has no effect.
     *
     * @defaultValue false
     */
    collapsible?: boolean;
    /**
     * When `true`, prevents the user from interacting with the accordion and all its items
     *
     * @defaultValue false
     */
    disabled?: boolean;
    /**
     * The reading direction of the accordion when applicable. If omitted, assumes LTR (left-to-right) reading mode.
     *
     * @defaultValue "ltr"
     */
    dir?: Direction;
    /**
     * The orientation of the accordion.
     *
     * @defaultValue "vertical"
     */
    orientation?: DataOrientation;
    /**
     * When `true`, the element will be unmounted on closed state.
     *
     * @defaultValue `true`
     */
    unmountOnHide?: boolean;
}

export declare const AccordionTrigger: __VLS_WithTemplateSlots_5<DefineComponent<AccordionTriggerProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<AccordionTriggerProps> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface AccordionTriggerProps extends PrimitiveProps {
}

declare type ActivationMode = 'focus' | 'dblclick' | 'none';

export declare const AlertDialogAction: __VLS_WithTemplateSlots_6<DefineComponent<AlertDialogActionProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<AlertDialogActionProps> & Readonly<{}>, {
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface AlertDialogActionProps extends DialogCloseProps {
}

export declare const AlertDialogCancel: __VLS_WithTemplateSlots_7<DefineComponent<AlertDialogCancelProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<AlertDialogCancelProps> & Readonly<{}>, {
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface AlertDialogCancelProps extends DialogCloseProps {
}

export declare const AlertDialogContent: __VLS_WithTemplateSlots_8<DefineComponent<AlertDialogContentProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {
escapeKeyDown: (event: KeyboardEvent) => any;
pointerDownOutside: (event: PointerDownOutsideEvent) => any;
focusOutside: (event: FocusOutsideEvent) => any;
interactOutside: (event: PointerDownOutsideEvent | FocusOutsideEvent) => any;
openAutoFocus: (event: Event) => any;
closeAutoFocus: (event: Event) => any;
}, string, PublicProps, Readonly<AlertDialogContentProps> & Readonly<{
onEscapeKeyDown?: ((event: KeyboardEvent) => any) | undefined;
onPointerDownOutside?: ((event: PointerDownOutsideEvent) => any) | undefined;
onFocusOutside?: ((event: FocusOutsideEvent) => any) | undefined;
onInteractOutside?: ((event: PointerDownOutsideEvent | FocusOutsideEvent) => any) | undefined;
onOpenAutoFocus?: ((event: Event) => any) | undefined;
onCloseAutoFocus?: ((event: Event) => any) | undefined;
}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

declare interface AlertDialogContentContext {
    onCancelElementChange: (el: HTMLElement | undefined) => void;
}

export declare type AlertDialogContentEmits = DialogContentEmits;

export declare interface AlertDialogContentProps extends DialogContentProps {
}

export declare const AlertDialogDescription: __VLS_WithTemplateSlots_9<DefineComponent<AlertDialogDescriptionProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<AlertDialogDescriptionProps> & Readonly<{}>, {
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface AlertDialogDescriptionProps extends DialogDescriptionProps {
}

export declare type AlertDialogEmits = DialogRootEmits;

export declare const AlertDialogOverlay: __VLS_WithTemplateSlots_10<DefineComponent<AlertDialogOverlayProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<AlertDialogOverlayProps> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface AlertDialogOverlayProps extends DialogOverlayProps {
}

export declare const AlertDialogPortal: __VLS_WithTemplateSlots_11<DefineComponent<AlertDialogPortalProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<AlertDialogPortalProps> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface AlertDialogPortalProps extends TeleportProps {
}

export declare interface AlertDialogProps extends Omit<DialogRootProps, 'modal'> {
}

export declare const AlertDialogRoot: __VLS_WithTemplateSlots_12<DefineComponent<AlertDialogProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {
"update:open": (value: boolean) => any;
}, string, PublicProps, Readonly<AlertDialogProps> & Readonly<{
"onUpdate:open"?: ((value: boolean) => any) | undefined;
}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {
        open: boolean;
        close: () => void;
    }): any;
}>;

export declare const AlertDialogTitle: __VLS_WithTemplateSlots_13<DefineComponent<AlertDialogTitleProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<AlertDialogTitleProps> & Readonly<{}>, {
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface AlertDialogTitleProps extends DialogTitleProps {
}

export declare const AlertDialogTrigger: __VLS_WithTemplateSlots_14<DefineComponent<AlertDialogTriggerProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<AlertDialogTriggerProps> & Readonly<{}>, {
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface AlertDialogTriggerProps extends DialogTriggerProps {
}

declare type Align = (typeof ALIGN_OPTIONS)[number];

declare const ALIGN_OPTIONS: readonly ["start", "center", "end"];

declare interface ArrowProps extends PrimitiveProps {
    /**
     * The width of the arrow in pixels.
     *
     * @defaultValue 10
     */
    width?: number;
    /**
     * The height of the arrow in pixels.
     *
     * @defaultValue 5
     */
    height?: number;
    /**
     * When `true`, render the rounded version of arrow. Do not work with `as`/`asChild`
     *
     * @defaultValue false
     */
    rounded?: boolean;
}

export declare const AspectRatio: __VLS_WithTemplateSlots_15<DefineComponent<AspectRatioProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<AspectRatioProps> & Readonly<{}>, {
ratio: number;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, Readonly<{
    default?: (props: {
        /** Current aspect ratio (in %) */
        aspect: number;
    }) => any;
}> & {
    default?: (props: {
        /** Current aspect ratio (in %) */
        aspect: number;
    }) => any;
}>;

export declare interface AspectRatioProps extends PrimitiveProps {
    /**
     * The desired ratio. Eg: 16/9
     * @defaultValue 1
     */
    ratio?: number;
}

export declare type AsTag = 'a' | 'button' | 'div' | 'form' | 'h2' | 'h3' | 'img' | 'input' | 'label' | 'li' | 'nav' | 'ol' | 'p' | 'span' | 'svg' | 'ul' | 'template' | ({} & string);

export declare const AvatarFallback: __VLS_WithTemplateSlots_16<DefineComponent<AvatarFallbackProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<AvatarFallbackProps> & Readonly<{}>, {
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface AvatarFallbackProps extends PrimitiveProps {
    /** Useful for delaying rendering so it only appears for those with slower connections. */
    delayMs?: number;
}

export declare const AvatarImage: __VLS_WithTemplateSlots_17<DefineComponent<AvatarImageProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {
loadingStatusChange: (value: ImageLoadingStatus) => any;
}, string, PublicProps, Readonly<AvatarImageProps> & Readonly<{
onLoadingStatusChange?: ((value: ImageLoadingStatus) => any) | undefined;
}>, {
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare type AvatarImageEmits = {
    /**
     * A callback providing information about the loading status of the image. <br>
     * This is useful in case you want to control more precisely what to render as the image is loading.
     */
    loadingStatusChange: [value: ImageLoadingStatus];
};

export declare interface AvatarImageProps extends PrimitiveProps {
    src: string;
    referrerPolicy?: ImgHTMLAttributes['referrerpolicy'];
    crossOrigin?: ImgHTMLAttributes['crossorigin'];
}

export declare const AvatarRoot: __VLS_WithTemplateSlots_18<DefineComponent<AvatarRootProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<AvatarRootProps> & Readonly<{}>, {
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

declare type AvatarRootContext = {
    imageLoadingStatus: Ref<ImageLoadingStatus>;
};

export declare interface AvatarRootProps extends PrimitiveProps {
}

declare interface BaseSeparatorProps extends PrimitiveProps {
    /**
     * Orientation of the component.
     *
     * Either `vertical` or `horizontal`. Defaults to `horizontal`.
     */
    orientation?: DataOrientation;
    /**
     * Whether or not the component is purely decorative. <br>When `true`, accessibility-related attributes
     * are updated so that that the rendered element is removed from the accessibility tree.
     */
    decorative?: boolean;
}

export declare const CalendarCell: __VLS_WithTemplateSlots_19<DefineComponent<CalendarCellProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<CalendarCellProps> & Readonly<{}>, {
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface CalendarCellProps extends PrimitiveProps {
    /** The date value for the cell */
    date: DateValue;
}

export declare const CalendarCellTrigger: __VLS_WithTemplateSlots_20<DefineComponent<CalendarCellTriggerProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<CalendarCellTriggerProps> & Readonly<{}>, {
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {
primitiveElement: CreateComponentPublicInstanceWithMixins<Readonly<ExtractPropTypes<    {
asChild: {
type: BooleanConstructor;
default: boolean;
};
as: {
type: PropType<AsTag | Component>;
default: string;
};
}>> & Readonly<{}>, () => VNode<RendererNode, RendererElement, {
[key: string]: any;
}>, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, PublicProps, {
asChild: boolean;
as: AsTag | Component;
}, true, {}, {}, GlobalComponents, GlobalDirectives, string, {}, any, ComponentProvideOptions, {
P: {};
B: {};
D: {};
C: {};
M: {};
Defaults: {};
}, Readonly<ExtractPropTypes<    {
asChild: {
type: BooleanConstructor;
default: boolean;
};
as: {
type: PropType<AsTag | Component>;
default: string;
};
}>> & Readonly<{}>, () => VNode<RendererNode, RendererElement, {
[key: string]: any;
}>, {}, {}, {}, {
asChild: boolean;
as: AsTag | Component;
}> | null;
}, any>, Readonly<CalendarCellTriggerSlot> & CalendarCellTriggerSlot>;

export declare interface CalendarCellTriggerProps extends PrimitiveProps {
    /** The date value provided to the cell trigger */
    day: DateValue;
    /** The month in which the cell is rendered */
    month: DateValue;
}

declare interface CalendarCellTriggerSlot {
    default?: (props: {
        /** Current day */
        dayValue: string;
        /** Current disable state */
        disabled: boolean;
        /** Current selected state */
        selected: boolean;
        /** Current today state */
        today: boolean;
        /** Current outside view state */
        outsideView: boolean;
        /** Current outside visible view state */
        outsideVisibleView: boolean;
        /** Current unavailable state */
        unavailable: boolean;
    }) => any;
}

export declare const CalendarGrid: __VLS_WithTemplateSlots_21<DefineComponent<CalendarGridProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<CalendarGridProps> & Readonly<{}>, {
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare const CalendarGridBody: __VLS_WithTemplateSlots_22<DefineComponent<CalendarGridBodyProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<CalendarGridBodyProps> & Readonly<{}>, {
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface CalendarGridBodyProps extends PrimitiveProps {
}

export declare const CalendarGridHead: __VLS_WithTemplateSlots_23<DefineComponent<CalendarGridHeadProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<CalendarGridHeadProps> & Readonly<{}>, {
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface CalendarGridHeadProps extends PrimitiveProps {
}

export declare interface CalendarGridProps extends PrimitiveProps {
}

export declare const CalendarGridRow: __VLS_WithTemplateSlots_24<DefineComponent<CalendarGridRowProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<CalendarGridRowProps> & Readonly<{}>, {
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface CalendarGridRowProps extends PrimitiveProps {
}

export declare const CalendarHeadCell: __VLS_WithTemplateSlots_25<DefineComponent<CalendarHeadCellProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<CalendarHeadCellProps> & Readonly<{}>, {
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface CalendarHeadCellProps extends PrimitiveProps {
}

export declare const CalendarHeader: __VLS_WithTemplateSlots_26<DefineComponent<CalendarHeaderProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<CalendarHeaderProps> & Readonly<{}>, {
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface CalendarHeaderProps extends PrimitiveProps {
}

export declare const CalendarHeading: __VLS_WithTemplateSlots_27<DefineComponent<CalendarHeadingProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<CalendarHeadingProps> & Readonly<{}>, {
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, Readonly<{
    default?: (props: {
        /** Current month and year */
        headingValue: string;
    }) => any;
}> & {
    default?: (props: {
        /** Current month and year */
        headingValue: string;
    }) => any;
}>;

export declare interface CalendarHeadingProps extends PrimitiveProps {
}

export declare const CalendarNext: __VLS_WithTemplateSlots_28<DefineComponent<CalendarNextProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<CalendarNextProps> & Readonly<{}>, {
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, Readonly<CalendarNextSlot> & CalendarNextSlot>;

export declare interface CalendarNextProps extends PrimitiveProps {
    /** The function to be used for the next page. Overwrites the `nextPage` function set on the `CalendarRoot`. */
    nextPage?: (placeholder: DateValue) => DateValue;
}

declare interface CalendarNextSlot {
    default?: (props: {
        /** Current disable state */
        disabled: boolean;
    }) => any;
}

export declare const CalendarPrev: __VLS_WithTemplateSlots_29<DefineComponent<CalendarPrevProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<CalendarPrevProps> & Readonly<{}>, {
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, Readonly<CalendarPrevSlot> & CalendarPrevSlot>;

export declare interface CalendarPrevProps extends PrimitiveProps {
    /** The function to be used for the prev page. Overwrites the `prevPage` function set on the `CalendarRoot`. */
    prevPage?: (placeholder: DateValue) => DateValue;
}

declare interface CalendarPrevSlot {
    default?: (props: {
        /** Current disable state */
        disabled: boolean;
    }) => any;
}

export declare const CalendarRoot: __VLS_WithTemplateSlots_30<DefineComponent<CalendarRootProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {
"update:modelValue": (date: DateValue | undefined) => any;
"update:placeholder": (date: DateValue) => any;
}, string, PublicProps, Readonly<CalendarRootProps> & Readonly<{
"onUpdate:modelValue"?: ((date: DateValue | undefined) => any) | undefined;
"onUpdate:placeholder"?: ((date: DateValue) => any) | undefined;
}>, {
defaultValue: DateValue;
weekStartsOn: 0 | 1 | 2 | 3 | 4 | 5 | 6;
fixedWeeks: boolean;
numberOfMonths: number;
pagedNavigation: boolean;
placeholder: DateValue;
disabled: boolean;
multiple: boolean;
as: AsTag | Component;
preventDeselect: boolean;
weekdayFormat: WeekDayFormat;
readonly: boolean;
initialFocus: boolean;
isDateDisabled: Matcher;
isDateUnavailable: Matcher;
disableDaysOutsideCurrentView: boolean;
}, {}, {}, {}, string, ComponentProvideOptions, false, {
primitiveElement: CreateComponentPublicInstanceWithMixins<Readonly<ExtractPropTypes<    {
asChild: {
type: BooleanConstructor;
default: boolean;
};
as: {
type: PropType<AsTag | Component>;
default: string;
};
}>> & Readonly<{}>, () => VNode<RendererNode, RendererElement, {
[key: string]: any;
}>, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, PublicProps, {
asChild: boolean;
as: AsTag | Component;
}, true, {}, {}, GlobalComponents, GlobalDirectives, string, {}, any, ComponentProvideOptions, {
P: {};
B: {};
D: {};
C: {};
M: {};
Defaults: {};
}, Readonly<ExtractPropTypes<    {
asChild: {
type: BooleanConstructor;
default: boolean;
};
as: {
type: PropType<AsTag | Component>;
default: string;
};
}>> & Readonly<{}>, () => VNode<RendererNode, RendererElement, {
[key: string]: any;
}>, {}, {}, {}, {
asChild: boolean;
as: AsTag | Component;
}> | null;
}, any>, Readonly<{
    default?: (props: {
        /** The current date of the placeholder */
        date: DateValue;
        /** The grid of dates */
        grid: Grid<DateValue>[];
        /** The days of the week */
        weekDays: string[];
        /** The start of the week */
        weekStartsOn: 0 | 1 | 2 | 3 | 4 | 5 | 6;
        /** The calendar locale */
        locale: string;
        /** Whether or not to always display 6 weeks in the calendar */
        fixedWeeks: boolean;
        /** The current date of the calendar */
        modelValue: DateValue | DateValue[] | undefined;
    }) => any;
}> & {
    default?: (props: {
        /** The current date of the placeholder */
        date: DateValue;
        /** The grid of dates */
        grid: Grid<DateValue>[];
        /** The days of the week */
        weekDays: string[];
        /** The start of the week */
        weekStartsOn: 0 | 1 | 2 | 3 | 4 | 5 | 6;
        /** The calendar locale */
        locale: string;
        /** Whether or not to always display 6 weeks in the calendar */
        fixedWeeks: boolean;
        /** The current date of the calendar */
        modelValue: DateValue | DateValue[] | undefined;
    }) => any;
}>;

declare type CalendarRootContext = {
    locale: Ref<string>;
    modelValue: Ref<DateValue | DateValue[] | undefined>;
    placeholder: Ref<DateValue>;
    pagedNavigation: Ref<boolean>;
    preventDeselect: Ref<boolean>;
    grid: Ref<Grid<DateValue>[]>;
    weekDays: Ref<string[]>;
    weekStartsOn: Ref<0 | 1 | 2 | 3 | 4 | 5 | 6>;
    weekdayFormat: Ref<WeekDayFormat>;
    fixedWeeks: Ref<boolean>;
    multiple: Ref<boolean>;
    numberOfMonths: Ref<number>;
    disabled: Ref<boolean>;
    readonly: Ref<boolean>;
    initialFocus: Ref<boolean>;
    onDateChange: (date: DateValue) => void;
    onPlaceholderChange: (date: DateValue) => void;
    fullCalendarLabel: Ref<string>;
    parentElement: Ref<HTMLElement | undefined>;
    headingValue: Ref<string>;
    isInvalid: Ref<boolean>;
    isDateDisabled: Matcher;
    isDateSelected: Matcher;
    isDateUnavailable?: Matcher;
    isOutsideVisibleView: (date: DateValue) => boolean;
    prevPage: (prevPageFunc?: (date: DateValue) => DateValue) => void;
    nextPage: (nextPageFunc?: (date: DateValue) => DateValue) => void;
    isNextButtonDisabled: (nextPageFunc?: (date: DateValue) => DateValue) => boolean;
    isPrevButtonDisabled: (prevPageFunc?: (date: DateValue) => DateValue) => boolean;
    formatter: Formatter;
    dir: Ref<Direction>;
    disableDaysOutsideCurrentView: Ref<boolean>;
};

export declare type CalendarRootEmits = {
    /** Event handler called whenever the model value changes */
    'update:modelValue': [date: DateValue | undefined];
    /** Event handler called whenever the placeholder value changes */
    'update:placeholder': [date: DateValue];
};

export declare interface CalendarRootProps extends PrimitiveProps {
    /** The default value for the calendar */
    defaultValue?: DateValue;
    /** The default placeholder date */
    defaultPlaceholder?: DateValue;
    /** The placeholder date, which is used to determine what month to display when no date is selected */
    placeholder?: DateValue;
    /** This property causes the previous and next buttons to navigate by the number of months displayed at once, rather than one month */
    pagedNavigation?: boolean;
    /** Whether or not to prevent the user from deselecting a date without selecting another date first */
    preventDeselect?: boolean;
    /** The day of the week to start the calendar on */
    weekStartsOn?: 0 | 1 | 2 | 3 | 4 | 5 | 6;
    /** The format to use for the weekday strings provided via the weekdays slot prop */
    weekdayFormat?: WeekDayFormat;
    /** The accessible label for the calendar */
    calendarLabel?: string;
    /** Whether or not to always display 6 weeks in the calendar */
    fixedWeeks?: boolean;
    /** The maximum date that can be selected */
    maxValue?: DateValue;
    /** The minimum date that can be selected */
    minValue?: DateValue;
    /** The locale to use for formatting dates */
    locale?: string;
    /** The number of months to display at once */
    numberOfMonths?: number;
    /** Whether the calendar is disabled */
    disabled?: boolean;
    /** Whether the calendar is readonly */
    readonly?: boolean;
    /** If true, the calendar will focus the selected day, today, or the first day of the month depending on what is visible when the calendar is mounted */
    initialFocus?: boolean;
    /** A function that returns whether or not a date is disabled */
    isDateDisabled?: Matcher;
    /** A function that returns whether or not a date is unavailable */
    isDateUnavailable?: Matcher;
    /** The reading direction of the calendar when applicable. <br> If omitted, inherits globally from `ConfigProvider` or assumes LTR (left-to-right) reading mode. */
    dir?: Direction;
    /** A function that returns the next page of the calendar. It receives the current placeholder as an argument inside the component. */
    nextPage?: (placeholder: DateValue) => DateValue;
    /** A function that returns the previous page of the calendar. It receives the current placeholder as an argument inside the component. */
    prevPage?: (placeholder: DateValue) => DateValue;
    /** The controlled checked state of the calendar */
    modelValue?: DateValue | DateValue[] | undefined;
    /** Whether multiple dates can be selected */
    multiple?: boolean;
    /** Whether or not to disable days outside the current view. */
    disableDaysOutsideCurrentView?: boolean;
}

export declare type CheckboxCheckedState = boolean | 'indeterminate';

export declare const CheckboxGroupRoot: <T extends AcceptableValue = AcceptableValue>(__VLS_props: NonNullable<Awaited<typeof __VLS_setup>>["props"], __VLS_ctx?: __VLS_PrettifyLocal_2<Pick<NonNullable<Awaited<typeof __VLS_setup>>, "attrs" | "emit" | "slots">>, __VLS_expose?: NonNullable<Awaited<typeof __VLS_setup>>["expose"], __VLS_setup?: Promise<{
    props: __VLS_PrettifyLocal_2<Pick<Partial<{}> & Omit<{
        readonly "onUpdate:modelValue"?: ((value: T[]) => any) | undefined;
    } & VNodeProps & AllowedComponentProps & ComponentCustomProps, never>, "onUpdate:modelValue"> & CheckboxGroupRootProps<T> & Partial<{}>> & PublicProps;
    expose(exposed: ShallowUnwrapRef<    {}>): void;
    attrs: any;
    slots: {
        default?(_: {}): any;
    };
    emit: (evt: "update:modelValue", value: T[]) => void;
}>) => VNode & {
    __ctx?: Awaited<typeof __VLS_setup>;
};

declare interface CheckboxGroupRootContext {
    modelValue: Ref<AcceptableValue[]>;
    rovingFocus: Ref<boolean>;
    disabled: Ref<boolean>;
}

export declare type CheckboxGroupRootEmits<T = AcceptableValue> = {
    /** Event handler called when the value of the checkbox changes. */
    'update:modelValue': [value: T[]];
};

export declare interface CheckboxGroupRootProps<T = AcceptableValue> extends Pick<RovingFocusGroupProps, 'as' | 'asChild' | 'dir' | 'orientation' | 'loop'>, FormFieldProps {
    /** The value of the checkbox when it is initially rendered. Use when you do not need to control its value. */
    defaultValue?: T[];
    /** The controlled value of the checkbox. Can be binded with v-model. */
    modelValue?: T[];
    /** When `false`, navigating through the items using arrow keys will be disabled. */
    rovingFocus?: boolean;
    /** When `true`, prevents the user from interacting with the checkboxes */
    disabled?: boolean;
}

export declare const CheckboxIndicator: __VLS_WithTemplateSlots_31<DefineComponent<CheckboxIndicatorProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<CheckboxIndicatorProps> & Readonly<{}>, {
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface CheckboxIndicatorProps extends PrimitiveProps {
    /**
     * Used to force mounting when more control is needed. Useful when
     * controlling animation with Vue animation libraries.
     */
    forceMount?: boolean;
}

export declare const CheckboxRoot: __VLS_WithTemplateSlots_32<DefineComponent<CheckboxRootProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {
"update:modelValue": (value: boolean | "indeterminate") => any;
}, string, PublicProps, Readonly<CheckboxRootProps> & Readonly<{
"onUpdate:modelValue"?: ((value: boolean | "indeterminate") => any) | undefined;
}>, {
value: AcceptableValue;
as: AsTag | Component;
modelValue: boolean | "indeterminate" | null;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, Readonly<{
    default?: (props: {
        /** Current value */
        modelValue: CheckboxCheckedState;
        /** Current state */
        state: CheckboxCheckedState;
    }) => any;
}> & {
    default?: (props: {
        /** Current value */
        modelValue: CheckboxCheckedState;
        /** Current state */
        state: CheckboxCheckedState;
    }) => any;
}>;

declare interface CheckboxRootContext {
    disabled: Ref<boolean>;
    state: Ref<CheckboxCheckedState>;
}

export declare type CheckboxRootEmits = {
    /** Event handler called when the value of the checkbox changes. */
    'update:modelValue': [value: boolean | 'indeterminate'];
};

export declare interface CheckboxRootProps extends PrimitiveProps, FormFieldProps {
    /** The value of the checkbox when it is initially rendered. Use when you do not need to control its value. */
    defaultValue?: boolean | 'indeterminate';
    /** The controlled value of the checkbox. Can be binded with v-model. */
    modelValue?: boolean | 'indeterminate' | null;
    /** When `true`, prevents the user from interacting with the checkbox */
    disabled?: boolean;
    /**
     * The value given as data when submitted with a `name`.
     *  @defaultValue "on"
     */
    value?: AcceptableValue;
    /** Id of the element */
    id?: string;
}

declare type CheckedState = boolean | 'indeterminate';

export declare const CollapsibleContent: __VLS_WithTemplateSlots_33<DefineComponent<CollapsibleContentProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {
contentFound: (args_0: void) => any;
}, string, PublicProps, Readonly<CollapsibleContentProps> & Readonly<{
onContentFound?: ((args_0?: void | undefined) => any) | undefined;
}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {
presentRef: CreateComponentPublicInstanceWithMixins<Readonly<ExtractPropTypes<    {
present: {
type: BooleanConstructor;
required: true;
};
forceMount: {
type: BooleanConstructor;
};
}>> & Readonly<{}>, () => VNode<RendererNode, RendererElement, {
[key: string]: any;
}> | null, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, PublicProps, {
forceMount: boolean;
}, true, {}, SlotsType<    {
default: (opts: {
present: boolean;
}) => any;
}>, GlobalComponents, GlobalDirectives, string, {}, any, ComponentProvideOptions, {
P: {};
B: {};
D: {};
C: {};
M: {};
Defaults: {};
}, Readonly<ExtractPropTypes<    {
present: {
type: BooleanConstructor;
required: true;
};
forceMount: {
type: BooleanConstructor;
};
}>> & Readonly<{}>, () => VNode<RendererNode, RendererElement, {
[key: string]: any;
}> | null, {}, {}, {}, {
forceMount: boolean;
}> | null;
}, any>, {
    default?(_: {}): any;
}>;

export declare type CollapsibleContentEmits = {
    contentFound: [void];
};

export declare interface CollapsibleContentProps extends PrimitiveProps {
    /**
     * Used to force mounting when more control is needed. Useful when
     * controlling animation with Vue animation libraries.
     */
    forceMount?: boolean;
}

export declare const CollapsibleRoot: __VLS_WithTemplateSlots_34<DefineComponent<CollapsibleRootProps, {
open: Ref<boolean, boolean>;
}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {
"update:open": (value: boolean) => any;
}, string, PublicProps, Readonly<CollapsibleRootProps> & Readonly<{
"onUpdate:open"?: ((value: boolean) => any) | undefined;
}>, {
defaultOpen: boolean;
open: boolean;
unmountOnHide: boolean;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, Readonly<{
    default?: (props: {
        /** Current open state */
        open: boolean;
    }) => any;
}> & {
    default?: (props: {
        /** Current open state */
        open: boolean;
    }) => any;
}>;

declare interface CollapsibleRootContext {
    contentId: string;
    disabled?: Ref<boolean>;
    open: Ref<boolean>;
    unmountOnHide: Ref<boolean>;
    onOpenToggle: () => void;
}

export declare type CollapsibleRootEmits = {
    /** Event handler called when the open state of the collapsible changes. */
    'update:open': [value: boolean];
};

export declare interface CollapsibleRootProps extends PrimitiveProps {
    /** The open state of the collapsible when it is initially rendered. <br> Use when you do not need to control its open state. */
    defaultOpen?: boolean;
    /** The controlled open state of the collapsible. Can be binded with `v-model`. */
    open?: boolean;
    /** When `true`, prevents the user from interacting with the collapsible. */
    disabled?: boolean;
    /** When `true`, the element will be unmounted on closed state. */
    unmountOnHide?: boolean;
}

export declare const CollapsibleTrigger: __VLS_WithTemplateSlots_35<DefineComponent<CollapsibleTriggerProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<CollapsibleTriggerProps> & Readonly<{}>, {
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface CollapsibleTriggerProps extends PrimitiveProps {
}

export declare const ComboboxAnchor: __VLS_WithTemplateSlots_36<DefineComponent<ComboboxAnchorProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<ComboboxAnchorProps> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface ComboboxAnchorProps extends PopperAnchorProps {
}

export declare const ComboboxArrow: __VLS_WithTemplateSlots_37<DefineComponent<ComboboxArrowProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<ComboboxArrowProps> & Readonly<{}>, {
width: number;
height: number;
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface ComboboxArrowProps extends PopperArrowProps {
}

export declare const ComboboxCancel: __VLS_WithTemplateSlots_38<DefineComponent<ComboboxCancelProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<ComboboxCancelProps> & Readonly<{}>, {
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface ComboboxCancelProps extends PrimitiveProps {
}

export declare const ComboboxContent: __VLS_WithTemplateSlots_39<DefineComponent<ComboboxContentProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {
escapeKeyDown: (event: KeyboardEvent) => any;
pointerDownOutside: (event: PointerDownOutsideEvent) => any;
focusOutside: (event: FocusOutsideEvent) => any;
interactOutside: (event: PointerDownOutsideEvent | FocusOutsideEvent) => any;
}, string, PublicProps, Readonly<ComboboxContentProps> & Readonly<{
onEscapeKeyDown?: ((event: KeyboardEvent) => any) | undefined;
onPointerDownOutside?: ((event: PointerDownOutsideEvent) => any) | undefined;
onFocusOutside?: ((event: FocusOutsideEvent) => any) | undefined;
onInteractOutside?: ((event: PointerDownOutsideEvent | FocusOutsideEvent) => any) | undefined;
}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare type ComboboxContentEmits = ComboboxContentImplEmits;

declare type ComboboxContentImplEmits = DismissableLayerEmits;

declare interface ComboboxContentImplProps extends PopperContentProps, DismissableLayerProps {
    /**
     * The positioning mode to use, <br>
     * `inline` is the default and you can control the position using CSS. <br>
     * `popper` positions content in the same way as our other primitives, for example `Popover` or `DropdownMenu`.
     */
    position?: 'inline' | 'popper';
    /** The document.body will be lock, and scrolling will be disabled. */
    bodyLock?: boolean;
}

export declare interface ComboboxContentProps extends ComboboxContentImplProps {
    /**
     * Used to force mounting when more control is needed. Useful when
     * controlling animation with Vue animation libraries.
     */
    forceMount?: boolean;
}

export declare const ComboboxEmpty: __VLS_WithTemplateSlots_40<DefineComponent<ComboboxEmptyProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<ComboboxEmptyProps> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface ComboboxEmptyProps extends PrimitiveProps {
}

export declare const ComboboxGroup: __VLS_WithTemplateSlots_41<DefineComponent<ComboboxGroupProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<ComboboxGroupProps> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

declare type ComboboxGroupContext = {
    id: string;
    labelId: string;
};

export declare interface ComboboxGroupProps extends ListboxGroupProps {
}

export declare const ComboboxInput: __VLS_WithTemplateSlots_42<DefineComponent<ComboboxInputProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {
"update:modelValue": (args_0: string) => any;
}, string, PublicProps, Readonly<ComboboxInputProps> & Readonly<{
"onUpdate:modelValue"?: ((args_0: string) => any) | undefined;
}>, {
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {
primitiveElement: ({
$: ComponentInternalInstance;
$data: {};
$props: {
readonly modelValue?: string | undefined;
readonly autoFocus?: boolean | undefined;
readonly disabled?: boolean | undefined;
readonly asChild?: boolean | undefined;
readonly as?: (AsTag | Component) | undefined;
readonly "onUpdate:modelValue"?: ((args_0: string) => any) | undefined;
} & VNodeProps & AllowedComponentProps & ComponentCustomProps;
$attrs: {
[x: string]: unknown;
};
$refs: {
[x: string]: unknown;
} & {
primitiveElement: CreateComponentPublicInstanceWithMixins<Readonly<ExtractPropTypes<    {
asChild: {
type: BooleanConstructor;
default: boolean;
};
as: {
type: PropType<AsTag | Component>;
default: string;
};
}>> & Readonly<{}>, () => VNode<RendererNode, RendererElement, {
[key: string]: any;
}>, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, PublicProps, {
asChild: boolean;
as: AsTag | Component;
}, true, {}, {}, GlobalComponents, GlobalDirectives, string, {}, any, ComponentProvideOptions, {
P: {};
B: {};
D: {};
C: {};
M: {};
Defaults: {};
}, Readonly<ExtractPropTypes<    {
asChild: {
type: BooleanConstructor;
default: boolean;
};
as: {
type: PropType<AsTag | Component>;
default: string;
};
}>> & Readonly<{}>, () => VNode<RendererNode, RendererElement, {
[key: string]: any;
}>, {}, {}, {}, {
asChild: boolean;
as: AsTag | Component;
}> | null;
};
$slots: Readonly<{
[name: string]: Slot_2<any> | undefined;
}>;
$root: ComponentPublicInstance | null;
$parent: ComponentPublicInstance | null;
$host: Element | null;
$emit: (event: "update:modelValue", args_0: string) => void;
$el: any;
$options: ComponentOptionsBase<Readonly<ListboxFilterProps> & Readonly<{
"onUpdate:modelValue"?: ((args_0: string) => any) | undefined;
}>, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {
"update:modelValue": (args_0: string) => any;
}, string, {
as: AsTag | Component;
}, {}, string, {}, GlobalComponents, GlobalDirectives, string, ComponentProvideOptions> & {
beforeCreate?: (() => void) | (() => void)[];
created?: (() => void) | (() => void)[];
beforeMount?: (() => void) | (() => void)[];
mounted?: (() => void) | (() => void)[];
beforeUpdate?: (() => void) | (() => void)[];
updated?: (() => void) | (() => void)[];
activated?: (() => void) | (() => void)[];
deactivated?: (() => void) | (() => void)[];
beforeDestroy?: (() => void) | (() => void)[];
beforeUnmount?: (() => void) | (() => void)[];
destroyed?: (() => void) | (() => void)[];
unmounted?: (() => void) | (() => void)[];
renderTracked?: ((e: DebuggerEvent) => void) | ((e: DebuggerEvent) => void)[];
renderTriggered?: ((e: DebuggerEvent) => void) | ((e: DebuggerEvent) => void)[];
errorCaptured?: ((err: unknown, instance: ComponentPublicInstance | null, info: string) => boolean | void) | ((err: unknown, instance: ComponentPublicInstance | null, info: string) => boolean | void)[];
};
$forceUpdate: () => void;
$nextTick: typeof nextTick;
$watch<T extends string | ((...args: any) => any)>(source: T, cb: T extends (...args: any) => infer R ? (...args: [R, R, OnCleanup]) => any : (...args: [any, any, OnCleanup]) => any, options?: WatchOptions): WatchStopHandle;
} & Readonly<{
as: AsTag | Component;
}> & Omit<Readonly<ListboxFilterProps> & Readonly<{
"onUpdate:modelValue"?: ((args_0: string) => any) | undefined;
}>, "as"> & ShallowUnwrapRef<    {}> & {} & ComponentCustomProperties & {} & {
$slots: Readonly<{
default?: (props: {
modelValue: string | undefined;
}) => any;
}> & {
default?: (props: {
modelValue: string | undefined;
}) => any;
};
}) | null;
}, any>, {
    default?(_: {}): any;
}>;

export declare type ComboboxInputEmits = ListboxFilterEmits;

export declare interface ComboboxInputProps extends ListboxFilterProps {
    /** The display value of input for selected item. Does not work with `multiple`. */
    displayValue?: (val: any) => string;
}

export declare const ComboboxItem: <T extends AcceptableValue = AcceptableValue>(__VLS_props: NonNullable<Awaited<typeof __VLS_setup>>["props"], __VLS_ctx?: __VLS_PrettifyLocal_3<Pick<NonNullable<Awaited<typeof __VLS_setup>>, "attrs" | "emit" | "slots">>, __VLS_expose?: NonNullable<Awaited<typeof __VLS_setup>>["expose"], __VLS_setup?: Promise<{
    props: __VLS_PrettifyLocal_3<Pick<Partial<{}> & Omit<{
        readonly onSelect?: ((event: ListboxItemSelectEvent<T>) => any) | undefined;
    } & VNodeProps & AllowedComponentProps & ComponentCustomProps, never>, "onSelect"> & ComboboxItemProps<T> & Partial<{}>> & PublicProps;
    expose(exposed: ShallowUnwrapRef<    {}>): void;
    attrs: any;
    slots: {
        default?(_: {}): any;
    };
    emit: (evt: "select", event: ListboxItemSelectEvent<T>) => void;
}>) => VNode & {
    __ctx?: Awaited<typeof __VLS_setup>;
};

export declare type ComboboxItemEmits<T = AcceptableValue> = ListboxItemEmits<T>;

export declare const ComboboxItemIndicator: __VLS_WithTemplateSlots_43<DefineComponent<ComboboxItemIndicatorProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<ComboboxItemIndicatorProps> & Readonly<{}>, {
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface ComboboxItemIndicatorProps extends ListboxItemIndicatorProps {
}

export declare interface ComboboxItemProps<T = AcceptableValue> extends ListboxItemProps<T> {
    /**
     * A string representation of the item contents.
     *
     * If the children are not plain text, then the `textValue` prop must also be set to a plain text representation, which will be used for autocomplete in the ComboBox.
     */
    textValue?: string;
}

export declare const ComboboxLabel: __VLS_WithTemplateSlots_44<DefineComponent<ComboboxLabelProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<ComboboxLabelProps> & Readonly<{}>, {
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface ComboboxLabelProps extends PrimitiveProps {
    for?: string;
}

export declare const ComboboxPortal: __VLS_WithTemplateSlots_45<DefineComponent<ComboboxPortalProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<ComboboxPortalProps> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface ComboboxPortalProps extends TeleportProps {
}

export declare const ComboboxRoot: <T extends AcceptableValue = AcceptableValue>(__VLS_props: NonNullable<Awaited<typeof __VLS_setup>>["props"], __VLS_ctx?: __VLS_PrettifyLocal_4<Pick<NonNullable<Awaited<typeof __VLS_setup>>, "attrs" | "emit" | "slots">>, __VLS_expose?: NonNullable<Awaited<typeof __VLS_setup>>["expose"], __VLS_setup?: Promise<{
    props: __VLS_PrettifyLocal_4<Pick<Partial<{}> & Omit<{
        readonly "onUpdate:open"?: ((value: boolean) => any) | undefined;
        readonly "onUpdate:modelValue"?: ((value: T) => any) | undefined;
        readonly onHighlight?: ((payload: {
            ref: HTMLElement;
            value: T;
        } | undefined) => any) | undefined;
    } & VNodeProps & AllowedComponentProps & ComponentCustomProps, never>, "onUpdate:open" | "onUpdate:modelValue" | "onHighlight"> & ComboboxRootProps<T> & Partial<{}>> & PublicProps;
    expose(exposed: ShallowUnwrapRef<    {
    filtered: ComputedRef<    {
    count: number;
    items: Map<string, number> & Omit<Map<string, number>, keyof Map<any, any>>;
    groups: Set<string> & Omit<Set<string>, keyof Set<any>>;
    }>;
    highlightedElement: ComputedRef<HTMLElement | undefined>;
    highlightItem: ((value: AcceptableValue) => void) | undefined;
    highlightFirstItem: (() => void) | undefined;
    highlightSelected: ((event?: Event) => Promise<void>) | undefined;
    }>): void;
    attrs: any;
    slots: Readonly<{
        default?: (props: {
            /** Current open state */
            open: boolean;
            /** Current active value */
            modelValue: T | T[];
        }) => any;
    }> & {
        default?: (props: {
            /** Current open state */
            open: boolean;
            /** Current active value */
            modelValue: T | T[];
        }) => any;
    };
    emit: ((evt: "update:open", value: boolean) => void) & ((evt: "update:modelValue", value: T) => void) & ((evt: "highlight", payload: {
        ref: HTMLElement;
        value: T;
    } | undefined) => void);
}>) => VNode & {
    __ctx?: Awaited<typeof __VLS_setup>;
};

declare type ComboboxRootContext<T> = {
    modelValue: Ref<T | Array<T>>;
    multiple: Ref<boolean>;
    disabled: Ref<boolean>;
    open: Ref<boolean>;
    onOpenChange: (value: boolean) => void;
    isUserInputted: Ref<boolean>;
    isVirtual: Ref<boolean>;
    contentId: string;
    inputElement: Ref<HTMLInputElement | undefined>;
    onInputElementChange: (el: HTMLInputElement) => void;
    triggerElement: Ref<HTMLElement | undefined>;
    onTriggerElementChange: (el: HTMLElement) => void;
    highlightedElement: Ref<HTMLElement | undefined>;
    parentElement: Ref<HTMLElement | undefined>;
    resetSearchTermOnSelect: Ref<boolean>;
    onResetSearchTerm: EventHookOn;
    allItems: Ref<Map<string, string>>;
    allGroups: Ref<Map<string, Set<string>>>;
    filterState: {
        search: string;
        filtered: {
            count: number;
            items: Map<string, number>;
            groups: Set<string>;
        };
    };
    ignoreFilter: Ref<boolean>;
};

export declare type ComboboxRootEmits<T = AcceptableValue> = {
    /** Event handler called when the value changes. */
    'update:modelValue': [value: T];
    /** Event handler when highlighted element changes. */
    'highlight': [payload: {
        ref: HTMLElement;
        value: T;
    } | undefined];
    /** Event handler called when the open state of the combobox changes. */
    'update:open': [value: boolean];
};

export declare interface ComboboxRootProps<T = AcceptableValue> extends Omit<ListboxRootProps<T>, 'orientation' | 'selectionBehavior'> {
    /** The controlled open state of the Combobox. Can be binded with with `v-model:open`. */
    open?: boolean;
    /** The open state of the combobox when it is initially rendered. <br> Use when you do not need to control its open state. */
    defaultOpen?: boolean;
    /**
     * Whether to reset the searchTerm when the Combobox input blurred
     * @defaultValue `true`
     */
    resetSearchTermOnBlur?: boolean;
    /**
     * Whether to reset the searchTerm when the Combobox value is selected
     * @defaultValue `true`
     */
    resetSearchTermOnSelect?: boolean;
    /**
     * When `true`, disable the default filters
     */
    ignoreFilter?: boolean;
}

export declare const ComboboxSeparator: __VLS_WithTemplateSlots_46<DefineComponent<ComboboxSeparatorProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<ComboboxSeparatorProps> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface ComboboxSeparatorProps extends PrimitiveProps {
}

export declare const ComboboxTrigger: __VLS_WithTemplateSlots_47<DefineComponent<ComboboxTriggerProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<ComboboxTriggerProps> & Readonly<{}>, {
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface ComboboxTriggerProps extends PrimitiveProps {
    /** When `true`, prevents the user from interacting with item */
    disabled?: boolean;
}

export declare const ComboboxViewport: __VLS_WithTemplateSlots_48<DefineComponent<ComboboxViewportProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<ComboboxViewportProps> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface ComboboxViewportProps extends PrimitiveProps {
    /**
     * Will add `nonce` attribute to the style tag which can be used by Content Security Policy. <br> If omitted, inherits globally from `ConfigProvider`.
     */
    nonce?: string;
}

export declare const ComboboxVirtualizer: <T extends AcceptableValue = AcceptableValue>(__VLS_props: NonNullable<Awaited<typeof __VLS_setup>>["props"], __VLS_ctx?: __VLS_PrettifyLocal_5<Pick<NonNullable<Awaited<typeof __VLS_setup>>, "attrs" | "emit" | "slots">>, __VLS_expose?: NonNullable<Awaited<typeof __VLS_setup>>["expose"], __VLS_setup?: Promise<{
    props: __VLS_PrettifyLocal_5<Pick<Partial<{}> & Omit<{} & VNodeProps & AllowedComponentProps & ComponentCustomProps, never>, never> & ComboboxVirtualizerProps<T> & Partial<{}>> & PublicProps;
    expose(exposed: ShallowUnwrapRef<    {}>): void;
    attrs: any;
    slots: Readonly<{
        default?: (props: {
            option: T;
            virtualizer: Virtualizer<HTMLElement, Element>;
            virtualItem: VirtualItem;
        }) => any;
    }> & {
        default?: (props: {
            option: T;
            virtualizer: Virtualizer<HTMLElement, Element>;
            virtualItem: VirtualItem;
        }) => any;
    };
    emit: {};
}>) => VNode & {
    __ctx?: Awaited<typeof __VLS_setup>;
};

export declare interface ComboboxVirtualizerProps<T extends AcceptableValue = AcceptableValue> extends ListboxVirtualizerProps<T> {
}

export declare const ConfigProvider: __VLS_WithTemplateSlots_49<DefineComponent<ConfigProviderProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<ConfigProviderProps> & Readonly<{}>, {
locale: string;
dir: Direction;
scrollBody: boolean | ScrollBodyOption;
nonce: string;
useId: () => string;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

declare interface ConfigProviderContextValue {
    dir?: Ref<Direction>;
    locale?: Ref<string>;
    scrollBody?: Ref<boolean | ScrollBodyOption>;
    nonce?: Ref<string | undefined>;
    useId?: () => string;
}

export declare interface ConfigProviderProps {
    /**
     * The global reading direction of your application. This will be inherited by all primitives.
     * @defaultValue 'ltr'
     */
    dir?: Direction;
    /**
     * The global locale of your application. This will be inherited by all primitives.
     * @defaultValue 'en'
     */
    locale?: string;
    /**
     * The global scroll body behavior of your application. This will be inherited by the related primitives.
     * @type boolean | ScrollBodyOption
     */
    scrollBody?: boolean | ScrollBodyOption;
    /**
     * The global `nonce` value of your application. This will be inherited by the related primitives.
     * @type string
     */
    nonce?: string;
    /**
     * The global `useId` injection as a workaround for preventing hydration issue.
     */
    useId?: () => string;
}

export declare const ContextMenuArrow: __VLS_WithTemplateSlots_50<DefineComponent<ContextMenuArrowProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<ContextMenuArrowProps> & Readonly<{}>, {
width: number;
height: number;
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface ContextMenuArrowProps extends MenuArrowProps {
}

export declare const ContextMenuCheckboxItem: __VLS_WithTemplateSlots_51<DefineComponent<ContextMenuCheckboxItemProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {
select: (event: Event) => any;
"update:modelValue": (payload: boolean) => any;
}, string, PublicProps, Readonly<ContextMenuCheckboxItemProps> & Readonly<{
onSelect?: ((event: Event) => any) | undefined;
"onUpdate:modelValue"?: ((payload: boolean) => any) | undefined;
}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare type ContextMenuCheckboxItemEmits = MenuCheckboxItemEmits;

export declare interface ContextMenuCheckboxItemProps extends MenuCheckboxItemProps {
}

export declare const ContextMenuContent: __VLS_WithTemplateSlots_52<DefineComponent<ContextMenuContentProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {
escapeKeyDown: (event: KeyboardEvent) => any;
pointerDownOutside: (event: PointerDownOutsideEvent) => any;
focusOutside: (event: FocusOutsideEvent) => any;
interactOutside: (event: PointerDownOutsideEvent | FocusOutsideEvent) => any;
closeAutoFocus: (event: Event) => any;
}, string, PublicProps, Readonly<ContextMenuContentProps> & Readonly<{
onEscapeKeyDown?: ((event: KeyboardEvent) => any) | undefined;
onPointerDownOutside?: ((event: PointerDownOutsideEvent) => any) | undefined;
onFocusOutside?: ((event: FocusOutsideEvent) => any) | undefined;
onInteractOutside?: ((event: PointerDownOutsideEvent | FocusOutsideEvent) => any) | undefined;
onCloseAutoFocus?: ((event: Event) => any) | undefined;
}>, {
alignOffset: number;
avoidCollisions: boolean;
collisionBoundary: Element | null | Array<Element | null>;
collisionPadding: number | Partial<Record<Side, number>>;
sticky: "partial" | "always";
hideWhenDetached: boolean;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare type ContextMenuContentEmits = MenuContentEmits;

export declare interface ContextMenuContentProps extends Omit<MenuContentProps, 'side' | 'sideOffset' | 'align' | 'arrowPadding' | 'updatePositionStrategy'> {
}

export declare const ContextMenuGroup: __VLS_WithTemplateSlots_53<DefineComponent<ContextMenuGroupProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<ContextMenuGroupProps> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface ContextMenuGroupProps extends MenuGroupProps {
}

export declare const ContextMenuItem: __VLS_WithTemplateSlots_54<DefineComponent<MenuItemProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {
select: (event: Event) => any;
}, string, PublicProps, Readonly<MenuItemProps> & Readonly<{
onSelect?: ((event: Event) => any) | undefined;
}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare type ContextMenuItemEmits = MenuItemEmits;

export declare const ContextMenuItemIndicator: __VLS_WithTemplateSlots_55<DefineComponent<ContextMenuItemIndicatorProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<ContextMenuItemIndicatorProps> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface ContextMenuItemIndicatorProps extends MenuItemIndicatorProps {
}

export declare interface ContextMenuItemProps extends MenuItemProps {
}

export declare const ContextMenuLabel: __VLS_WithTemplateSlots_56<DefineComponent<ContextMenuLabelProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<ContextMenuLabelProps> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface ContextMenuLabelProps extends MenuLabelProps {
}

export declare const ContextMenuPortal: __VLS_WithTemplateSlots_57<typeof __VLS_component, __VLS_TemplateResult["slots"]>;

export declare interface ContextMenuPortalProps extends MenuPortalProps {
}

export declare const ContextMenuRadioGroup: __VLS_WithTemplateSlots_58<DefineComponent<ContextMenuRadioGroupProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {
"update:modelValue": (payload: string) => any;
}, string, PublicProps, Readonly<ContextMenuRadioGroupProps> & Readonly<{
"onUpdate:modelValue"?: ((payload: string) => any) | undefined;
}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare type ContextMenuRadioGroupEmits = MenuRadioGroupEmits;

export declare interface ContextMenuRadioGroupProps extends MenuRadioGroupProps {
}

export declare const ContextMenuRadioItem: __VLS_WithTemplateSlots_59<DefineComponent<ContextMenuRadioItemProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {
select: (event: Event) => any;
}, string, PublicProps, Readonly<ContextMenuRadioItemProps> & Readonly<{
onSelect?: ((event: Event) => any) | undefined;
}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare type ContextMenuRadioItemEmits = MenuItemEmits;

export declare interface ContextMenuRadioItemProps extends MenuRadioItemProps {
}

export declare const ContextMenuRoot: __VLS_WithTemplateSlots_60<DefineComponent<ContextMenuRootProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {
"update:open": (payload: boolean) => any;
}, string, PublicProps, Readonly<ContextMenuRootProps> & Readonly<{
"onUpdate:open"?: ((payload: boolean) => any) | undefined;
}>, {
modal: boolean;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

declare type ContextMenuRootContext = {
    open: Ref<boolean>;
    onOpenChange: (open: boolean) => void;
    modal: Ref<boolean>;
    dir: Ref<Direction>;
    triggerElement: Ref<HTMLElement | undefined>;
};

export declare type ContextMenuRootEmits = MenuEmits;

export declare interface ContextMenuRootProps extends Omit<MenuProps, 'open'> {
}

export declare const ContextMenuSeparator: __VLS_WithTemplateSlots_61<DefineComponent<ContextMenuSeparatorProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<ContextMenuSeparatorProps> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface ContextMenuSeparatorProps extends MenuSeparatorProps {
}

export declare const ContextMenuSub: __VLS_WithTemplateSlots_62<DefineComponent<ContextMenuSubProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {
"update:open": (payload: boolean) => any;
}, string, PublicProps, Readonly<ContextMenuSubProps> & Readonly<{
"onUpdate:open"?: ((payload: boolean) => any) | undefined;
}>, {
open: boolean;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, Readonly<{
    default?: (props: {
        /** Current open state */
        open: boolean;
    }) => any;
}> & {
    default?: (props: {
        /** Current open state */
        open: boolean;
    }) => any;
}>;

export declare const ContextMenuSubContent: __VLS_WithTemplateSlots_63<DefineComponent<ContextMenuSubContentProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {
escapeKeyDown: (event: KeyboardEvent) => any;
pointerDownOutside: (event: PointerDownOutsideEvent) => any;
focusOutside: (event: FocusOutsideEvent) => any;
interactOutside: (event: PointerDownOutsideEvent | FocusOutsideEvent) => any;
openAutoFocus: (event: Event) => any;
closeAutoFocus: (event: Event) => any;
entryFocus: (event: Event) => any;
}, string, PublicProps, Readonly<ContextMenuSubContentProps> & Readonly<{
onEscapeKeyDown?: ((event: KeyboardEvent) => any) | undefined;
onPointerDownOutside?: ((event: PointerDownOutsideEvent) => any) | undefined;
onFocusOutside?: ((event: FocusOutsideEvent) => any) | undefined;
onInteractOutside?: ((event: PointerDownOutsideEvent | FocusOutsideEvent) => any) | undefined;
onOpenAutoFocus?: ((event: Event) => any) | undefined;
onCloseAutoFocus?: ((event: Event) => any) | undefined;
onEntryFocus?: ((event: Event) => any) | undefined;
}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare type ContextMenuSubContentEmits = MenuSubContentEmits;

export declare interface ContextMenuSubContentProps extends MenuSubContentProps {
}

export declare type ContextMenuSubEmits = MenuSubEmits;

export declare interface ContextMenuSubProps extends MenuSubProps {
    /** The open state of the submenu when it is initially rendered. Use when you do not need to control its open state. */
    defaultOpen?: boolean;
}

export declare const ContextMenuSubTrigger: __VLS_WithTemplateSlots_64<DefineComponent<ContextMenuSubTriggerProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<ContextMenuSubTriggerProps> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface ContextMenuSubTriggerProps extends MenuSubTriggerProps {
}

export declare const ContextMenuTrigger: __VLS_WithTemplateSlots_65<DefineComponent<ContextMenuTriggerProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<ContextMenuTriggerProps> & Readonly<{}>, {
disabled: boolean;
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface ContextMenuTriggerProps extends PrimitiveProps {
    /**
     * When `true`, the context menu would not open when right-clicking.
     *
     * Note that this will also restore the native context menu.
     */
    disabled?: boolean;
}

/**
 * @param providerComponentName - The name(s) of the component(s) providing the context.
 *
 * There are situations where context can come from multiple components. In such cases, you might need to give an array of component names to provide your context, instead of just a single string.
 *
 * @param contextName The description for injection key symbol.
 */
export declare function createContext<ContextValue>(providerComponentName: string | string[], contextName?: string): readonly [<T extends ContextValue | null | undefined = ContextValue>(fallback?: T) => T extends null ? ContextValue | null : ContextValue, (contextValue: ContextValue) => ContextValue];

declare type DataOrientation = 'vertical' | 'horizontal';

declare type DataState = 'on' | 'off';

declare const DATE_SEGMENT_PARTS: readonly ["day", "month", "year"];

declare type DateAndTimeSegmentObj = DateSegmentObj & TimeSegmentObj;

export declare const DateFieldInput: __VLS_WithTemplateSlots_66<DefineComponent<DateFieldInputProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<DateFieldInputProps> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface DateFieldInputProps extends PrimitiveProps {
    /** The part of the date to render */
    part: SegmentPart;
}

export declare const DateFieldRoot: __VLS_WithTemplateSlots_67<DefineComponent<DateFieldRootProps, {
/** Helper to set the focused element inside the DateField */
setFocusedElement: (el: HTMLElement) => void;
}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {
"update:modelValue": (date: DateValue | undefined) => any;
"update:placeholder": (date: DateValue) => any;
}, string, PublicProps, Readonly<DateFieldRootProps> & Readonly<{
"onUpdate:modelValue"?: ((date: DateValue | undefined) => any) | undefined;
"onUpdate:placeholder"?: ((date: DateValue) => any) | undefined;
}>, {
defaultValue: DateValue;
placeholder: DateValue;
disabled: boolean;
readonly: boolean;
isDateUnavailable: Matcher;
}, {}, {}, {}, string, ComponentProvideOptions, false, {
primitiveElement: CreateComponentPublicInstanceWithMixins<Readonly<ExtractPropTypes<    {
asChild: {
type: BooleanConstructor;
default: boolean;
};
as: {
type: PropType<AsTag | Component>;
default: string;
};
}>> & Readonly<{}>, () => VNode<RendererNode, RendererElement, {
[key: string]: any;
}>, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, PublicProps, {
asChild: boolean;
as: AsTag | Component;
}, true, {}, {}, GlobalComponents, GlobalDirectives, string, {}, any, ComponentProvideOptions, {
P: {};
B: {};
D: {};
C: {};
M: {};
Defaults: {};
}, Readonly<ExtractPropTypes<    {
asChild: {
type: BooleanConstructor;
default: boolean;
};
as: {
type: PropType<AsTag | Component>;
default: string;
};
}>> & Readonly<{}>, () => VNode<RendererNode, RendererElement, {
[key: string]: any;
}>, {}, {}, {}, {
asChild: boolean;
as: AsTag | Component;
}> | null;
}, any>, Readonly<{
    default?: (props: {
        /** The current date of the field */
        modelValue: DateValue | undefined;
        /** The date field segment contents */
        segments: {
            part: SegmentPart;
            value: string;
        }[];
        /** Value if the input is invalid */
        isInvalid: boolean;
    }) => any;
}> & {
    default?: (props: {
        /** The current date of the field */
        modelValue: DateValue | undefined;
        /** The date field segment contents */
        segments: {
            part: SegmentPart;
            value: string;
        }[];
        /** Value if the input is invalid */
        isInvalid: boolean;
    }) => any;
}>;

declare type DateFieldRootContext = {
    locale: Ref<string>;
    modelValue: Ref<DateValue | undefined>;
    placeholder: Ref<DateValue>;
    isDateUnavailable?: Matcher;
    isInvalid: Ref<boolean>;
    disabled: Ref<boolean>;
    readonly: Ref<boolean>;
    formatter: Formatter;
    hourCycle: HourCycle;
    step: Ref<DateStep>;
    segmentValues: Ref<SegmentValueObj>;
    segmentContents: Ref<{
        part: SegmentPart;
        value: string;
    }[]>;
    elements: Ref<Set<HTMLElement>>;
    focusNext: () => void;
    setFocusedElement: (el: HTMLElement) => void;
};

export declare type DateFieldRootEmits = {
    /** Event handler called whenever the model value changes */
    'update:modelValue': [date: DateValue | undefined];
    /** Event handler called whenever the placeholder value changes */
    'update:placeholder': [date: DateValue];
};

export declare interface DateFieldRootProps extends PrimitiveProps, FormFieldProps {
    /** The default value for the calendar */
    defaultValue?: DateValue;
    /** The default placeholder date */
    defaultPlaceholder?: DateValue;
    /** The placeholder date, which is used to determine what month to display when no date is selected. This updates as the user navigates the calendar and can be used to programmatically control the calendar view */
    placeholder?: DateValue;
    /** The controlled checked state of the calendar. Can be bound as `v-model`. */
    modelValue?: DateValue | null;
    /** The hour cycle used for formatting times. Defaults to the local preference */
    hourCycle?: HourCycle;
    /** The stepping interval for the time fields. Defaults to `1`. */
    step?: DateStep;
    /** The granularity to use for formatting times. Defaults to day if a CalendarDate is provided, otherwise defaults to minute. The field will render segments for each part of the date up to and including the specified granularity */
    granularity?: Granularity;
    /** Whether or not to hide the time zone segment of the field */
    hideTimeZone?: boolean;
    /** The maximum date that can be selected */
    maxValue?: DateValue;
    /** The minimum date that can be selected */
    minValue?: DateValue;
    /** The locale to use for formatting dates */
    locale?: string;
    /** Whether or not the date field is disabled */
    disabled?: boolean;
    /** Whether or not the date field is readonly */
    readonly?: boolean;
    /** A function that returns whether or not a date is unavailable */
    isDateUnavailable?: Matcher;
    /** Id of the element */
    id?: string;
    /** The reading direction of the date field when applicable. <br> If omitted, inherits globally from `ConfigProvider` or assumes LTR (left-to-right) reading mode. */
    dir?: Direction;
}

declare interface DateFormatterOptions extends Intl.DateTimeFormatOptions {
    calendar?: string;
}

export declare const DatePickerAnchor: __VLS_WithTemplateSlots_68<DefineComponent<DatePickerAnchorProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<DatePickerAnchorProps> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

declare interface DatePickerAnchorProps extends PopoverAnchorProps {
}

export declare const DatePickerArrow: __VLS_WithTemplateSlots_69<DefineComponent<DatePickerArrowProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<DatePickerArrowProps> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

declare interface DatePickerArrowProps extends PopoverArrowProps {
}

export declare const DatePickerCalendar: __VLS_WithTemplateSlots_70<DefineComponent<    {}, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<{}> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, true, {}, any>, {
    default?(_: {
        date: DateValue;
        grid: Grid<DateValue>[];
        weekDays: string[];
        weekStartsOn: 0 | 1 | 2 | 6 | 3 | 4 | 5;
        locale: string;
        fixedWeeks: boolean;
    }): any;
}>;

export declare const DatePickerCell: __VLS_WithTemplateSlots_71<DefineComponent<DatePickerCellProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<DatePickerCellProps> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface DatePickerCellProps extends CalendarCellProps {
}

export declare const DatePickerCellTrigger: __VLS_WithTemplateSlots_72<DefineComponent<DatePickerCellTriggerProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<DatePickerCellTriggerProps> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, Readonly<CalendarCellTriggerSlot> & CalendarCellTriggerSlot>;

export declare interface DatePickerCellTriggerProps extends CalendarCellTriggerProps {
}

export declare const DatePickerClose: __VLS_WithTemplateSlots_73<DefineComponent<DatePickerCloseProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<DatePickerCloseProps> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

declare interface DatePickerCloseProps extends PopoverCloseProps {
}

export declare const DatePickerContent: __VLS_WithTemplateSlots_74<DefineComponent<DatePickerContentProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {
escapeKeyDown: (event: KeyboardEvent) => any;
pointerDownOutside: (event: PointerDownOutsideEvent) => any;
focusOutside: (event: FocusOutsideEvent) => any;
interactOutside: (event: PointerDownOutsideEvent | FocusOutsideEvent) => any;
openAutoFocus: (event: Event) => any;
closeAutoFocus: (event: Event) => any;
}, string, PublicProps, Readonly<DatePickerContentProps> & Readonly<{
onEscapeKeyDown?: ((event: KeyboardEvent) => any) | undefined;
onPointerDownOutside?: ((event: PointerDownOutsideEvent) => any) | undefined;
onFocusOutside?: ((event: FocusOutsideEvent) => any) | undefined;
onInteractOutside?: ((event: PointerDownOutsideEvent | FocusOutsideEvent) => any) | undefined;
onOpenAutoFocus?: ((event: Event) => any) | undefined;
onCloseAutoFocus?: ((event: Event) => any) | undefined;
}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface DatePickerContentProps extends PopoverContentProps {
    /**
     * Props to control the portal wrapped around the content.
     */
    portal?: PopoverPortalProps;
}

export declare const DatePickerField: __VLS_WithTemplateSlots_75<DefineComponent<    {}, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<{}> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, true, {}, any>, {
    default?(_: {
        segments: {
            part: SegmentPart;
            value: string;
        }[];
        modelValue: DateValue | undefined;
    }): any;
}>;

export declare const DatePickerGrid: __VLS_WithTemplateSlots_76<DefineComponent<DatePickerGridProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<DatePickerGridProps> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare const DatePickerGridBody: __VLS_WithTemplateSlots_77<DefineComponent<DatePickerGridBodyProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<DatePickerGridBodyProps> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface DatePickerGridBodyProps extends CalendarGridBodyProps {
}

export declare const DatePickerGridHead: __VLS_WithTemplateSlots_78<DefineComponent<DatePickerGridHeadProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<DatePickerGridHeadProps> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface DatePickerGridHeadProps extends CalendarGridHeadProps {
}

export declare interface DatePickerGridProps extends CalendarGridProps {
}

export declare const DatePickerGridRow: __VLS_WithTemplateSlots_79<DefineComponent<DatePickerGridRowProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<DatePickerGridRowProps> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface DatePickerGridRowProps extends CalendarGridRowProps {
}

export declare const DatePickerHeadCell: __VLS_WithTemplateSlots_80<DefineComponent<DatePickerHeadCellProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<DatePickerHeadCellProps> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface DatePickerHeadCellProps extends CalendarHeadCellProps {
}

export declare const DatePickerHeader: __VLS_WithTemplateSlots_81<DefineComponent<DatePickerHeaderProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<DatePickerHeaderProps> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface DatePickerHeaderProps extends CalendarHeaderProps {
}

export declare const DatePickerHeading: __VLS_WithTemplateSlots_82<DefineComponent<DatePickerHeadingProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<DatePickerHeadingProps> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, Readonly<{
    default?: (props: {
        /** Current month and year */
        headingValue: string;
    }) => any;
}> & {
    default?: (props: {
        /** Current month and year */
        headingValue: string;
    }) => any;
}>;

export declare interface DatePickerHeadingProps extends CalendarHeadingProps {
}

export declare const DatePickerInput: __VLS_WithTemplateSlots_83<DefineComponent<DatePickerInputProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<DatePickerInputProps> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface DatePickerInputProps extends DateFieldInputProps {
}

export declare const DatePickerNext: __VLS_WithTemplateSlots_84<DefineComponent<DatePickerNextProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<DatePickerNextProps> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, Readonly<CalendarNextSlot> & CalendarNextSlot>;

export declare interface DatePickerNextProps extends CalendarNextProps {
}

export declare const DatePickerPrev: __VLS_WithTemplateSlots_85<DefineComponent<DatePickerPrevProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<DatePickerPrevProps> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, Readonly<CalendarPrevSlot> & CalendarPrevSlot>;

export declare interface DatePickerPrevProps extends CalendarPrevProps {
}

export declare const DatePickerRoot: __VLS_WithTemplateSlots_86<DefineComponent<DatePickerRootProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {
"update:open": (value: boolean) => any;
"update:modelValue": (date: DateValue | undefined) => any;
"update:placeholder": (date: DateValue) => any;
}, string, PublicProps, Readonly<DatePickerRootProps> & Readonly<{
"onUpdate:open"?: ((value: boolean) => any) | undefined;
"onUpdate:modelValue"?: ((date: DateValue | undefined) => any) | undefined;
"onUpdate:placeholder"?: ((date: DateValue) => any) | undefined;
}>, {
defaultValue: DateValue;
locale: string;
weekStartsOn: 0 | 1 | 2 | 3 | 4 | 5 | 6;
fixedWeeks: boolean;
numberOfMonths: number;
pagedNavigation: boolean;
placeholder: DateValue;
disabled: boolean;
defaultOpen: boolean;
open: boolean;
modal: boolean;
preventDeselect: boolean;
weekdayFormat: WeekDayFormat;
readonly: boolean;
isDateDisabled: Matcher;
isDateUnavailable: Matcher;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

declare type DatePickerRootContext = {
    id: Ref<string | undefined>;
    name: Ref<string | undefined>;
    minValue: Ref<DateValue | undefined>;
    maxValue: Ref<DateValue | undefined>;
    hourCycle: Ref<HourCycle | undefined>;
    granularity: Ref<Granularity | undefined>;
    hideTimeZone: Ref<boolean>;
    required: Ref<boolean>;
    locale: Ref<string>;
    dateFieldRef: Ref<InstanceType<typeof DateFieldRoot> | undefined>;
    modelValue: Ref<DateValue | undefined>;
    placeholder: Ref<DateValue>;
    pagedNavigation: Ref<boolean>;
    preventDeselect: Ref<boolean>;
    weekStartsOn: Ref<0 | 1 | 2 | 3 | 4 | 5 | 6>;
    weekdayFormat: Ref<WeekDayFormat>;
    fixedWeeks: Ref<boolean>;
    numberOfMonths: Ref<number>;
    disabled: Ref<boolean>;
    readonly: Ref<boolean>;
    isDateDisabled?: Matcher;
    isDateUnavailable?: Matcher;
    defaultOpen: Ref<boolean>;
    open: Ref<boolean>;
    modal: Ref<boolean>;
    onDateChange: (date: DateValue | undefined) => void;
    onPlaceholderChange: (date: DateValue) => void;
    dir: Ref<Direction>;
};

export declare type DatePickerRootEmits = {
    /** Event handler called whenever the model value changes */
    'update:modelValue': [date: DateValue | undefined];
    /** Event handler called whenever the placeholder value changes */
    'update:placeholder': [date: DateValue];
};

export declare type DatePickerRootProps = DateFieldRootProps & PopoverRootProps & Pick<CalendarRootProps, 'isDateDisabled' | 'pagedNavigation' | 'weekStartsOn' | 'weekdayFormat' | 'fixedWeeks' | 'numberOfMonths' | 'preventDeselect'>;

export declare const DatePickerTrigger: __VLS_WithTemplateSlots_87<DefineComponent<DatePickerTriggerProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<DatePickerTriggerProps> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface DatePickerTriggerProps extends PopoverTriggerProps {
}

export declare type DateRange = {
    start: DateValue | undefined;
    end: DateValue | undefined;
};

export declare const DateRangeFieldInput: __VLS_WithTemplateSlots_88<DefineComponent<DateRangeFieldInputProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<DateRangeFieldInputProps> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface DateRangeFieldInputProps extends PrimitiveProps {
    /** The part of the date to render */
    part: SegmentPart;
    /** The type of field to render (start or end) */
    type: DateRangeType;
}

export declare const DateRangeFieldRoot: __VLS_WithTemplateSlots_89<DefineComponent<DateRangeFieldRootProps, {
setFocusedElement: (el: HTMLElement) => void;
}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {
"update:modelValue": (args_0: DateRange) => any;
"update:placeholder": (date: DateValue) => any;
}, string, PublicProps, Readonly<DateRangeFieldRootProps> & Readonly<{
"onUpdate:modelValue"?: ((args_0: DateRange) => any) | undefined;
"onUpdate:placeholder"?: ((date: DateValue) => any) | undefined;
}>, {
defaultValue: DateRange;
placeholder: DateValue;
disabled: boolean;
readonly: boolean;
isDateUnavailable: Matcher;
}, {}, {}, {}, string, ComponentProvideOptions, false, {
primitiveElement: CreateComponentPublicInstanceWithMixins<Readonly<ExtractPropTypes<    {
asChild: {
type: BooleanConstructor;
default: boolean;
};
as: {
type: PropType<AsTag | Component>;
default: string;
};
}>> & Readonly<{}>, () => VNode<RendererNode, RendererElement, {
[key: string]: any;
}>, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, PublicProps, {
asChild: boolean;
as: AsTag | Component;
}, true, {}, {}, GlobalComponents, GlobalDirectives, string, {}, any, ComponentProvideOptions, {
P: {};
B: {};
D: {};
C: {};
M: {};
Defaults: {};
}, Readonly<ExtractPropTypes<    {
asChild: {
type: BooleanConstructor;
default: boolean;
};
as: {
type: PropType<AsTag | Component>;
default: string;
};
}>> & Readonly<{}>, () => VNode<RendererNode, RendererElement, {
[key: string]: any;
}>, {}, {}, {}, {
asChild: boolean;
as: AsTag | Component;
}> | null;
}, any>, {
    default?(_: {
        modelValue: DateRange | null;
        segments: {
            start: {
                part: SegmentPart;
                value: string;
            }[];
            end: {
                part: SegmentPart;
                value: string;
            }[];
        };
    }): any;
}>;

declare type DateRangeFieldRootContext = {
    locale: Ref<string>;
    startValue: Ref<DateValue | undefined>;
    endValue: Ref<DateValue | undefined>;
    placeholder: Ref<DateValue>;
    isDateUnavailable?: Matcher;
    isInvalid: Ref<boolean>;
    disabled: Ref<boolean>;
    readonly: Ref<boolean>;
    formatter: Formatter;
    hourCycle: HourCycle;
    step: Ref<DateStep>;
    segmentValues: Record<DateRangeType, Ref<SegmentValueObj>>;
    segmentContents: Ref<{
        start: {
            part: SegmentPart;
            value: string;
        }[];
        end: {
            part: SegmentPart;
            value: string;
        }[];
    }>;
    elements: Ref<Set<HTMLElement>>;
    focusNext: () => void;
    setFocusedElement: (el: HTMLElement) => void;
};

export declare type DateRangeFieldRootEmits = {
    /** Event handler called whenever the model value changes */
    'update:modelValue': [DateRange];
    /** Event handler called whenever the placeholder value changes */
    'update:placeholder': [date: DateValue];
};

export declare interface DateRangeFieldRootProps extends PrimitiveProps, FormFieldProps {
    /** The default value for the calendar */
    defaultValue?: DateRange;
    /** The default placeholder date */
    defaultPlaceholder?: DateValue;
    /** The placeholder date, which is used to determine what month to display when no date is selected. This updates as the user navigates the calendar and can be used to programmatically control the calendar view */
    placeholder?: DateValue;
    /** The controlled checked state of the calendar. Can be bound as `v-model`. */
    modelValue?: DateRange | null;
    /** The hour cycle used for formatting times. Defaults to the local preference */
    hourCycle?: HourCycle;
    /** The stepping interval for the time fields. Defaults to `1`. */
    step?: DateStep;
    /** The granularity to use for formatting times. Defaults to day if a CalendarDate is provided, otherwise defaults to minute. The field will render segments for each part of the date up to and including the specified granularity */
    granularity?: Granularity;
    /** Whether or not to hide the time zone segment of the field */
    hideTimeZone?: boolean;
    /** The maximum date that can be selected */
    maxValue?: DateValue;
    /** The minimum date that can be selected */
    minValue?: DateValue;
    /** The locale to use for formatting dates */
    locale?: string;
    /** Whether or not the date field is disabled */
    disabled?: boolean;
    /** Whether or not the date field is readonly */
    readonly?: boolean;
    /** A function that returns whether or not a date is unavailable */
    isDateUnavailable?: Matcher;
    /** Id of the element */
    id?: string;
    /** The reading direction of the date field when applicable. <br> If omitted, inherits globally from `ConfigProvider` or assumes LTR (left-to-right) reading mode. */
    dir?: Direction;
}

export declare const DateRangePickerAnchor: __VLS_WithTemplateSlots_90<DefineComponent<DateRangePickerAnchorProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<DateRangePickerAnchorProps> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

declare interface DateRangePickerAnchorProps extends PopoverAnchorProps {
}

export declare const DateRangePickerArrow: __VLS_WithTemplateSlots_91<DefineComponent<DateRangePickerArrowProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<DateRangePickerArrowProps> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

declare interface DateRangePickerArrowProps extends PopoverArrowProps {
}

export declare const DateRangePickerCalendar: __VLS_WithTemplateSlots_92<DefineComponent<    {}, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<{}> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, true, {}, any>, {
    default?(_: {
        date: DateValue;
        grid: Grid<DateValue>[];
        weekDays: string[];
        weekStartsOn: 0 | 1 | 2 | 6 | 3 | 4 | 5;
        locale: string;
        fixedWeeks: boolean;
    }): any;
}>;

export declare const DateRangePickerCell: __VLS_WithTemplateSlots_93<DefineComponent<DateRangePickerCellProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<DateRangePickerCellProps> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface DateRangePickerCellProps extends RangeCalendarCellProps {
}

export declare const DateRangePickerCellTrigger: __VLS_WithTemplateSlots_94<DefineComponent<DateRangePickerCellTriggerProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<DateRangePickerCellTriggerProps> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, Readonly<RangeCalendarCellTriggerSlot> & RangeCalendarCellTriggerSlot>;

export declare interface DateRangePickerCellTriggerProps extends RangeCalendarCellTriggerProps {
}

export declare const DateRangePickerClose: __VLS_WithTemplateSlots_95<DefineComponent<DateRangePickerCloseProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<DateRangePickerCloseProps> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

declare interface DateRangePickerCloseProps extends PopoverCloseProps {
}

export declare const DateRangePickerContent: __VLS_WithTemplateSlots_96<DefineComponent<DateRangePickerContentProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {
escapeKeyDown: (event: KeyboardEvent) => any;
pointerDownOutside: (event: PointerDownOutsideEvent) => any;
focusOutside: (event: FocusOutsideEvent) => any;
interactOutside: (event: PointerDownOutsideEvent | FocusOutsideEvent) => any;
openAutoFocus: (event: Event) => any;
closeAutoFocus: (event: Event) => any;
}, string, PublicProps, Readonly<DateRangePickerContentProps> & Readonly<{
onEscapeKeyDown?: ((event: KeyboardEvent) => any) | undefined;
onPointerDownOutside?: ((event: PointerDownOutsideEvent) => any) | undefined;
onFocusOutside?: ((event: FocusOutsideEvent) => any) | undefined;
onInteractOutside?: ((event: PointerDownOutsideEvent | FocusOutsideEvent) => any) | undefined;
onOpenAutoFocus?: ((event: Event) => any) | undefined;
onCloseAutoFocus?: ((event: Event) => any) | undefined;
}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface DateRangePickerContentProps extends PopoverContentProps {
    /**
     * Props to control the portal wrapped around the content.
     */
    portal?: PopoverPortalProps;
}

export declare const DateRangePickerField: __VLS_WithTemplateSlots_97<DefineComponent<    {}, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<{}> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, true, {}, any>, {
    default?(_: {
        segments: {
            start: {
                part: SegmentPart;
                value: string;
            }[];
            end: {
                part: SegmentPart;
                value: string;
            }[];
        };
        modelValue: DateRange | null;
    }): any;
}>;

export declare const DateRangePickerGrid: __VLS_WithTemplateSlots_98<DefineComponent<DateRangePickerGridProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<DateRangePickerGridProps> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare const DateRangePickerGridBody: __VLS_WithTemplateSlots_99<DefineComponent<DateRangePickerGridBodyProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<DateRangePickerGridBodyProps> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface DateRangePickerGridBodyProps extends RangeCalendarGridBodyProps {
}

export declare const DateRangePickerGridHead: __VLS_WithTemplateSlots_100<DefineComponent<DateRangePickerGridHeadProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<DateRangePickerGridHeadProps> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface DateRangePickerGridHeadProps extends RangeCalendarGridHeadProps {
}

export declare interface DateRangePickerGridProps extends RangeCalendarGridProps {
}

export declare const DateRangePickerGridRow: __VLS_WithTemplateSlots_101<DefineComponent<DateRangePickerGridRowProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<DateRangePickerGridRowProps> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface DateRangePickerGridRowProps extends RangeCalendarGridRowProps {
}

export declare const DateRangePickerHeadCell: __VLS_WithTemplateSlots_102<DefineComponent<DateRangePickerHeadCellProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<DateRangePickerHeadCellProps> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface DateRangePickerHeadCellProps extends RangeCalendarHeadCellProps {
}

export declare const DateRangePickerHeader: __VLS_WithTemplateSlots_103<DefineComponent<DateRangePickerHeaderProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<DateRangePickerHeaderProps> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface DateRangePickerHeaderProps extends RangeCalendarHeaderProps {
}

export declare const DateRangePickerHeading: __VLS_WithTemplateSlots_104<DefineComponent<DateRangePickerHeadingProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<DateRangePickerHeadingProps> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, Readonly<{
    default?: (props: {
        /** Current month and year */
        headingValue: string;
    }) => any;
}> & {
    default?: (props: {
        /** Current month and year */
        headingValue: string;
    }) => any;
}>;

export declare interface DateRangePickerHeadingProps extends RangeCalendarHeadingProps {
}

export declare const DateRangePickerInput: __VLS_WithTemplateSlots_105<DefineComponent<DateRangePickerInputProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<DateRangePickerInputProps> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface DateRangePickerInputProps extends DateRangeFieldInputProps {
}

export declare const DateRangePickerNext: __VLS_WithTemplateSlots_106<DefineComponent<DateRangePickerNextProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<DateRangePickerNextProps> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, Readonly<RangeCalendarNextSlot> & RangeCalendarNextSlot>;

export declare interface DateRangePickerNextProps extends RangeCalendarNextProps {
}

export declare const DateRangePickerPrev: __VLS_WithTemplateSlots_107<DefineComponent<DateRangePickerPrevProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<DateRangePickerPrevProps> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, Readonly<RangeCalendarPrevSlot> & RangeCalendarPrevSlot>;

export declare interface DateRangePickerPrevProps extends RangeCalendarPrevProps {
}

export declare const DateRangePickerRoot: __VLS_WithTemplateSlots_108<DefineComponent<DateRangePickerRootProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {
"update:open": (value: boolean) => any;
"update:modelValue": (date: DateRange) => any;
"update:placeholder": (date: DateValue) => any;
"update:startValue": (date: DateValue | undefined) => any;
}, string, PublicProps, Readonly<DateRangePickerRootProps> & Readonly<{
"onUpdate:open"?: ((value: boolean) => any) | undefined;
"onUpdate:modelValue"?: ((date: DateRange) => any) | undefined;
"onUpdate:placeholder"?: ((date: DateValue) => any) | undefined;
"onUpdate:startValue"?: ((date: DateValue | undefined) => any) | undefined;
}>, {
defaultValue: DateRange;
locale: string;
weekStartsOn: 0 | 1 | 2 | 3 | 4 | 5 | 6;
fixedWeeks: boolean;
numberOfMonths: number;
pagedNavigation: boolean;
placeholder: DateValue;
disabled: boolean;
defaultOpen: boolean;
open: boolean;
modal: boolean;
preventDeselect: boolean;
weekdayFormat: WeekDayFormat;
readonly: boolean;
isDateDisabled: Matcher;
isDateUnavailable: Matcher;
isDateHighlightable: Matcher;
allowNonContiguousRanges: boolean;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {
        modelValue: DateRange;
        open: boolean;
    }): any;
}>;

declare type DateRangePickerRootContext = {
    id: Ref<string | undefined>;
    name: Ref<string | undefined>;
    minValue: Ref<DateValue | undefined>;
    maxValue: Ref<DateValue | undefined>;
    hourCycle: Ref<HourCycle | undefined>;
    granularity: Ref<Granularity | undefined>;
    hideTimeZone: Ref<boolean>;
    required: Ref<boolean>;
    locale: Ref<string>;
    dateFieldRef: Ref<InstanceType<typeof DateRangeFieldRoot> | undefined>;
    modelValue: Ref<{
        start: DateValue | undefined;
        end: DateValue | undefined;
    }>;
    placeholder: Ref<DateValue>;
    pagedNavigation: Ref<boolean>;
    preventDeselect: Ref<boolean>;
    weekStartsOn: Ref<0 | 1 | 2 | 3 | 4 | 5 | 6>;
    weekdayFormat: Ref<WeekDayFormat>;
    fixedWeeks: Ref<boolean>;
    numberOfMonths: Ref<number>;
    disabled: Ref<boolean>;
    readonly: Ref<boolean>;
    isDateDisabled?: Matcher;
    isDateUnavailable?: Matcher;
    isDateHighlightable?: Matcher;
    defaultOpen: Ref<boolean>;
    open: Ref<boolean>;
    modal: Ref<boolean>;
    onDateChange: (date: DateRange) => void;
    onPlaceholderChange: (date: DateValue) => void;
    onStartValueChange: (date: DateValue | undefined) => void;
    dir: Ref<Direction>;
    allowNonContiguousRanges: Ref<boolean>;
    fixedDate: Ref<'start' | 'end' | undefined>;
};

export declare type DateRangePickerRootEmits = {
    /** Event handler called whenever the model value changes */
    'update:modelValue': [date: DateRange];
    /** Event handler called whenever the placeholder value changes */
    'update:placeholder': [date: DateValue];
    /** Event handler called whenever the start value changes */
    'update:startValue': [date: DateValue | undefined];
};

export declare type DateRangePickerRootProps = DateRangeFieldRootProps & PopoverRootProps & Pick<RangeCalendarRootProps, 'isDateDisabled' | 'pagedNavigation' | 'weekStartsOn' | 'weekdayFormat' | 'fixedWeeks' | 'numberOfMonths' | 'preventDeselect' | 'isDateUnavailable' | 'isDateHighlightable' | 'allowNonContiguousRanges' | 'fixedDate'>;

export declare const DateRangePickerTrigger: __VLS_WithTemplateSlots_109<DefineComponent<DateRangePickerTriggerProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<DateRangePickerTriggerProps> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface DateRangePickerTriggerProps extends PopoverTriggerProps {
}

declare type DateRangeType = 'start' | 'end';

declare type DateSegmentObj = {
    [K in DateSegmentPart]: number | null;
};

declare type DateSegmentPart = (typeof DATE_SEGMENT_PARTS)[number];

declare type DateStep = {
    year?: number;
    month?: number;
    day?: number;
    hour?: number;
    minute?: number;
    second?: number;
    millisecond?: number;
};

export { DateValue }

declare type DayPeriod = 'AM' | 'PM' | null;

export declare const DialogClose: __VLS_WithTemplateSlots_110<DefineComponent<DialogCloseProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<DialogCloseProps> & Readonly<{}>, {
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface DialogCloseProps extends PrimitiveProps {
}

export declare const DialogContent: __VLS_WithTemplateSlots_111<DefineComponent<DialogContentProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {
escapeKeyDown: (event: KeyboardEvent) => any;
pointerDownOutside: (event: PointerDownOutsideEvent) => any;
focusOutside: (event: FocusOutsideEvent) => any;
interactOutside: (event: PointerDownOutsideEvent | FocusOutsideEvent) => any;
openAutoFocus: (event: Event) => any;
closeAutoFocus: (event: Event) => any;
}, string, PublicProps, Readonly<DialogContentProps> & Readonly<{
onEscapeKeyDown?: ((event: KeyboardEvent) => any) | undefined;
onPointerDownOutside?: ((event: PointerDownOutsideEvent) => any) | undefined;
onFocusOutside?: ((event: FocusOutsideEvent) => any) | undefined;
onInteractOutside?: ((event: PointerDownOutsideEvent | FocusOutsideEvent) => any) | undefined;
onOpenAutoFocus?: ((event: Event) => any) | undefined;
onCloseAutoFocus?: ((event: Event) => any) | undefined;
}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
    default?(_: {}): any;
}>;

export declare type DialogContentEmits = DialogContentImplEmits;

declare type DialogContentImplEmits = DismissableLayerEmits & {
    /**
     * Event handler called when auto-focusing on open.
     * Can be prevented.
     */
    openAutoFocus: [event: Event];
    /**
     * Event handler called when auto-focusing on close.
     * Can be prevented.
     */
    closeAutoFocus: [event: Event];
};

declare interface DialogContentImplProps extends DismissableLayerProps {
    /**
     * Used to force mounting when more control is needed. Useful when
     * controlling transntion with Vue native transition or other animation libraries.
     */
    forceMount?: boolean;
    /**
     * When `true`, focus cannot escape the `Content` via keyboard,
     * pointer, or a programmatic focus.
     * @defaultValue false
     */
    trapFocus?: boolean;
}

export declare interface DialogContentProps extends DialogContentImplProps {
    /**
     * Used to force mounting when more control is needed. Useful when
     * controlling animation with Vue animation libraries.
     */
    forceMount?: boolean;
}

export declare const DialogDescription: __VLS_WithTemplateSlots_112<DefineComponent<DialogDescriptionProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<DialogDescriptionProps> & Readonly<{}>, {
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface DialogDescriptionProps extends PrimitiveProps {
}

export declare const DialogOverlay: __VLS_WithTemplateSlots_113<DefineComponent<DialogOverlayProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<DialogOverlayProps> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

declare interface DialogOverlayImplProps extends PrimitiveProps {
}

export declare interface DialogOverlayProps extends DialogOverlayImplProps {
    /**
     * Used to force mounting when more control is needed. Useful when
     * controlling animation with Vue animation libraries.
     */
    forceMount?: boolean;
}

export declare const DialogPortal: __VLS_WithTemplateSlots_114<DefineComponent<DialogPortalProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<DialogPortalProps> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface DialogPortalProps extends TeleportProps {
}

export declare const DialogRoot: __VLS_WithTemplateSlots_115<DefineComponent<DialogRootProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {
"update:open": (value: boolean) => any;
}, string, PublicProps, Readonly<DialogRootProps> & Readonly<{
"onUpdate:open"?: ((value: boolean) => any) | undefined;
}>, {
defaultOpen: boolean;
open: boolean;
modal: boolean;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, Readonly<{
    default?: (props: {
        /** Current open state */
        open: boolean;
        /** Close the dialog */
        close: () => void;
    }) => any;
}> & {
    default?: (props: {
        /** Current open state */
        open: boolean;
        /** Close the dialog */
        close: () => void;
    }) => any;
}>;

declare interface DialogRootContext {
    open: Readonly<Ref<boolean>>;
    modal: Ref<boolean>;
    openModal: () => void;
    onOpenChange: (value: boolean) => void;
    onOpenToggle: () => void;
    triggerElement: Ref<HTMLElement | undefined>;
    contentElement: Ref<HTMLElement | undefined>;
    contentId: string;
    titleId: string;
    descriptionId: string;
}

export declare type DialogRootEmits = {
    /** Event handler called when the open state of the dialog changes. */
    'update:open': [value: boolean];
};

export declare interface DialogRootProps {
    /** The controlled open state of the dialog. Can be binded as `v-model:open`. */
    open?: boolean;
    /** The open state of the dialog when it is initially rendered. Use when you do not need to control its open state. */
    defaultOpen?: boolean;
    /**
     * The modality of the dialog When set to `true`, <br>
     * interaction with outside elements will be disabled and only dialog content will be visible to screen readers.
     */
    modal?: boolean;
}

export declare const DialogTitle: __VLS_WithTemplateSlots_116<DefineComponent<DialogTitleProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<DialogTitleProps> & Readonly<{}>, {
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface DialogTitleProps extends PrimitiveProps {
}

export declare const DialogTrigger: __VLS_WithTemplateSlots_117<DefineComponent<DialogTriggerProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<DialogTriggerProps> & Readonly<{}>, {
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface DialogTriggerProps extends PrimitiveProps {
}

declare type Direction = 'ltr' | 'rtl';

declare type Direction_2 = 'ltr' | 'rtl';

declare type Direction_3 = 'ltr' | 'rtl';

declare type Direction_4 = 'ltr' | 'rtl';

declare type Direction_5 = 'ltr' | 'rtl';

declare type Direction_6 = 'horizontal' | 'vertical';

declare type DismissableLayerEmits = {
    /**
     * Event handler called when the escape key is down.
     * Can be prevented.
     */
    escapeKeyDown: [event: KeyboardEvent];
    /**
     * Event handler called when a `pointerdown` event happens outside of the `DismissableLayer`.
     * Can be prevented.
     */
    pointerDownOutside: [event: PointerDownOutsideEvent];
    /**
     * Event handler called when the focus moves outside of the `DismissableLayer`.
     * Can be prevented.
     */
    focusOutside: [event: FocusOutsideEvent];
    /**
     * Event handler called when an interaction happens outside the `DismissableLayer`.
     * Specifically, when a `pointerdown` event happens outside or focus moves outside of it.
     * Can be prevented.
     */
    interactOutside: [event: PointerDownOutsideEvent | FocusOutsideEvent];
};

declare interface DismissableLayerProps extends PrimitiveProps {
    /**
     * When `true`, hover/focus/click interactions will be disabled on elements outside
     * the `DismissableLayer`. Users will need to click twice on outside elements to
     * interact with them: once to close the `DismissableLayer`, and again to trigger the element.
     */
    disableOutsidePointerEvents?: boolean;
}

declare type DragState = {
    dragHandleId: string;
    dragHandleRect: DOMRect;
    initialCursorPosition: number;
    initialLayout: number[];
};

export declare const DropdownMenuArrow: __VLS_WithTemplateSlots_118<DefineComponent<DropdownMenuArrowProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<DropdownMenuArrowProps> & Readonly<{}>, {
width: number;
height: number;
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface DropdownMenuArrowProps extends MenuArrowProps {
}

export declare const DropdownMenuCheckboxItem: __VLS_WithTemplateSlots_119<DefineComponent<DropdownMenuCheckboxItemProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {
select: (event: Event) => any;
"update:modelValue": (payload: boolean) => any;
}, string, PublicProps, Readonly<DropdownMenuCheckboxItemProps> & Readonly<{
onSelect?: ((event: Event) => any) | undefined;
"onUpdate:modelValue"?: ((payload: boolean) => any) | undefined;
}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare type DropdownMenuCheckboxItemEmits = MenuCheckboxItemEmits;

export declare interface DropdownMenuCheckboxItemProps extends MenuCheckboxItemProps {
}

export declare const DropdownMenuContent: __VLS_WithTemplateSlots_120<DefineComponent<DropdownMenuContentProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {
escapeKeyDown: (event: KeyboardEvent) => any;
pointerDownOutside: (event: PointerDownOutsideEvent) => any;
focusOutside: (event: FocusOutsideEvent) => any;
interactOutside: (event: PointerDownOutsideEvent | FocusOutsideEvent) => any;
closeAutoFocus: (event: Event) => any;
}, string, PublicProps, Readonly<DropdownMenuContentProps> & Readonly<{
onEscapeKeyDown?: ((event: KeyboardEvent) => any) | undefined;
onPointerDownOutside?: ((event: PointerDownOutsideEvent) => any) | undefined;
onFocusOutside?: ((event: FocusOutsideEvent) => any) | undefined;
onInteractOutside?: ((event: PointerDownOutsideEvent | FocusOutsideEvent) => any) | undefined;
onCloseAutoFocus?: ((event: Event) => any) | undefined;
}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare type DropdownMenuContentEmits = MenuContentEmits;

export declare interface DropdownMenuContentProps extends MenuContentProps {
}

export declare const DropdownMenuGroup: __VLS_WithTemplateSlots_121<DefineComponent<DropdownMenuGroupProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<DropdownMenuGroupProps> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface DropdownMenuGroupProps extends MenuGroupProps {
}

export declare const DropdownMenuItem: __VLS_WithTemplateSlots_122<DefineComponent<DropdownMenuItemProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {
select: (event: Event) => any;
}, string, PublicProps, Readonly<DropdownMenuItemProps> & Readonly<{
onSelect?: ((event: Event) => any) | undefined;
}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare type DropdownMenuItemEmits = MenuItemEmits;

export declare const DropdownMenuItemIndicator: __VLS_WithTemplateSlots_123<DefineComponent<DropdownMenuItemIndicatorProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<DropdownMenuItemIndicatorProps> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface DropdownMenuItemIndicatorProps extends MenuItemIndicatorProps {
}

export declare interface DropdownMenuItemProps extends MenuItemProps {
}

export declare const DropdownMenuLabel: __VLS_WithTemplateSlots_124<DefineComponent<DropdownMenuLabelProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<DropdownMenuLabelProps> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface DropdownMenuLabelProps extends MenuLabelProps {
}

export declare const DropdownMenuPortal: __VLS_WithTemplateSlots_125<DefineComponent<DropdownMenuPortalProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<DropdownMenuPortalProps> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface DropdownMenuPortalProps extends MenuPortalProps {
}

export declare const DropdownMenuRadioGroup: __VLS_WithTemplateSlots_126<DefineComponent<MenuRadioGroupProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {
"update:modelValue": (payload: string) => any;
}, string, PublicProps, Readonly<MenuRadioGroupProps> & Readonly<{
"onUpdate:modelValue"?: ((payload: string) => any) | undefined;
}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare type DropdownMenuRadioGroupEmits = MenuRadioGroupEmits;

export declare interface DropdownMenuRadioGroupProps extends MenuRadioGroupProps {
}

export declare const DropdownMenuRadioItem: __VLS_WithTemplateSlots_127<DefineComponent<DropdownMenuRadioItemProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {
select: (event: Event) => any;
}, string, PublicProps, Readonly<DropdownMenuRadioItemProps> & Readonly<{
onSelect?: ((event: Event) => any) | undefined;
}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare type DropdownMenuRadioItemEmits = MenuRadioItemEmits;

export declare interface DropdownMenuRadioItemProps extends MenuRadioItemProps {
}

export declare const DropdownMenuRoot: __VLS_WithTemplateSlots_128<DefineComponent<DropdownMenuRootProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {
"update:open": (payload: boolean) => any;
}, string, PublicProps, Readonly<DropdownMenuRootProps> & Readonly<{
"onUpdate:open"?: ((payload: boolean) => any) | undefined;
}>, {
open: boolean;
modal: boolean;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, Readonly<{
    default?: (props: {
        /** Current open state */
        open: boolean;
    }) => any;
}> & {
    default?: (props: {
        /** Current open state */
        open: boolean;
    }) => any;
}>;

declare interface DropdownMenuRootContext {
    open: Readonly<Ref<boolean>>;
    onOpenChange: (open: boolean) => void;
    onOpenToggle: () => void;
    triggerId: string;
    triggerElement: Ref<HTMLElement | undefined>;
    contentId: string;
    modal: Ref<boolean>;
    dir: Ref<Direction>;
}

export declare type DropdownMenuRootEmits = MenuEmits;

export declare interface DropdownMenuRootProps extends MenuProps {
    /** The open state of the dropdown menu when it is initially rendered. Use when you do not need to control its open state. */
    defaultOpen?: boolean;
}

export declare const DropdownMenuSeparator: __VLS_WithTemplateSlots_129<DefineComponent<DropdownMenuSeparatorProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<DropdownMenuSeparatorProps> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface DropdownMenuSeparatorProps extends MenuSeparatorProps {
}

export declare const DropdownMenuSub: __VLS_WithTemplateSlots_130<DefineComponent<DropdownMenuSubProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {
"update:open": (payload: boolean) => any;
}, string, PublicProps, Readonly<DropdownMenuSubProps> & Readonly<{
"onUpdate:open"?: ((payload: boolean) => any) | undefined;
}>, {
open: boolean;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, Readonly<{
    default?: (props: {
        /** Current open state */
        open: boolean;
    }) => any;
}> & {
    default?: (props: {
        /** Current open state */
        open: boolean;
    }) => any;
}>;

export declare const DropdownMenuSubContent: __VLS_WithTemplateSlots_131<DefineComponent<DropdownMenuSubContentProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {
escapeKeyDown: (event: KeyboardEvent) => any;
pointerDownOutside: (event: PointerDownOutsideEvent) => any;
focusOutside: (event: FocusOutsideEvent) => any;
interactOutside: (event: PointerDownOutsideEvent | FocusOutsideEvent) => any;
openAutoFocus: (event: Event) => any;
closeAutoFocus: (event: Event) => any;
entryFocus: (event: Event) => any;
}, string, PublicProps, Readonly<DropdownMenuSubContentProps> & Readonly<{
onEscapeKeyDown?: ((event: KeyboardEvent) => any) | undefined;
onPointerDownOutside?: ((event: PointerDownOutsideEvent) => any) | undefined;
onFocusOutside?: ((event: FocusOutsideEvent) => any) | undefined;
onInteractOutside?: ((event: PointerDownOutsideEvent | FocusOutsideEvent) => any) | undefined;
onOpenAutoFocus?: ((event: Event) => any) | undefined;
onCloseAutoFocus?: ((event: Event) => any) | undefined;
onEntryFocus?: ((event: Event) => any) | undefined;
}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare type DropdownMenuSubContentEmits = MenuSubContentEmits;

export declare interface DropdownMenuSubContentProps extends MenuSubContentProps {
}

export declare type DropdownMenuSubEmits = MenuSubEmits;

export declare interface DropdownMenuSubProps extends MenuSubProps {
    /** The open state of the dropdown menu when it is initially rendered. Use when you do not need to control its open state. */
    defaultOpen?: boolean;
}

export declare const DropdownMenuSubTrigger: __VLS_WithTemplateSlots_132<DefineComponent<DropdownMenuSubTriggerProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<DropdownMenuSubTriggerProps> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface DropdownMenuSubTriggerProps extends MenuSubTriggerProps {
}

export declare const DropdownMenuTrigger: __VLS_WithTemplateSlots_133<DefineComponent<DropdownMenuTriggerProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<DropdownMenuTriggerProps> & Readonly<{}>, {
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface DropdownMenuTriggerProps extends PrimitiveProps {
    /** When `true`, prevents the user from interacting with item */
    disabled?: boolean;
}

declare const EDITABLE_SEGMENT_PARTS: readonly ["day", "month", "year", "hour", "minute", "second", "dayPeriod"];

export declare const EditableArea: __VLS_WithTemplateSlots_134<DefineComponent<EditableAreaProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<EditableAreaProps> & Readonly<{}>, {
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface EditableAreaProps extends PrimitiveProps {
}

export declare const EditableCancelTrigger: __VLS_WithTemplateSlots_135<DefineComponent<EditableCancelTriggerProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<EditableCancelTriggerProps> & Readonly<{}>, {
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface EditableCancelTriggerProps extends PrimitiveProps {
}

export declare const EditableEditTrigger: __VLS_WithTemplateSlots_136<DefineComponent<EditableEditTriggerProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<EditableEditTriggerProps> & Readonly<{}>, {
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface EditableEditTriggerProps extends PrimitiveProps {
}

export declare const EditableInput: __VLS_WithTemplateSlots_137<DefineComponent<EditableInputProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<EditableInputProps> & Readonly<{}>, {
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {
primitiveElement: CreateComponentPublicInstanceWithMixins<Readonly<ExtractPropTypes<    {
asChild: {
type: BooleanConstructor;
default: boolean;
};
as: {
type: PropType<AsTag | Component>;
default: string;
};
}>> & Readonly<{}>, () => VNode<RendererNode, RendererElement, {
[key: string]: any;
}>, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, PublicProps, {
asChild: boolean;
as: AsTag | Component;
}, true, {}, {}, GlobalComponents, GlobalDirectives, string, {}, any, ComponentProvideOptions, {
P: {};
B: {};
D: {};
C: {};
M: {};
Defaults: {};
}, Readonly<ExtractPropTypes<    {
asChild: {
type: BooleanConstructor;
default: boolean;
};
as: {
type: PropType<AsTag | Component>;
default: string;
};
}>> & Readonly<{}>, () => VNode<RendererNode, RendererElement, {
[key: string]: any;
}>, {}, {}, {}, {
asChild: boolean;
as: AsTag | Component;
}> | null;
}, any>, {
    default?(_: {}): any;
}>;

export declare interface EditableInputProps extends PrimitiveProps {
}

export declare const EditablePreview: __VLS_WithTemplateSlots_138<DefineComponent<EditablePreviewProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<EditablePreviewProps> & Readonly<{}>, {
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface EditablePreviewProps extends PrimitiveProps {
}

export declare const EditableRoot: __VLS_WithTemplateSlots_139<DefineComponent<EditableRootProps, {
/** Function to submit the value of the editable */
submit: () => void;
/** Function to cancel the value of the editable */
cancel: () => void;
/** Function to set the editable in edit mode */
edit: () => void;
}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {
submit: (value: string | null | undefined) => any;
"update:modelValue": (value: string) => any;
"update:state": (state: "cancel" | "submit" | "edit") => any;
}, string, PublicProps, Readonly<EditableRootProps> & Readonly<{
onSubmit?: ((value: string | null | undefined) => any) | undefined;
"onUpdate:modelValue"?: ((value: string) => any) | undefined;
"onUpdate:state"?: ((state: "cancel" | "submit" | "edit") => any) | undefined;
}>, {
placeholder: string | {
edit: string;
preview: string;
};
disabled: boolean;
required: boolean;
as: AsTag | Component;
activationMode: ActivationMode;
selectOnFocus: boolean;
submitMode: SubmitMode;
autoResize: boolean;
}, {}, {}, {}, string, ComponentProvideOptions, false, {
primitiveElement: CreateComponentPublicInstanceWithMixins<Readonly<ExtractPropTypes<    {
asChild: {
type: BooleanConstructor;
default: boolean;
};
as: {
type: PropType<AsTag | Component>;
default: string;
};
}>> & Readonly<{}>, () => VNode<RendererNode, RendererElement, {
[key: string]: any;
}>, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, PublicProps, {
asChild: boolean;
as: AsTag | Component;
}, true, {}, {}, GlobalComponents, GlobalDirectives, string, {}, any, ComponentProvideOptions, {
P: {};
B: {};
D: {};
C: {};
M: {};
Defaults: {};
}, Readonly<ExtractPropTypes<    {
asChild: {
type: BooleanConstructor;
default: boolean;
};
as: {
type: PropType<AsTag | Component>;
default: string;
};
}>> & Readonly<{}>, () => VNode<RendererNode, RendererElement, {
[key: string]: any;
}>, {}, {}, {}, {
asChild: boolean;
as: AsTag | Component;
}> | null;
}, any>, Readonly<{
    default?: (props: {
        /** Whether the editable field is in edit mode */
        isEditing: boolean;
        /** The value of the editable field */
        modelValue: string | null | undefined;
        /** Whether the editable field is empty */
        isEmpty: boolean;
        /** Function to submit the value of the editable */
        submit: () => void;
        /** Function to cancel the value of the editable */
        cancel: () => void;
        /** Function to set the editable in edit mode */
        edit: () => void;
    }) => any;
}> & {
    default?: (props: {
        /** Whether the editable field is in edit mode */
        isEditing: boolean;
        /** The value of the editable field */
        modelValue: string | null | undefined;
        /** Whether the editable field is empty */
        isEmpty: boolean;
        /** Function to submit the value of the editable */
        submit: () => void;
        /** Function to cancel the value of the editable */
        cancel: () => void;
        /** Function to set the editable in edit mode */
        edit: () => void;
    }) => any;
}>;

declare type EditableRootContext = {
    id: Ref<string | undefined>;
    name: Ref<string | undefined>;
    maxLength: Ref<number | undefined>;
    disabled: Ref<boolean>;
    modelValue: Ref<string | null | undefined>;
    inputValue: Ref<string | null | undefined>;
    placeholder: Ref<{
        edit: string;
        preview: string;
    }>;
    isEditing: Ref<boolean>;
    submitMode: Ref<SubmitMode>;
    activationMode: Ref<ActivationMode>;
    selectOnFocus: Ref<boolean>;
    edit: () => void;
    cancel: () => void;
    submit: () => void;
    inputRef: Ref<HTMLInputElement | undefined>;
    startWithEditMode: Ref<boolean>;
    isEmpty: Ref<boolean>;
    readonly: Ref<boolean>;
    autoResize: Ref<boolean>;
};

export declare type EditableRootEmits = {
    /** Event handler called whenever the model value changes */
    'update:modelValue': [value: string];
    /** Event handler called when a value is submitted */
    'submit': [value: string | null | undefined];
    /** Event handler called when the editable field changes state */
    'update:state': [state: 'edit' | 'submit' | 'cancel'];
};

export declare interface EditableRootProps extends PrimitiveProps, FormFieldProps {
    /** The default value of the editable field */
    defaultValue?: string;
    /** The value of the editable field */
    modelValue?: string | null;
    /** The placeholder for the editable field */
    placeholder?: string | {
        edit: string;
        preview: string;
    };
    /** The reading direction of the calendar when applicable. <br> If omitted, inherits globally from `ConfigProvider` or assumes LTR (left-to-right) reading mode. */
    dir?: Direction;
    /** Whether the editable field is disabled */
    disabled?: boolean;
    /** Whether the editable field is read-only */
    readonly?: boolean;
    /** The activation event of the editable field */
    activationMode?: ActivationMode;
    /** Whether to select the text in the input when it is focused. */
    selectOnFocus?: boolean;
    /** The submit event of the editable field */
    submitMode?: SubmitMode;
    /** Whether to start with the edit mode active */
    startWithEditMode?: boolean;
    /** The maximum number of characters allowed */
    maxLength?: number;
    /** Whether the editable field should auto resize */
    autoResize?: boolean;
    /** The id of the field */
    id?: string;
}

declare type EditableSegmentPart = (typeof EDITABLE_SEGMENT_PARTS)[number];

export declare const EditableSubmitTrigger: __VLS_WithTemplateSlots_140<DefineComponent<EditableSubmitTriggerProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<EditableSubmitTriggerProps> & Readonly<{}>, {
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface EditableSubmitTriggerProps extends PrimitiveProps {
}

export declare type FlattenedItem<T> = {
    _id: string;
    index: number;
    value: T;
    level: number;
    hasChildren: boolean;
    parentItem?: T;
    bind: {
        value: T;
        level: number;
        [key: string]: any;
    };
};

declare type FocusOutsideEvent = CustomEvent<{
    originalEvent: FocusEvent;
}>;

export declare const FocusScope: __VLS_WithTemplateSlots_141<DefineComponent<FocusScopeProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {
mountAutoFocus: (event: Event) => any;
unmountAutoFocus: (event: Event) => any;
}, string, PublicProps, Readonly<FocusScopeProps> & Readonly<{
onMountAutoFocus?: ((event: Event) => any) | undefined;
onUnmountAutoFocus?: ((event: Event) => any) | undefined;
}>, {
loop: boolean;
trapped: boolean;
}, {}, {}, {}, string, ComponentProvideOptions, false, {
currentRef: CreateComponentPublicInstanceWithMixins<Readonly<ExtractPropTypes<    {
asChild: {
type: BooleanConstructor;
default: boolean;
};
as: {
type: PropType<AsTag | Component>;
default: string;
};
}>> & Readonly<{}>, () => VNode<RendererNode, RendererElement, {
[key: string]: any;
}>, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, PublicProps, {
asChild: boolean;
as: AsTag | Component;
}, true, {}, {}, GlobalComponents, GlobalDirectives, string, {}, any, ComponentProvideOptions, {
P: {};
B: {};
D: {};
C: {};
M: {};
Defaults: {};
}, Readonly<ExtractPropTypes<    {
asChild: {
type: BooleanConstructor;
default: boolean;
};
as: {
type: PropType<AsTag | Component>;
default: string;
};
}>> & Readonly<{}>, () => VNode<RendererNode, RendererElement, {
[key: string]: any;
}>, {}, {}, {}, {
asChild: boolean;
as: AsTag | Component;
}> | null;
}, any>, {
    default?(_: {}): any;
}>;

export declare type FocusScopeEmits = {
    /**
     * Event handler called when auto-focusing on mount.
     * Can be prevented.
     */
    mountAutoFocus: [event: Event];
    /**
     * Event handler called when auto-focusing on unmount.
     * Can be prevented.
     */
    unmountAutoFocus: [event: Event];
};

export declare interface FocusScopeProps extends PrimitiveProps {
    /**
     * When `true`, tabbing from last item will focus first tabbable
     * and shift+tab from first item will focus last tababble.
     * @defaultValue false
     */
    loop?: boolean;
    /**
     * When `true`, focus cannot escape the focus scope via keyboard,
     * pointer, or a programmatic focus.
     * @defaultValue false
     */
    trapped?: boolean;
}

export declare type Formatter = {
    getLocale: () => string;
    setLocale: (newLocale: string) => void;
    custom: (date: Date, options: DateFormatterOptions) => string;
    selectedDate: (date: DateValue, includeTime?: boolean) => string;
    dayOfWeek: (date: Date, length?: DateFormatterOptions['weekday']) => string;
    fullMonthAndYear: (date: Date, options?: DateFormatterOptions) => string;
    fullMonth: (date: Date, options?: DateFormatterOptions) => string;
    fullYear: (date: Date, options?: DateFormatterOptions) => string;
    dayPeriod: (date: Date) => string;
    part: (dateObj: DateValue, type: Intl.DateTimeFormatPartTypes, options?: DateFormatterOptions) => string;
    toParts: (date: DateValue, options?: DateFormatterOptions) => Intl.DateTimeFormatPart[];
    getMonths: () => {
        label: string;
        value: number;
    }[];
};

declare interface FormFieldProps {
    /** The name of the field. Submitted with its owning form as part of a name/value pair. */
    name?: string;
    /** When `true`, indicates that the user must set the value before the owning form can be submitted. */
    required?: boolean;
}

export declare type GenericComponentInstance<T> = T extends new (...args: any[]) => infer R ? R : T extends (...args: any[]) => infer R ? R extends {
    __ctx?: infer K;
} ? Exclude<K, void> extends {
    expose: (...args: infer Y) => void;
} ? Y[0] & InstanceType<DefineComponent> : any : any : any;

declare type Granularity = 'day' | 'hour' | 'minute' | 'second';

declare type Grid<T> = {
    /**
     * A `DateValue` used to represent the month. Since days
     * from the previous and next months may be included in the
     * calendar grid, we need a source of truth for the value
     * the grid is representing.
     */
    value: DateValue;
    /**
     * An array of arrays representing the weeks in the calendar.
     * Each sub-array represents a week, and contains the dates for each
     * day in that week. This structure is useful for rendering the calendar
     * grid using a table, where each row represents a week and each cell
     * represents a day.
     */
    rows: T[][];
    /**
     * An array of all the dates in the current month, including dates from
     * the previous and next months that are used to fill out the calendar grid.
     * This array is useful for rendering the calendar grid in a customizable way,
     * as it provides all the dates that should be displayed in the grid in a flat
     * array.
     */
    cells: T[];
};

declare type HourCycle = 12 | 24 | undefined;

export declare const HoverCardArrow: __VLS_WithTemplateSlots_142<DefineComponent<HoverCardArrowProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<HoverCardArrowProps> & Readonly<{}>, {
width: number;
height: number;
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface HoverCardArrowProps extends PopperArrowProps {
}

export declare const HoverCardContent: __VLS_WithTemplateSlots_143<DefineComponent<HoverCardContentProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {
escapeKeyDown: (event: KeyboardEvent) => any;
pointerDownOutside: (event: PointerDownOutsideEvent) => any;
focusOutside: (event: FocusOutsideEvent) => any;
interactOutside: (event: PointerDownOutsideEvent | FocusOutsideEvent) => any;
}, string, PublicProps, Readonly<HoverCardContentProps> & Readonly<{
onEscapeKeyDown?: ((event: KeyboardEvent) => any) | undefined;
onPointerDownOutside?: ((event: PointerDownOutsideEvent) => any) | undefined;
onFocusOutside?: ((event: FocusOutsideEvent) => any) | undefined;
onInteractOutside?: ((event: PointerDownOutsideEvent | FocusOutsideEvent) => any) | undefined;
}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

declare interface HoverCardContentImplProps extends PopperContentProps {
}

export declare interface HoverCardContentProps extends HoverCardContentImplProps {
    /**
     * Used to force mounting when more control is needed. Useful when
     * controlling animation with Vue animation libraries.
     */
    forceMount?: boolean;
}

export declare const HoverCardPortal: __VLS_WithTemplateSlots_144<DefineComponent<HoverCardPortalProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<HoverCardPortalProps> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface HoverCardPortalProps extends TeleportProps {
}

export declare const HoverCardRoot: __VLS_WithTemplateSlots_145<DefineComponent<HoverCardRootProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {
"update:open": (value: boolean) => any;
}, string, PublicProps, Readonly<HoverCardRootProps> & Readonly<{
"onUpdate:open"?: ((value: boolean) => any) | undefined;
}>, {
defaultOpen: boolean;
open: boolean;
openDelay: number;
closeDelay: number;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, Readonly<{
    default?: (props: {
        /** Current open state */
        open: boolean;
    }) => any;
}> & {
    default?: (props: {
        /** Current open state */
        open: boolean;
    }) => any;
}>;

declare interface HoverCardRootContext {
    open: Ref<boolean>;
    onOpenChange: (open: boolean) => void;
    onOpen: () => void;
    onClose: () => void;
    onDismiss: () => void;
    hasSelectionRef: Ref<boolean>;
    isPointerDownOnContentRef: Ref<boolean>;
    isPointerInTransitRef: Ref<boolean>;
    triggerElement: Ref<HTMLElement | undefined>;
}

export declare type HoverCardRootEmits = {
    /** Event handler called when the open state of the hover card changes. */
    'update:open': [value: boolean];
};

export declare interface HoverCardRootProps {
    /** The open state of the hover card when it is initially rendered. Use when you do not need to control its open state. */
    defaultOpen?: boolean;
    /** The controlled open state of the hover card. Can be binded as `v-model:open`. */
    open?: boolean;
    /** The duration from when the mouse enters the trigger until the hover card opens. */
    openDelay?: number;
    /** The duration from when the mouse leaves the trigger or content until the hover card closes. */
    closeDelay?: number;
}

export declare const HoverCardTrigger: __VLS_WithTemplateSlots_146<DefineComponent<HoverCardTriggerProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<HoverCardTriggerProps> & Readonly<{}>, {
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface HoverCardTriggerProps extends PopperAnchorProps {
}

declare type ImageLoadingStatus = 'idle' | 'loading' | 'loaded' | 'error';

export declare const injectAccordionItemContext: <T extends AccordionItemContext | null | undefined = AccordionItemContext>(fallback?: T | undefined) => T extends null ? AccordionItemContext | null : AccordionItemContext;

export declare const injectAccordionRootContext: <T extends AccordionRootContext<AccordionRootProps<string | string[]>> | null | undefined = AccordionRootContext<AccordionRootProps<string | string[]>>>(fallback?: T | undefined) => T extends null ? AccordionRootContext<AccordionRootProps<string | string[]>> | null : AccordionRootContext<AccordionRootProps<string | string[]>>;

export declare const injectAlertDialogContentContext: <T extends AlertDialogContentContext | null | undefined = AlertDialogContentContext>(fallback?: T | undefined) => T extends null ? AlertDialogContentContext | null : AlertDialogContentContext;

export declare const injectAvatarRootContext: <T extends AvatarRootContext | null | undefined = AvatarRootContext>(fallback?: T | undefined) => T extends null ? AvatarRootContext | null : AvatarRootContext;

export declare const injectCalendarRootContext: <T extends CalendarRootContext | null | undefined = CalendarRootContext>(fallback?: T | undefined) => T extends null ? CalendarRootContext | null : CalendarRootContext;

export declare const injectCheckboxGroupRootContext: <T extends CheckboxGroupRootContext | null | undefined = CheckboxGroupRootContext>(fallback?: T | undefined) => T extends null ? CheckboxGroupRootContext | null : CheckboxGroupRootContext;

export declare const injectCheckboxRootContext: <T extends CheckboxRootContext | null | undefined = CheckboxRootContext>(fallback?: T | undefined) => T extends null ? CheckboxRootContext | null : CheckboxRootContext;

export declare const injectCollapsibleRootContext: <T extends CollapsibleRootContext | null | undefined = CollapsibleRootContext>(fallback?: T | undefined) => T extends null ? CollapsibleRootContext | null : CollapsibleRootContext;

export declare const injectComboboxGroupContext: <T extends ComboboxGroupContext | null | undefined = ComboboxGroupContext>(fallback?: T | undefined) => T extends null ? ComboboxGroupContext | null : ComboboxGroupContext;

export declare const injectComboboxRootContext: <T extends ComboboxRootContext<AcceptableValue> | null | undefined = ComboboxRootContext<AcceptableValue>>(fallback?: T | undefined) => T extends null ? ComboboxRootContext<AcceptableValue> | null : ComboboxRootContext<AcceptableValue>;

export declare const injectConfigProviderContext: <T extends ConfigProviderContextValue | null | undefined = ConfigProviderContextValue>(fallback?: T | undefined) => T extends null ? ConfigProviderContextValue | null : ConfigProviderContextValue;

export declare const injectContextMenuRootContext: <T extends ContextMenuRootContext | null | undefined = ContextMenuRootContext>(fallback?: T | undefined) => T extends null ? ContextMenuRootContext | null : ContextMenuRootContext;

export declare const injectDateFieldRootContext: <T extends DateFieldRootContext | null | undefined = DateFieldRootContext>(fallback?: T | undefined) => T extends null ? DateFieldRootContext | null : DateFieldRootContext;

export declare const injectDatePickerRootContext: <T extends DatePickerRootContext | null | undefined = DatePickerRootContext>(fallback?: T | undefined) => T extends null ? DatePickerRootContext | null : DatePickerRootContext;

export declare const injectDateRangeFieldRootContext: <T extends DateRangeFieldRootContext | null | undefined = DateRangeFieldRootContext>(fallback?: T | undefined) => T extends null ? DateRangeFieldRootContext | null : DateRangeFieldRootContext;

export declare const injectDateRangePickerRootContext: <T extends DateRangePickerRootContext | null | undefined = DateRangePickerRootContext>(fallback?: T | undefined) => T extends null ? DateRangePickerRootContext | null : DateRangePickerRootContext;

export declare const injectDialogRootContext: <T extends DialogRootContext | null | undefined = DialogRootContext>(fallback?: T | undefined) => T extends null ? DialogRootContext | null : DialogRootContext;

export declare const injectDropdownMenuRootContext: <T extends DropdownMenuRootContext | null | undefined = DropdownMenuRootContext>(fallback?: T | undefined) => T extends null ? DropdownMenuRootContext | null : DropdownMenuRootContext;

export declare const injectEditableRootContext: <T extends EditableRootContext | null | undefined = EditableRootContext>(fallback?: T | undefined) => T extends null ? EditableRootContext | null : EditableRootContext;

export declare const injectHoverCardRootContext: <T extends HoverCardRootContext | null | undefined = HoverCardRootContext>(fallback?: T | undefined) => T extends null ? HoverCardRootContext | null : HoverCardRootContext;

export declare const injectListboxGroupContext: <T extends ListboxGroupContext | null | undefined = ListboxGroupContext>(fallback?: T | undefined) => T extends null ? ListboxGroupContext | null : ListboxGroupContext;

declare const injectListboxItemContext: <T extends ListboxItemContext | null | undefined = ListboxItemContext>(fallback?: T | undefined) => T extends null ? ListboxItemContext | null : ListboxItemContext;
export { injectListboxItemContext as injectComboboxItemContext }
export { injectListboxItemContext }

export declare const injectListboxRootContext: <T extends ListboxRootContext<AcceptableValue> | null | undefined = ListboxRootContext<AcceptableValue>>(fallback?: T | undefined) => T extends null ? ListboxRootContext<AcceptableValue> | null : ListboxRootContext<AcceptableValue>;

export declare const injectMenubarMenuContext: <T extends MenubarMenuContext | null | undefined = MenubarMenuContext>(fallback?: T | undefined) => T extends null ? MenubarMenuContext | null : MenubarMenuContext;

export declare const injectMenubarRootContext: <T extends MenubarRootContext | null | undefined = MenubarRootContext>(fallback?: T | undefined) => T extends null ? MenubarRootContext | null : MenubarRootContext;

export declare const injectNavigationMenuContext: <T extends NavigationMenuContext | null | undefined = NavigationMenuContext>(fallback?: T | undefined) => T extends null ? NavigationMenuContext | null : NavigationMenuContext;

export declare const injectNavigationMenuItemContext: <T extends NavigationMenuItemContext | null | undefined = NavigationMenuItemContext>(fallback?: T | undefined) => T extends null ? NavigationMenuItemContext | null : NavigationMenuItemContext;

export declare const injectNumberFieldRootContext: <T extends NumberFieldRootContext | null | undefined = NumberFieldRootContext>(fallback?: T | undefined) => T extends null ? NumberFieldRootContext | null : NumberFieldRootContext;

export declare const injectPaginationRootContext: <T extends PaginationRootContext | null | undefined = PaginationRootContext>(fallback?: T | undefined) => T extends null ? PaginationRootContext | null : PaginationRootContext;

export declare const injectPinInputRootContext: <T extends PinInputRootContext<PinInputType> | null | undefined = PinInputRootContext<PinInputType>>(fallback?: T | undefined) => T extends null ? PinInputRootContext<PinInputType> | null : PinInputRootContext<PinInputType>;

export declare const injectPopoverRootContext: <T extends PopoverRootContext | null | undefined = PopoverRootContext>(fallback?: T | undefined) => T extends null ? PopoverRootContext | null : PopoverRootContext;

export declare const injectProgressRootContext: <T extends ProgressRootContext | null | undefined = ProgressRootContext>(fallback?: T | undefined) => T extends null ? ProgressRootContext | null : ProgressRootContext;

export declare const injectRadioGroupItemContext: <T extends RadioGroupItemContext | null | undefined = RadioGroupItemContext>(fallback?: T | undefined) => T extends null ? RadioGroupItemContext | null : RadioGroupItemContext;

export declare const injectRadioGroupRootContext: <T extends RadioGroupRootContext | null | undefined = RadioGroupRootContext>(fallback?: T | undefined) => T extends null ? RadioGroupRootContext | null : RadioGroupRootContext;

export declare const injectRangeCalendarRootContext: <T extends RangeCalendarRootContext | null | undefined = RangeCalendarRootContext>(fallback?: T | undefined) => T extends null ? RangeCalendarRootContext | null : RangeCalendarRootContext;

export declare const injectScrollAreaRootContext: <T extends ScrollAreaRootContext | null | undefined = ScrollAreaRootContext>(fallback?: T | undefined) => T extends null ? ScrollAreaRootContext | null : ScrollAreaRootContext;

export declare const injectScrollAreaScrollbarContext: <T extends ScrollAreaScollbarContext | null | undefined = ScrollAreaScollbarContext>(fallback?: T | undefined) => T extends null ? ScrollAreaScollbarContext | null : ScrollAreaScollbarContext;

export declare const injectSelectGroupContext: <T extends SelectGroupContext | null | undefined = SelectGroupContext>(fallback?: T | undefined) => T extends null ? SelectGroupContext | null : SelectGroupContext;

export declare const injectSelectItemContext: <T extends SelectItemContext<AcceptableValue> | null | undefined = SelectItemContext<AcceptableValue>>(fallback?: T | undefined) => T extends null ? SelectItemContext<AcceptableValue> | null : SelectItemContext<AcceptableValue>;

export declare const injectSelectRootContext: <T extends SelectRootContext<AcceptableValue> | null | undefined = SelectRootContext<AcceptableValue>>(fallback?: T | undefined) => T extends null ? SelectRootContext<AcceptableValue> | null : SelectRootContext<AcceptableValue>;

export declare const injectSliderRootContext: <T extends SliderRootContext | null | undefined = SliderRootContext>(fallback?: T | undefined) => T extends null ? SliderRootContext | null : SliderRootContext;

export declare const injectSplitterGroupContext: <T extends PanelGroupContext | null | undefined = PanelGroupContext>(fallback?: T | undefined) => T extends null ? PanelGroupContext | null : PanelGroupContext;

export declare const injectStepperItemContext: <T extends StepperItemContext | null | undefined = StepperItemContext>(fallback?: T | undefined) => T extends null ? StepperItemContext | null : StepperItemContext;

export declare const injectStepperRootContext: <T extends StepperRootContext | null | undefined = StepperRootContext>(fallback?: T | undefined) => T extends null ? StepperRootContext | null : StepperRootContext;

export declare const injectSwitchRootContext: <T extends SwitchRootContext | null | undefined = SwitchRootContext>(fallback?: T | undefined) => T extends null ? SwitchRootContext | null : SwitchRootContext;

export declare const injectTabsRootContext: <T extends TabsRootContext | null | undefined = TabsRootContext>(fallback?: T | undefined) => T extends null ? TabsRootContext | null : TabsRootContext;

export declare const injectTagsInputItemContext: <T extends TagsInputItemContext | null | undefined = TagsInputItemContext>(fallback?: T | undefined) => T extends null ? TagsInputItemContext | null : TagsInputItemContext;

export declare const injectTagsInputRootContext: <T extends TagsInputRootContext<AcceptableInputValue> | null | undefined = TagsInputRootContext<AcceptableInputValue>>(fallback?: T | undefined) => T extends null ? TagsInputRootContext<AcceptableInputValue> | null : TagsInputRootContext<AcceptableInputValue>;

export declare const injectTimeFieldRootContext: <T extends TimeFieldRootContext | null | undefined = TimeFieldRootContext>(fallback?: T | undefined) => T extends null ? TimeFieldRootContext | null : TimeFieldRootContext;

export declare const injectToastProviderContext: <T extends ToastProviderContext | null | undefined = ToastProviderContext>(fallback?: T | undefined) => T extends null ? ToastProviderContext | null : ToastProviderContext;

export declare const injectToggleGroupRootContext: <T extends ToggleGroupRootContext | null | undefined = ToggleGroupRootContext>(fallback?: T | undefined) => T extends null ? ToggleGroupRootContext | null : ToggleGroupRootContext;

export declare const injectToolbarRootContext: <T extends ToolbarRootContext | null | undefined = ToolbarRootContext>(fallback?: T | undefined) => T extends null ? ToolbarRootContext | null : ToolbarRootContext;

export declare const injectTooltipProviderContext: <T extends TooltipProviderContext | null | undefined = TooltipProviderContext>(fallback?: T | undefined) => T extends null ? TooltipProviderContext | null : TooltipProviderContext;

export declare const injectTooltipRootContext: <T extends TooltipContext | null | undefined = TooltipContext>(fallback?: T | undefined) => T extends null ? TooltipContext | null : TooltipContext;

export declare const injectTreeRootContext: <T extends TreeRootContext<any> | null | undefined = TreeRootContext<any>>(fallback?: T | undefined) => T extends null ? TreeRootContext<any> | null : TreeRootContext<any>;

export declare const Label: __VLS_WithTemplateSlots_147<DefineComponent<LabelProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<LabelProps> & Readonly<{}>, {
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface LabelProps extends PrimitiveProps {
    /** The id of the element the label is associated with. */
    for?: string;
}

export declare const ListboxContent: __VLS_WithTemplateSlots_148<DefineComponent<ListboxContentProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<ListboxContentProps> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface ListboxContentProps extends PrimitiveProps {
}

export declare const ListboxFilter: __VLS_WithTemplateSlots_149<DefineComponent<ListboxFilterProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {
"update:modelValue": (args_0: string) => any;
}, string, PublicProps, Readonly<ListboxFilterProps> & Readonly<{
"onUpdate:modelValue"?: ((args_0: string) => any) | undefined;
}>, {
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {
primitiveElement: CreateComponentPublicInstanceWithMixins<Readonly<ExtractPropTypes<    {
asChild: {
type: BooleanConstructor;
default: boolean;
};
as: {
type: PropType<AsTag | Component>;
default: string;
};
}>> & Readonly<{}>, () => VNode<RendererNode, RendererElement, {
[key: string]: any;
}>, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, PublicProps, {
asChild: boolean;
as: AsTag | Component;
}, true, {}, {}, GlobalComponents, GlobalDirectives, string, {}, any, ComponentProvideOptions, {
P: {};
B: {};
D: {};
C: {};
M: {};
Defaults: {};
}, Readonly<ExtractPropTypes<    {
asChild: {
type: BooleanConstructor;
default: boolean;
};
as: {
type: PropType<AsTag | Component>;
default: string;
};
}>> & Readonly<{}>, () => VNode<RendererNode, RendererElement, {
[key: string]: any;
}>, {}, {}, {}, {
asChild: boolean;
as: AsTag | Component;
}> | null;
}, any>, Readonly<{
    default?: (props: {
        /** Current input values */
        modelValue: string | undefined;
    }) => any;
}> & {
    default?: (props: {
        /** Current input values */
        modelValue: string | undefined;
    }) => any;
}>;

export declare type ListboxFilterEmits = {
    'update:modelValue': [string];
};

export declare interface ListboxFilterProps extends PrimitiveProps {
    /** The controlled value of the filter. Can be binded with with v-model. */
    modelValue?: string;
    /** Focus on element when mounted. */
    autoFocus?: boolean;
    /** When `true`, prevents the user from interacting with item */
    disabled?: boolean;
}

export declare const ListboxGroup: __VLS_WithTemplateSlots_150<DefineComponent<ListboxGroupProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<ListboxGroupProps> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

declare interface ListboxGroupContext {
    id: string;
}

export declare const ListboxGroupLabel: __VLS_WithTemplateSlots_151<DefineComponent<ListboxGroupLabelProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<ListboxGroupLabelProps> & Readonly<{}>, {
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface ListboxGroupLabelProps extends PrimitiveProps {
    for?: string;
}

export declare interface ListboxGroupProps extends PrimitiveProps {
}

export declare const ListboxItem: <T extends AcceptableValue = AcceptableValue>(__VLS_props: NonNullable<Awaited<typeof __VLS_setup>>["props"], __VLS_ctx?: __VLS_PrettifyLocal_6<Pick<NonNullable<Awaited<typeof __VLS_setup>>, "attrs" | "emit" | "slots">>, __VLS_expose?: NonNullable<Awaited<typeof __VLS_setup>>["expose"], __VLS_setup?: Promise<{
    props: __VLS_PrettifyLocal_6<Pick<Partial<{}> & Omit<{
        readonly onSelect?: ((event: ListboxItemSelectEvent<T>) => any) | undefined;
    } & VNodeProps & AllowedComponentProps & ComponentCustomProps, never>, "onSelect"> & ListboxItemProps<T> & Partial<{}>> & PublicProps;
    expose(exposed: ShallowUnwrapRef<    {}>): void;
    attrs: any;
    slots: {
        default?(_: {}): any;
    };
    emit: (evt: "select", event: ListboxItemSelectEvent<T>) => void;
}>) => VNode & {
    __ctx?: Awaited<typeof __VLS_setup>;
};

declare interface ListboxItemContext {
    isSelected: Ref<boolean>;
}

export declare type ListboxItemEmits<T = AcceptableValue> = {
    /** Event handler called when the selecting item. <br> It can be prevented by calling `event.preventDefault`. */
    select: [event: ListboxItemSelectEvent<T>];
};

export declare const ListboxItemIndicator: __VLS_WithTemplateSlots_152<DefineComponent<ListboxItemIndicatorProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<ListboxItemIndicatorProps> & Readonly<{}>, {
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface ListboxItemIndicatorProps extends PrimitiveProps {
}

export declare interface ListboxItemProps<T = AcceptableValue> extends PrimitiveProps {
    /** The value given as data when submitted with a `name`. */
    value: T;
    /** When `true`, prevents the user from interacting with the item. */
    disabled?: boolean;
}

export declare type ListboxItemSelectEvent<T> = CustomEvent<{
    originalEvent: PointerEvent;
    value?: T;
}>;

export declare const ListboxRoot: <T extends AcceptableValue = AcceptableValue>(__VLS_props: NonNullable<Awaited<typeof __VLS_setup>>["props"], __VLS_ctx?: __VLS_PrettifyLocal_7<Pick<NonNullable<Awaited<typeof __VLS_setup>>, "attrs" | "emit" | "slots">>, __VLS_expose?: NonNullable<Awaited<typeof __VLS_setup>>["expose"], __VLS_setup?: Promise<{
    props: __VLS_PrettifyLocal_7<Pick<Partial<{}> & Omit<{
        readonly onLeave?: ((event: Event) => any) | undefined;
        readonly "onUpdate:modelValue"?: ((value: AcceptableValue) => any) | undefined;
        readonly onEntryFocus?: ((event: CustomEvent<any>) => any) | undefined;
        readonly onHighlight?: ((payload: {
            ref: HTMLElement;
            value: AcceptableValue;
        } | undefined) => any) | undefined;
    } & VNodeProps & AllowedComponentProps & ComponentCustomProps, never>, "onUpdate:modelValue" | "onEntryFocus" | "onLeave" | "onHighlight"> & ListboxRootProps<AcceptableValue> & Partial<{}>> & PublicProps;
    expose(exposed: ShallowUnwrapRef<    {
    highlightedElement: Ref<HTMLElement | null, HTMLElement | null>;
    highlightItem: (value: T) => void;
    highlightFirstItem: () => void;
    highlightSelected: (event?: Event) => Promise<void>;
    getItems: (includeDisabledItem?: boolean) => ({
    ref: HTMLElement;
    value?: any;
    } & {
    value: T;
    })[];
    }>): void;
    attrs: any;
    slots: Readonly<{
        default?: (props: {
            /** Current active value */
            modelValue: T | T[] | undefined;
        }) => any;
    }> & {
        default?: (props: {
            /** Current active value */
            modelValue: T | T[] | undefined;
        }) => any;
    };
    emit: ((evt: "leave", event: Event) => void) & ((evt: "update:modelValue", value: AcceptableValue) => void) & ((evt: "entryFocus", event: CustomEvent<any>) => void) & ((evt: "highlight", payload: {
        ref: HTMLElement;
        value: AcceptableValue;
    } | undefined) => void);
}>) => VNode & {
    __ctx?: Awaited<typeof __VLS_setup>;
};

declare type ListboxRootContext<T> = {
    modelValue: Ref<T | Array<T> | undefined>;
    onValueChange: (val: T) => void;
    multiple: Ref<boolean>;
    orientation: Ref<DataOrientation>;
    dir: Ref<Direction>;
    disabled: Ref<boolean>;
    highlightOnHover: Ref<boolean>;
    highlightedElement: Ref<HTMLElement | null>;
    isVirtual: Ref<boolean>;
    virtualFocusHook: EventHook<Event | null | undefined>;
    virtualKeydownHook: EventHook<KeyboardEvent>;
    virtualHighlightHook: EventHook<any>;
    by?: string | ((a: T, b: T) => boolean);
    firstValue?: Ref<T | undefined>;
    selectionBehavior?: Ref<'toggle' | 'replace'>;
    focusable: Ref<boolean>;
    onLeave: (event: Event) => void;
    onEnter: (event: Event) => void;
    changeHighlight: (el: HTMLElement, scrollIntoView?: boolean) => void;
    onKeydownNavigation: (event: KeyboardEvent) => void;
    onKeydownEnter: (event: KeyboardEvent) => void;
    onKeydownTypeAhead: (event: KeyboardEvent) => void;
    onCompositionStart: () => void;
    onCompositionEnd: () => void;
    highlightFirstItem: (event: InputEvent) => void;
};

export declare type ListboxRootEmits<T = AcceptableValue> = {
    /** Event handler called when the value changes. */
    'update:modelValue': [value: T];
    /** Event handler when highlighted element changes. */
    'highlight': [payload: {
        ref: HTMLElement;
        value: T;
    } | undefined];
    /** Event handler called when container is being focused. Can be prevented. */
    'entryFocus': [event: CustomEvent];
    /** Event handler called when the mouse leave the container */
    'leave': [event: Event];
};

export declare interface ListboxRootProps<T = AcceptableValue> extends PrimitiveProps, FormFieldProps {
    /** The controlled value of the listbox. Can be binded with with `v-model`. */
    modelValue?: T | Array<T>;
    /** The value of the listbox when initially rendered. Use when you do not need to control the state of the Listbox */
    defaultValue?: T | Array<T>;
    /** Whether multiple options can be selected or not. */
    multiple?: boolean;
    /** The orientation of the listbox. <br>Mainly so arrow navigation is done accordingly (left & right vs. up & down) */
    orientation?: DataOrientation;
    /** The reading direction of the listbox when applicable. <br> If omitted, inherits globally from `ConfigProvider` or assumes LTR (left-to-right) reading mode. */
    dir?: Direction;
    /** When `true`, prevents the user from interacting with listbox */
    disabled?: boolean;
    /**
     * How multiple selection should behave in the collection.
     * @defaultValue 'toggle'
     */
    selectionBehavior?: 'toggle' | 'replace';
    /** When `true`, hover over item will trigger highlight */
    highlightOnHover?: boolean;
    /** Use this to compare objects by a particular field, or pass your own comparison function for complete control over how objects are compared. */
    by?: string | ((a: T, b: T) => boolean);
}

export declare const ListboxVirtualizer: <T extends AcceptableValue = AcceptableValue>(__VLS_props: NonNullable<Awaited<typeof __VLS_setup>>["props"], __VLS_ctx?: __VLS_PrettifyLocal_8<Pick<NonNullable<Awaited<typeof __VLS_setup>>, "attrs" | "emit" | "slots">>, __VLS_expose?: NonNullable<Awaited<typeof __VLS_setup>>["expose"], __VLS_setup?: Promise<{
    props: __VLS_PrettifyLocal_8<Pick<Partial<{}> & Omit<{} & VNodeProps & AllowedComponentProps & ComponentCustomProps, never>, never> & ListboxVirtualizerProps<T> & Partial<{}>> & PublicProps;
    expose(exposed: ShallowUnwrapRef<    {}>): void;
    attrs: any;
    slots: Readonly<{
        default?: (props: {
            option: T;
            virtualizer: Virtualizer<HTMLElement, Element>;
            virtualItem: VirtualItem;
        }) => any;
    }> & {
        default?: (props: {
            option: T;
            virtualizer: Virtualizer<HTMLElement, Element>;
            virtualItem: VirtualItem;
        }) => any;
    };
    emit: {};
}>) => VNode & {
    __ctx?: Awaited<typeof __VLS_setup>;
};

export declare interface ListboxVirtualizerProps<T extends AcceptableValue = AcceptableValue> {
    /** List of items */
    options: T[];
    /** Number of items rendered outside the visible area */
    overscan?: number;
    /** Estimated size (in px) of each item */
    estimateSize?: number;
    /** Text content for each item to achieve type-ahead feature */
    textContent?: (option: T) => string;
}

declare interface Machine<S> {
    [k: string]: {
        [k: string]: S;
    };
}

declare type MachineEvent<T> = keyof UnionToIntersection<T[keyof T]>;

declare type MachineState<T> = keyof T;

declare type Matcher = (date: DateValue) => boolean;

declare interface MenuArrowProps extends PopperArrowProps {
}

export declare const MenubarArrow: __VLS_WithTemplateSlots_153<DefineComponent<MenubarArrowProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<MenubarArrowProps> & Readonly<{}>, {
width: number;
height: number;
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface MenubarArrowProps extends MenuArrowProps {
}

export declare const MenubarCheckboxItem: __VLS_WithTemplateSlots_154<DefineComponent<MenubarCheckboxItemProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {
select: (event: Event) => any;
"update:modelValue": (payload: boolean) => any;
}, string, PublicProps, Readonly<MenubarCheckboxItemProps> & Readonly<{
onSelect?: ((event: Event) => any) | undefined;
"onUpdate:modelValue"?: ((payload: boolean) => any) | undefined;
}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare type MenubarCheckboxItemEmits = MenuCheckboxItemEmits;

export declare interface MenubarCheckboxItemProps extends MenuCheckboxItemProps {
}

export declare const MenubarContent: __VLS_WithTemplateSlots_155<DefineComponent<MenubarContentProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {
escapeKeyDown: (event: KeyboardEvent) => any;
pointerDownOutside: (event: PointerDownOutsideEvent) => any;
focusOutside: (event: FocusOutsideEvent) => any;
interactOutside: (event: PointerDownOutsideEvent | FocusOutsideEvent) => any;
closeAutoFocus: (event: Event) => any;
}, string, PublicProps, Readonly<MenubarContentProps> & Readonly<{
onEscapeKeyDown?: ((event: KeyboardEvent) => any) | undefined;
onPointerDownOutside?: ((event: PointerDownOutsideEvent) => any) | undefined;
onFocusOutside?: ((event: FocusOutsideEvent) => any) | undefined;
onInteractOutside?: ((event: PointerDownOutsideEvent | FocusOutsideEvent) => any) | undefined;
onCloseAutoFocus?: ((event: Event) => any) | undefined;
}>, {
align: Align;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface MenubarContentProps extends MenuContentProps {
}

export declare const MenubarGroup: __VLS_WithTemplateSlots_156<DefineComponent<MenubarGroupProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<MenubarGroupProps> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface MenubarGroupProps extends MenuGroupProps {
}

export declare const MenubarItem: __VLS_WithTemplateSlots_157<DefineComponent<MenubarItemProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {
select: (event: Event) => any;
}, string, PublicProps, Readonly<MenubarItemProps> & Readonly<{
onSelect?: ((event: Event) => any) | undefined;
}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare type MenubarItemEmits = MenuItemEmits;

export declare const MenubarItemIndicator: __VLS_WithTemplateSlots_158<DefineComponent<MenubarItemIndicatorProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<MenubarItemIndicatorProps> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface MenubarItemIndicatorProps extends MenuItemIndicatorProps {
}

export declare interface MenubarItemProps extends MenuItemProps {
}

export declare const MenubarLabel: __VLS_WithTemplateSlots_159<DefineComponent<MenubarLabelProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<MenubarLabelProps> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface MenubarLabelProps extends MenuLabelProps {
}

export declare const MenubarMenu: __VLS_WithTemplateSlots_160<DefineComponent<MenubarMenuProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<MenubarMenuProps> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

declare type MenubarMenuContext = {
    value: string;
    triggerId: string;
    triggerElement: Ref<HTMLElement | undefined>;
    contentId: string;
    wasKeyboardTriggerOpenRef: Ref<boolean>;
};

export declare interface MenubarMenuProps {
    /**
     * A unique value that associates the item with an active value when the navigation menu is controlled.
     *
     * This prop is managed automatically when uncontrolled.
     */
    value?: string;
}

export declare const MenubarPortal: __VLS_WithTemplateSlots_161<DefineComponent<MenubarPortalProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<MenubarPortalProps> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface MenubarPortalProps extends MenuPortalProps {
}

export declare const MenubarRadioGroup: __VLS_WithTemplateSlots_162<DefineComponent<MenubarRadioGroupProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {
"update:modelValue": (payload: string) => any;
}, string, PublicProps, Readonly<MenubarRadioGroupProps> & Readonly<{
"onUpdate:modelValue"?: ((payload: string) => any) | undefined;
}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare type MenubarRadioGroupEmits = MenuRadioGroupEmits;

export declare interface MenubarRadioGroupProps extends MenuRadioGroupProps {
}

export declare const MenubarRadioItem: __VLS_WithTemplateSlots_163<DefineComponent<MenuRadioItemProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {
select: (event: Event) => any;
}, string, PublicProps, Readonly<MenuRadioItemProps> & Readonly<{
onSelect?: ((event: Event) => any) | undefined;
}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare type MenubarRadioItemEmits = MenuRadioItemEmits;

export declare interface MenubarRadioItemProps extends MenuRadioItemProps {
}

export declare const MenubarRoot: __VLS_WithTemplateSlots_164<DefineComponent<MenubarRootProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {
"update:modelValue": (value: boolean) => any;
}, string, PublicProps, Readonly<MenubarRootProps> & Readonly<{
"onUpdate:modelValue"?: ((value: boolean) => any) | undefined;
}>, {
loop: boolean;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, Readonly<{
    default?: (props: {
        /** Current input values */
        modelValue: string;
    }) => any;
}> & {
    default?: (props: {
        /** Current input values */
        modelValue: string;
    }) => any;
}>;

declare interface MenubarRootContext {
    modelValue: Ref<string>;
    dir: Ref<Direction>;
    loop: Ref<boolean>;
    onMenuOpen: (value: string) => void;
    onMenuClose: () => void;
    onMenuToggle: (value: string) => void;
}

export declare type MenubarRootEmits = {
    /** Event handler called when the value changes. */
    'update:modelValue': [value: boolean];
};

export declare interface MenubarRootProps {
    /** The controlled value of the menu to open. Can be used as `v-model`. */
    modelValue?: string;
    /** The value of the menu that should be open when initially rendered. Use when you do not need to control the value state. */
    defaultValue?: string;
    /**
     * The reading direction of the combobox when applicable.
     *
     *  If omitted, inherits globally from `ConfigProvider` or assumes LTR (left-to-right) reading mode.
     */
    dir?: Direction;
    /** When `true`, keyboard navigation will loop from last item to first, and vice versa. */
    loop?: boolean;
}

export declare const MenubarSeparator: __VLS_WithTemplateSlots_165<DefineComponent<MenubarSeparatorProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<MenubarSeparatorProps> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface MenubarSeparatorProps extends MenuSeparatorProps {
}

export declare const MenubarSub: __VLS_WithTemplateSlots_166<DefineComponent<MenubarSubProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {
"update:open": (payload: boolean) => any;
}, string, PublicProps, Readonly<MenubarSubProps> & Readonly<{
"onUpdate:open"?: ((payload: boolean) => any) | undefined;
}>, {
open: boolean;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, Readonly<{
    default?: (props: {
        /** Current open state */
        open: boolean;
    }) => any;
}> & {
    default?: (props: {
        /** Current open state */
        open: boolean;
    }) => any;
}>;

export declare const MenubarSubContent: __VLS_WithTemplateSlots_167<DefineComponent<MenubarSubContentProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {
escapeKeyDown: (event: KeyboardEvent) => any;
pointerDownOutside: (event: PointerDownOutsideEvent) => any;
focusOutside: (event: FocusOutsideEvent) => any;
interactOutside: (event: PointerDownOutsideEvent | FocusOutsideEvent) => any;
openAutoFocus: (event: Event) => any;
closeAutoFocus: (event: Event) => any;
entryFocus: (event: Event) => any;
}, string, PublicProps, Readonly<MenubarSubContentProps> & Readonly<{
onEscapeKeyDown?: ((event: KeyboardEvent) => any) | undefined;
onPointerDownOutside?: ((event: PointerDownOutsideEvent) => any) | undefined;
onFocusOutside?: ((event: FocusOutsideEvent) => any) | undefined;
onInteractOutside?: ((event: PointerDownOutsideEvent | FocusOutsideEvent) => any) | undefined;
onOpenAutoFocus?: ((event: Event) => any) | undefined;
onCloseAutoFocus?: ((event: Event) => any) | undefined;
onEntryFocus?: ((event: Event) => any) | undefined;
}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare type MenubarSubContentEmits = MenuSubContentEmits;

export declare interface MenubarSubContentProps extends MenuSubContentProps {
}

export declare type MenubarSubEmits = MenuSubEmits;

export declare interface MenubarSubProps extends MenuSubProps {
    /** The open state of the submenu when it is initially rendered. Use when you do not need to control its open state. */
    defaultOpen?: boolean;
}

export declare const MenubarSubTrigger: __VLS_WithTemplateSlots_168<DefineComponent<MenubarSubTriggerProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<MenubarSubTriggerProps> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface MenubarSubTriggerProps extends MenuSubTriggerProps {
}

export declare const MenubarTrigger: __VLS_WithTemplateSlots_169<DefineComponent<MenubarTriggerProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<MenubarTriggerProps> & Readonly<{}>, {
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface MenubarTriggerProps extends PrimitiveProps {
    /** When `true`, prevents the user from interacting with item */
    disabled?: boolean;
}

declare type MenuCheckboxItemEmits = MenuItemEmits & {
    /** Event handler called when the checked state changes. */
    'update:modelValue': [payload: boolean];
};

declare interface MenuCheckboxItemProps extends MenuItemProps {
    /** The controlled checked state of the item. Can be used as `v-model`. */
    modelValue?: CheckedState;
}

declare type MenuContentEmits = Omit<MenuContentImplEmits, 'entryFocus' | 'openAutoFocus'>;

declare type MenuContentImplEmits = DismissableLayerEmits & Omit<RovingFocusGroupEmits, 'update:currentTabStopId'> & {
    openAutoFocus: [event: Event];
    /**
     * Event handler called when auto-focusing on close.
     * Can be prevented.
     */
    closeAutoFocus: [event: Event];
};

declare interface MenuContentImplPrivateProps {
    /**
     * When `true`, hover/focus/click interactions will be disabled on elements outside
     * the `DismissableLayer`. Users will need to click twice on outside elements to
     * interact with them: once to close the `DismissableLayer`, and again to trigger the element.
     */
    disableOutsidePointerEvents?: DismissableLayerProps['disableOutsidePointerEvents'];
    /**
     * Whether scrolling outside the `MenuContent` should be prevented
     * @defaultValue false
     */
    disableOutsideScroll?: boolean;
    /**
     * Whether focus should be trapped within the `MenuContent`
     * @defaultValue also
     */
    trapFocus?: FocusScopeProps['trapped'];
}

declare interface MenuContentImplProps extends MenuContentImplPrivateProps, Omit<PopperContentProps, 'dir'> {
    /**
     * When `true`, keyboard navigation will loop from last item to first, and vice versa.
     * @defaultValue false
     */
    loop?: boolean;
}

declare interface MenuContentProps extends MenuRootContentTypeProps {
    /**
     * Used to force mounting when more control is needed. Useful when
     * controlling animation with Vue animation libraries.
     */
    forceMount?: boolean;
}

declare type MenuEmits = {
    'update:open': [payload: boolean];
};

declare interface MenuGroupProps extends PrimitiveProps {
}

declare type MenuItemEmits = {
    /**
     * Event handler called when the user selects an item (via mouse or keyboard). <br>
     *  Calling `event.preventDefault` in this handler will prevent the menu from closing when selecting that item.
     */
    select: [event: Event];
};

declare interface MenuItemImplProps extends PrimitiveProps {
    /** When `true`, prevents the user from interacting with the item. */
    disabled?: boolean;
    /**
     * Optional text used for typeahead purposes. By default the typeahead behavior will use the `.textContent` of the item. <br>
     *  Use this when the content is complex, or you have non-textual content inside.
     */
    textValue?: string;
}

declare interface MenuItemIndicatorProps extends PrimitiveProps {
    /**
     * Used to force mounting when more control is needed. Useful when
     * controlling animation with Vue animation libraries.
     */
    forceMount?: boolean;
}

declare interface MenuItemProps extends MenuItemImplProps {
}

declare interface MenuLabelProps extends PrimitiveProps {
}

declare interface MenuPortalProps extends TeleportProps {
}

declare interface MenuProps {
    /** The controlled open state of the menu. Can be used as `v-model:open`. */
    open?: boolean;
    /**
     * The reading direction of the combobox when applicable.
     *
     * If omitted, inherits globally from `ConfigProvider` or assumes LTR (left-to-right) reading mode.
     */
    dir?: Direction_3;
    /**
     * The modality of the dropdown menu.
     *
     * When set to `true`, interaction with outside elements will be disabled and only menu content will be visible to screen readers.
     */
    modal?: boolean;
}

declare type MenuRadioGroupEmits = {
    /** Event handler called when the value changes. */
    'update:modelValue': [payload: string];
};

declare interface MenuRadioGroupProps extends MenuGroupProps {
    /** The value of the selected item in the group. */
    modelValue?: string;
}

declare type MenuRadioItemEmits = MenuItemEmits;

declare interface MenuRadioItemProps extends MenuItemProps {
    /** The unique value of the item. */
    value: string;
}

declare interface MenuRootContentTypeProps extends Omit<MenuContentImplProps, 'disableOutsidePointerEvents' | 'disableOutsideScroll' | 'trapFocus'> {
}

declare interface MenuSeparatorProps extends PrimitiveProps {
}

declare type MenuSubContentEmits = MenuContentImplEmits;

declare interface MenuSubContentProps extends Omit<MenuContentImplProps, 'disableOutsidePointerEvents' | 'disableOutsideScroll' | 'trapFocus' | 'side' | 'align'> {
    /**
     * Used to force mounting when more control is needed. Useful when
     * controlling animation with Vue animation libraries.
     */
    forceMount?: boolean;
}

declare type MenuSubEmits = {
    /** Event handler called when the open state of the submenu changes. */
    'update:open': [payload: boolean];
};

declare interface MenuSubProps {
    /** The controlled open state of the menu. Can be used as `v-model:open`. */
    open?: boolean;
}

declare interface MenuSubTriggerProps extends MenuItemImplProps {
}

declare interface MountingOptions<Props> {
    /**
     * Default props for the component
     */
    props?: (RawProps & Props) | ({} extends Props ? null : never) | ((attrs: Record<string, any>) => (RawProps & Props));
    /**
     * Pass attributes into the component
     */
    attrs?: Record<string, unknown>;
}

export declare const NavigationMenuContent: __VLS_WithTemplateSlots_170<DefineComponent<NavigationMenuContentProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {
escapeKeyDown: (event: KeyboardEvent) => any;
pointerDownOutside: (event: PointerDownOutsideEvent) => any;
focusOutside: (event: FocusOutsideEvent) => any;
interactOutside: (event: PointerDownOutsideEvent | FocusOutsideEvent) => any;
}, string, PublicProps, Readonly<NavigationMenuContentProps> & Readonly<{
onEscapeKeyDown?: ((event: KeyboardEvent) => any) | undefined;
onPointerDownOutside?: ((event: PointerDownOutsideEvent) => any) | undefined;
onFocusOutside?: ((event: FocusOutsideEvent) => any) | undefined;
onInteractOutside?: ((event: PointerDownOutsideEvent | FocusOutsideEvent) => any) | undefined;
}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare type NavigationMenuContentEmits = NavigationMenuContentImplEmits;

declare type NavigationMenuContentImplEmits = DismissableLayerEmits;

declare interface NavigationMenuContentImplProps extends DismissableLayerProps {
}

export declare interface NavigationMenuContentProps extends NavigationMenuContentImplProps {
    /**
     * Used to force mounting when more control is needed. Useful when
     * controlling animation with Vue animation libraries.
     */
    forceMount?: boolean;
}

declare interface NavigationMenuContext {
    isRootMenu: boolean;
    modelValue: Ref<string>;
    previousValue: Ref<string>;
    baseId: string;
    dir: Ref<Direction_4>;
    orientation: Orientation_2;
    disableClickTrigger: Ref<boolean>;
    disableHoverTrigger: Ref<boolean>;
    unmountOnHide: Ref<boolean>;
    rootNavigationMenu: Ref<HTMLElement | undefined>;
    activeTrigger: Ref<HTMLElement | undefined>;
    indicatorTrack: Ref<HTMLElement | undefined>;
    onIndicatorTrackChange: (indicatorTrack: HTMLElement | undefined) => void;
    viewport: Ref<HTMLElement | undefined>;
    onViewportChange: (viewport: HTMLElement | undefined) => void;
    onTriggerEnter: (itemValue: string) => void;
    onTriggerLeave: () => void;
    onContentEnter: (itemValue: string) => void;
    onContentLeave: () => void;
    onItemSelect: (itemValue: string) => void;
    onItemDismiss: () => void;
}

export declare const NavigationMenuIndicator: __VLS_WithTemplateSlots_171<DefineComponent<NavigationMenuIndicatorProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<NavigationMenuIndicatorProps> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface NavigationMenuIndicatorProps extends PrimitiveProps {
    /**
     * Used to force mounting when more control is needed. Useful when
     * controlling animation with Vue animation libraries.
     */
    forceMount?: boolean;
}

export declare const NavigationMenuItem: __VLS_WithTemplateSlots_172<DefineComponent<NavigationMenuItemProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<NavigationMenuItemProps> & Readonly<{}>, {
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

declare type NavigationMenuItemContext = {
    value: string;
    contentId: string;
    triggerRef: Ref<HTMLElement | undefined>;
    focusProxyRef: Ref<HTMLElement | undefined>;
    wasEscapeCloseRef: Ref<boolean>;
    onEntryKeyDown: () => void;
    onFocusProxyEnter: (side: 'start' | 'end') => void;
    onContentFocusOutside: () => void;
    onRootContentClose: () => void;
};

export declare interface NavigationMenuItemProps extends PrimitiveProps {
    /**
     * A unique value that associates the item with an active value when the navigation menu is controlled.
     *
     *  This prop is managed automatically when uncontrolled.
     */
    value?: string;
}

export declare const NavigationMenuLink: __VLS_WithTemplateSlots_173<DefineComponent<NavigationMenuLinkProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {
select: (payload: CustomEvent<{
originalEvent: Event;
}>) => any;
}, string, PublicProps, Readonly<NavigationMenuLinkProps> & Readonly<{
onSelect?: ((payload: CustomEvent<{
originalEvent: Event;
}>) => any) | undefined;
}>, {
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare type NavigationMenuLinkEmits = {
    /**
     * Event handler called when the user selects a link (via mouse or keyboard).
     *
     * Calling `event.preventDefault` in this handler will prevent the navigation menu from closing when selecting that link.
     */
    select: [payload: CustomEvent<{
        originalEvent: Event;
    }>];
};

export declare interface NavigationMenuLinkProps extends PrimitiveProps {
    /** Used to identify the link as the currently active page. */
    active?: boolean;
}

export declare const NavigationMenuList: __VLS_WithTemplateSlots_174<DefineComponent<NavigationMenuListProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<NavigationMenuListProps> & Readonly<{}>, {
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface NavigationMenuListProps extends PrimitiveProps {
}

export declare const NavigationMenuRoot: __VLS_WithTemplateSlots_175<DefineComponent<NavigationMenuRootProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {
"update:modelValue": (value: string) => any;
}, string, PublicProps, Readonly<NavigationMenuRootProps> & Readonly<{
"onUpdate:modelValue"?: ((value: string) => any) | undefined;
}>, {
as: AsTag | Component;
unmountOnHide: boolean;
modelValue: string;
orientation: Orientation_2;
delayDuration: number;
skipDelayDuration: number;
disableClickTrigger: boolean;
disableHoverTrigger: boolean;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, Readonly<{
    default?: (props: {
        /** Current input values */
        modelValue: string;
    }) => any;
}> & {
    default?: (props: {
        /** Current input values */
        modelValue: string;
    }) => any;
}>;

export declare type NavigationMenuRootEmits = {
    /** Event handler called when the value changes. */
    'update:modelValue': [value: string];
};

export declare interface NavigationMenuRootProps extends PrimitiveProps {
    /** The controlled value of the menu item to activate. Can be used as `v-model`. */
    modelValue?: string;
    /**
     * The value of the menu item that should be active when initially rendered.
     *
     * Use when you do not need to control the value state.
     */
    defaultValue?: string;
    /**
     * The reading direction of the combobox when applicable.
     *
     *  If omitted, inherits globally from `ConfigProvider` or assumes LTR (left-to-right) reading mode.
     */
    dir?: Direction_4;
    /** The orientation of the menu. */
    orientation?: Orientation_2;
    /**
     * The duration from when the pointer enters the trigger until the tooltip gets opened.
     * @defaultValue 200
     */
    delayDuration?: number;
    /**
     * How much time a user has to enter another trigger without incurring a delay again.
     * @defaultValue 300
     */
    skipDelayDuration?: number;
    /**
     * If `true`, menu cannot be open by click on trigger
     * @defaultValue false
     */
    disableClickTrigger?: boolean;
    /**
     * If `true`, menu cannot be open by hover on trigger
     * @defaultValue false
     */
    disableHoverTrigger?: boolean;
    /**
     * If `true`, menu will not close during pointer leave event
     * @defaultValue false
     */
    disablePointerLeaveClose?: boolean;
    /**
     * When `true`, the element will be unmounted on closed state.
     *
     * @defaultValue `true`
     */
    unmountOnHide?: boolean;
}

export declare const NavigationMenuSub: __VLS_WithTemplateSlots_176<DefineComponent<NavigationMenuSubProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {
"update:modelValue": (value: string) => any;
}, string, PublicProps, Readonly<NavigationMenuSubProps> & Readonly<{
"onUpdate:modelValue"?: ((value: string) => any) | undefined;
}>, {
orientation: Orientation_2;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, Readonly<{
    default?: (props: {
        /** Current input values */
        modelValue: string;
    }) => any;
}> & {
    default?: (props: {
        /** Current input values */
        modelValue: string;
    }) => any;
}>;

export declare type NavigationMenuSubEmits = {
    /** Event handler called when the value changes. */
    'update:modelValue': [value: string];
};

export declare interface NavigationMenuSubProps extends PrimitiveProps {
    /** The controlled value of the sub menu item to activate. Can be used as `v-model`. */
    modelValue?: string;
    /**
     * The value of the menu item that should be active when initially rendered.
     *
     * Use when you do not need to control the value state.
     */
    defaultValue?: string;
    /** The orientation of the menu. */
    orientation?: Orientation_2;
}

export declare const NavigationMenuTrigger: __VLS_WithTemplateSlots_177<DefineComponent<NavigationMenuTriggerProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<NavigationMenuTriggerProps> & Readonly<{}>, {
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface NavigationMenuTriggerProps extends PrimitiveProps {
    /** When `true`, prevents the user from interacting with item */
    disabled?: boolean;
}

export declare const NavigationMenuViewport: __VLS_WithTemplateSlots_178<DefineComponent<NavigationMenuViewportProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<NavigationMenuViewportProps> & Readonly<{}>, {
align: "start" | "center" | "end";
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface NavigationMenuViewportProps extends PrimitiveProps {
    /**
     * Used to force mounting when more control is needed. Useful when
     * controlling animation with Vue animation libraries.
     */
    forceMount?: boolean;
    /**
     * Placement of the viewport for css variables `(--reka-navigation-menu-viewport-left, --reka-navigation-menu-viewport-top)`.
     * @defaultValue 'center'
     */
    align?: 'start' | 'center' | 'end';
}

declare const NON_EDITABLE_SEGMENT_PARTS: readonly ["literal", "timeZoneName"];

declare type NonEditableSegmentPart = (typeof NON_EDITABLE_SEGMENT_PARTS)[number];

export declare const NumberFieldDecrement: __VLS_WithTemplateSlots_179<DefineComponent<NumberFieldDecrementProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<NumberFieldDecrementProps> & Readonly<{}>, {
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {
primitiveElement: CreateComponentPublicInstanceWithMixins<Readonly<ExtractPropTypes<    {
asChild: {
type: BooleanConstructor;
default: boolean;
};
as: {
type: PropType<AsTag | Component>;
default: string;
};
}>> & Readonly<{}>, () => VNode<RendererNode, RendererElement, {
[key: string]: any;
}>, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, PublicProps, {
asChild: boolean;
as: AsTag | Component;
}, true, {}, {}, GlobalComponents, GlobalDirectives, string, {}, any, ComponentProvideOptions, {
P: {};
B: {};
D: {};
C: {};
M: {};
Defaults: {};
}, Readonly<ExtractPropTypes<    {
asChild: {
type: BooleanConstructor;
default: boolean;
};
as: {
type: PropType<AsTag | Component>;
default: string;
};
}>> & Readonly<{}>, () => VNode<RendererNode, RendererElement, {
[key: string]: any;
}>, {}, {}, {}, {
asChild: boolean;
as: AsTag | Component;
}> | null;
}, any>, {
    default?(_: {}): any;
}>;

export declare interface NumberFieldDecrementProps extends PrimitiveProps {
    disabled?: boolean;
}

export declare const NumberFieldIncrement: __VLS_WithTemplateSlots_180<DefineComponent<NumberFieldIncrementProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<NumberFieldIncrementProps> & Readonly<{}>, {
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {
primitiveElement: CreateComponentPublicInstanceWithMixins<Readonly<ExtractPropTypes<    {
asChild: {
type: BooleanConstructor;
default: boolean;
};
as: {
type: PropType<AsTag | Component>;
default: string;
};
}>> & Readonly<{}>, () => VNode<RendererNode, RendererElement, {
[key: string]: any;
}>, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, PublicProps, {
asChild: boolean;
as: AsTag | Component;
}, true, {}, {}, GlobalComponents, GlobalDirectives, string, {}, any, ComponentProvideOptions, {
P: {};
B: {};
D: {};
C: {};
M: {};
Defaults: {};
}, Readonly<ExtractPropTypes<    {
asChild: {
type: BooleanConstructor;
default: boolean;
};
as: {
type: PropType<AsTag | Component>;
default: string;
};
}>> & Readonly<{}>, () => VNode<RendererNode, RendererElement, {
[key: string]: any;
}>, {}, {}, {}, {
asChild: boolean;
as: AsTag | Component;
}> | null;
}, any>, {
    default?(_: {}): any;
}>;

export declare interface NumberFieldIncrementProps extends PrimitiveProps {
    disabled?: boolean;
}

export declare const NumberFieldInput: __VLS_WithTemplateSlots_181<DefineComponent<NumberFieldInputProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<NumberFieldInputProps> & Readonly<{}>, {
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {
primitiveElement: CreateComponentPublicInstanceWithMixins<Readonly<ExtractPropTypes<    {
asChild: {
type: BooleanConstructor;
default: boolean;
};
as: {
type: PropType<AsTag | Component>;
default: string;
};
}>> & Readonly<{}>, () => VNode<RendererNode, RendererElement, {
[key: string]: any;
}>, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, PublicProps, {
asChild: boolean;
as: AsTag | Component;
}, true, {}, {}, GlobalComponents, GlobalDirectives, string, {}, any, ComponentProvideOptions, {
P: {};
B: {};
D: {};
C: {};
M: {};
Defaults: {};
}, Readonly<ExtractPropTypes<    {
asChild: {
type: BooleanConstructor;
default: boolean;
};
as: {
type: PropType<AsTag | Component>;
default: string;
};
}>> & Readonly<{}>, () => VNode<RendererNode, RendererElement, {
[key: string]: any;
}>, {}, {}, {}, {
asChild: boolean;
as: AsTag | Component;
}> | null;
}, any>, {
    default?(_: {}): any;
}>;

export declare interface NumberFieldInputProps extends PrimitiveProps {
}

export declare const NumberFieldRoot: __VLS_WithTemplateSlots_182<DefineComponent<NumberFieldRootProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {
"update:modelValue": (val: number) => any;
}, string, PublicProps, Readonly<NumberFieldRootProps> & Readonly<{
"onUpdate:modelValue"?: ((val: number) => any) | undefined;
}>, {
defaultValue: number;
as: AsTag | Component;
step: number;
stepSnapping: boolean;
}, {}, {}, {}, string, ComponentProvideOptions, false, {
primitiveElement: CreateComponentPublicInstanceWithMixins<Readonly<ExtractPropTypes<    {
asChild: {
type: BooleanConstructor;
default: boolean;
};
as: {
type: PropType<AsTag | Component>;
default: string;
};
}>> & Readonly<{}>, () => VNode<RendererNode, RendererElement, {
[key: string]: any;
}>, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, PublicProps, {
asChild: boolean;
as: AsTag | Component;
}, true, {}, {}, GlobalComponents, GlobalDirectives, string, {}, any, ComponentProvideOptions, {
P: {};
B: {};
D: {};
C: {};
M: {};
Defaults: {};
}, Readonly<ExtractPropTypes<    {
asChild: {
type: BooleanConstructor;
default: boolean;
};
as: {
type: PropType<AsTag | Component>;
default: string;
};
}>> & Readonly<{}>, () => VNode<RendererNode, RendererElement, {
[key: string]: any;
}>, {}, {}, {}, {
asChild: boolean;
as: AsTag | Component;
}> | null;
}, any>, {
    default?(_: {
        modelValue: number | undefined;
        textValue: string;
    }): any;
}>;

declare interface NumberFieldRootContext {
    modelValue: Ref<number | undefined>;
    handleIncrease: (multiplier?: number) => void;
    handleDecrease: (multiplier?: number) => void;
    handleMinMaxValue: (type: 'min' | 'max') => void;
    inputEl: Ref<HTMLInputElement | undefined>;
    onInputElement: (el: HTMLInputElement) => void;
    inputMode: Ref<HTMLAttributes['inputmode']>;
    textValue: Ref<string>;
    validate: (val: string) => boolean;
    applyInputValue: (val: string) => void;
    disabled: Ref<boolean>;
    disableWheelChange: Ref<boolean>;
    invertWheelChange: Ref<boolean>;
    max: Ref<number | undefined>;
    min: Ref<number | undefined>;
    isDecreaseDisabled: Ref<boolean>;
    isIncreaseDisabled: Ref<boolean>;
    id: Ref<string | undefined>;
}

export declare type NumberFieldRootEmits = {
    'update:modelValue': [val: number];
};

export declare interface NumberFieldRootProps extends PrimitiveProps, FormFieldProps {
    defaultValue?: number;
    modelValue?: number | null;
    /** The smallest value allowed for the input. */
    min?: number;
    /** The largest value allowed for the input. */
    max?: number;
    /** The amount that the input value changes with each increment or decrement "tick". */
    step?: number;
    /** When `false`, prevents the value from snapping to the nearest increment of the step value */
    stepSnapping?: boolean;
    /** Formatting options for the value displayed in the number field. This also affects what characters are allowed to be typed by the user. */
    formatOptions?: Intl.NumberFormatOptions;
    /** The locale to use for formatting dates */
    locale?: string;
    /** When `true`, prevents the user from interacting with the Number Field. */
    disabled?: boolean;
    /** When `true`, prevents the value from changing on wheel scroll. */
    disableWheelChange?: boolean;
    /** When `true`, inverts the direction of the wheel change. */
    invertWheelChange?: boolean;
    /** Id of the element */
    id?: string;
}

declare type Orientation = 'horizontal' | 'vertical';

declare type Orientation_2 = 'vertical' | 'horizontal';

export declare const PaginationEllipsis: __VLS_WithTemplateSlots_183<DefineComponent<PaginationEllipsisProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<PaginationEllipsisProps> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface PaginationEllipsisProps extends PrimitiveProps {
}

export declare const PaginationFirst: __VLS_WithTemplateSlots_184<DefineComponent<PaginationFirstProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<PaginationFirstProps> & Readonly<{}>, {
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface PaginationFirstProps extends PrimitiveProps {
}

export declare const PaginationLast: __VLS_WithTemplateSlots_185<DefineComponent<PaginationLastProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<PaginationLastProps> & Readonly<{}>, {
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface PaginationLastProps extends PrimitiveProps {
}

export declare const PaginationList: __VLS_WithTemplateSlots_186<DefineComponent<PaginationListProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<PaginationListProps> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, Readonly<{
    default?: (props: {
        /** Pages item */
        items: ({
            type: "ellipsis";
        } | {
            type: "page";
            value: number;
        })[];
    }) => any;
}> & {
    default?: (props: {
        /** Pages item */
        items: ({
            type: "ellipsis";
        } | {
            type: "page";
            value: number;
        })[];
    }) => any;
}>;

export declare const PaginationListItem: __VLS_WithTemplateSlots_187<DefineComponent<PaginationListItemProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<PaginationListItemProps> & Readonly<{}>, {
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface PaginationListItemProps extends PrimitiveProps {
    /** Value for the page */
    value: number;
}

export declare interface PaginationListProps extends PrimitiveProps {
}

export declare const PaginationNext: __VLS_WithTemplateSlots_188<DefineComponent<PaginationNextProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<PaginationNextProps> & Readonly<{}>, {
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface PaginationNextProps extends PrimitiveProps {
}

export declare const PaginationPrev: __VLS_WithTemplateSlots_189<DefineComponent<PaginationPrevProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<PaginationPrevProps> & Readonly<{}>, {
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface PaginationPrevProps extends PrimitiveProps {
}

export declare const PaginationRoot: __VLS_WithTemplateSlots_190<DefineComponent<PaginationRootProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {
"update:page": (value: number) => any;
}, string, PublicProps, Readonly<PaginationRootProps> & Readonly<{
"onUpdate:page"?: ((value: number) => any) | undefined;
}>, {
as: AsTag | Component;
defaultPage: number;
total: number;
siblingCount: number;
showEdges: boolean;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, Readonly<{
    default?: (props: {
        /** Current page state */
        page: number;
        /** Number of pages */
        pageCount: number;
    }) => any;
}> & {
    default?: (props: {
        /** Current page state */
        page: number;
        /** Number of pages */
        pageCount: number;
    }) => any;
}>;

declare type PaginationRootContext = {
    page: Ref<number>;
    onPageChange: (value: number) => void;
    pageCount: Ref<number>;
    siblingCount: Ref<number>;
    disabled: Ref<boolean>;
    showEdges: Ref<boolean>;
};

export declare type PaginationRootEmits = {
    /** Event handler called when the page value changes */
    'update:page': [value: number];
};

export declare interface PaginationRootProps extends PrimitiveProps {
    /** The controlled value of the current page. Can be binded as `v-model:page`. */
    page?: number;
    /**
     * The value of the page that should be active when initially rendered.
     *
     * Use when you do not need to control the value state.
     */
    defaultPage?: number;
    /** Number of items per page */
    itemsPerPage: number;
    /** Number of items in your list */
    total?: number;
    /** Number of sibling should be shown around the current page */
    siblingCount?: number;
    /** When `true`, prevents the user from interacting with item */
    disabled?: boolean;
    /** When `true`, always show first page, last page, and ellipsis */
    showEdges?: boolean;
}

declare type PanelCallbacks = {
    onCollapse?: PanelOnCollapse;
    onExpand?: PanelOnExpand;
    onResize?: PanelOnResize;
};

declare type PanelConstraints = {
    collapsedSize?: number | undefined;
    collapsible?: boolean | undefined;
    defaultSize?: number | undefined;
    /** Panel id (unique within group); falls back to useId when not provided */
    maxSize?: number | undefined;
    minSize?: number | undefined;
};

declare type PanelData = {
    callbacks: PanelCallbacks;
    constraints: PanelConstraints;
    id: string;
    idIsFromProps: boolean;
    order: number | undefined;
};

declare type PanelGroupContext = {
    direction: Ref<Direction_6>;
    dragState: DragState | null;
    groupId: string;
    reevaluatePanelConstraints: (panelData: PanelData, prevConstraints: PanelConstraints) => void;
    registerPanel: (panelData: PanelData) => void;
    registerResizeHandle: (dragHandleId: string) => ResizeHandler;
    resizePanel: (panelData: PanelData, size: number) => void;
    startDragging: (dragHandleId: string, event: ResizeEvent) => void;
    stopDragging: () => void;
    unregisterPanel: (panelData: PanelData) => void;
    panelGroupElement: Ref<ParentNode | null>;
    collapsePanel: (panelData: PanelData) => void;
    expandPanel: (panelData: PanelData) => void;
    isPanelCollapsed: (panelData: PanelData) => boolean;
    isPanelExpanded: (panelData: PanelData) => boolean;
    getPanelSize: (panelData: PanelData) => number;
    getPanelStyle: (panelData: PanelData, defaultSize: number | undefined) => CSSProperties;
};

declare type PanelGroupStorage = {
    getItem: (name: string) => string | null;
    setItem: (name: string, value: string) => void;
};

declare type PanelOnCollapse = () => void;

declare type PanelOnExpand = () => void;

declare type PanelOnResize = (size: number, prevSize: number | undefined) => void;

export declare const PinInputInput: __VLS_WithTemplateSlots_191<DefineComponent<PinInputInputProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<PinInputInputProps> & Readonly<{}>, {
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {
primitiveElement: CreateComponentPublicInstanceWithMixins<Readonly<ExtractPropTypes<    {
asChild: {
type: BooleanConstructor;
default: boolean;
};
as: {
type: PropType<AsTag | Component>;
default: string;
};
}>> & Readonly<{}>, () => VNode<RendererNode, RendererElement, {
[key: string]: any;
}>, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, PublicProps, {
asChild: boolean;
as: AsTag | Component;
}, true, {}, {}, GlobalComponents, GlobalDirectives, string, {}, any, ComponentProvideOptions, {
P: {};
B: {};
D: {};
C: {};
M: {};
Defaults: {};
}, Readonly<ExtractPropTypes<    {
asChild: {
type: BooleanConstructor;
default: boolean;
};
as: {
type: PropType<AsTag | Component>;
default: string;
};
}>> & Readonly<{}>, () => VNode<RendererNode, RendererElement, {
[key: string]: any;
}>, {}, {}, {}, {
asChild: boolean;
as: AsTag | Component;
}> | null;
}, any>, {
    default?(_: {}): any;
}>;

export declare interface PinInputInputProps extends PrimitiveProps {
    /** Position of the value this input binds to. */
    index: number;
    /** When `true`, prevents the user from interacting with the pin input */
    disabled?: boolean;
}

export declare const PinInputRoot: <Type extends PinInputType = "text">(__VLS_props: NonNullable<Awaited<typeof __VLS_setup>>["props"], __VLS_ctx?: __VLS_PrettifyLocal_9<Pick<NonNullable<Awaited<typeof __VLS_setup>>, "attrs" | "emit" | "slots">>, __VLS_expose?: NonNullable<Awaited<typeof __VLS_setup>>["expose"], __VLS_setup?: Promise<{
    props: __VLS_PrettifyLocal_9<Pick<Partial<{}> & Omit<{
        readonly "onUpdate:modelValue"?: ((value: PinInputValue<Type>) => any) | undefined;
        readonly onComplete?: ((value: PinInputValue<Type>) => any) | undefined;
    } & VNodeProps & AllowedComponentProps & ComponentCustomProps, never>, "onUpdate:modelValue" | "onComplete"> & PinInputRootProps<Type> & Partial<{}>> & PublicProps;
    expose(exposed: ShallowUnwrapRef<    {}>): void;
    attrs: any;
    slots: Readonly<{
        default?: (props: {
            /** Current input values */
            modelValue: PinInputValue<Type>;
        }) => any;
    }> & {
        default?: (props: {
            /** Current input values */
            modelValue: PinInputValue<Type>;
        }) => any;
    };
    emit: ((evt: "update:modelValue", value: PinInputValue<Type>) => void) & ((evt: "complete", value: PinInputValue<Type>) => void);
}>) => VNode & {
    __ctx?: Awaited<typeof __VLS_setup>;
};

declare interface PinInputRootContext<Type extends PinInputType = 'text'> {
    modelValue: Ref<PinInputValue<Type>>;
    currentModelValue: ComputedRef<PinInputValue<Type>>;
    mask: Ref<boolean>;
    otp: Ref<boolean>;
    placeholder: Ref<string>;
    type: Ref<PinInputType>;
    dir: Ref<Direction>;
    disabled: Ref<boolean>;
    isCompleted: ComputedRef<boolean>;
    inputElements?: Ref<Set<HTMLInputElement>>;
    onInputElementChange: (el: HTMLInputElement) => void;
}

export declare type PinInputRootEmits<Type extends PinInputType = 'text'> = {
    'update:modelValue': [value: PinInputValue<Type>];
    'complete': [value: PinInputValue<Type>];
};

export declare interface PinInputRootProps<Type extends PinInputType = 'text'> extends PrimitiveProps, FormFieldProps {
    /** The controlled checked state of the pin input. Can be binded as `v-model`. */
    modelValue?: PinInputValue<Type> | null;
    /** The default value of the pin inputs when it is initially rendered. Use when you do not need to control its checked state. */
    defaultValue?: PinInputValue<Type>[];
    /** The placeholder character to use for empty pin-inputs. */
    placeholder?: string;
    /** When `true`, pin inputs will be treated as password. */
    mask?: boolean;
    /** When `true`, mobile devices will autodetect the OTP from messages or clipboard, and enable the autocomplete field. */
    otp?: boolean;
    /** Input type for the inputs. */
    type?: Type;
    /** The reading direction of the combobox when applicable. <br> If omitted, inherits globally from `ConfigProvider` or assumes LTR (left-to-right) reading mode. */
    dir?: Direction;
    /** When `true`, prevents the user from interacting with the pin input */
    disabled?: boolean;
    /** Id of the element */
    id?: string;
}

declare type PinInputType = 'text' | 'number';

declare type PinInputValue<Type extends PinInputType = 'text'> = Type extends 'number' ? number[] : string[];

declare type PointerDownOutsideEvent = CustomEvent<{
    originalEvent: PointerEvent;
}>;

declare type PointerHitAreaMargins = {
    coarse: number;
    fine: number;
};

export declare const PopoverAnchor: __VLS_WithTemplateSlots_192<DefineComponent<PopoverAnchorProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<PopoverAnchorProps> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface PopoverAnchorProps extends PopperAnchorProps {
}

export declare const PopoverArrow: __VLS_WithTemplateSlots_193<DefineComponent<PopoverArrowProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<PopoverArrowProps> & Readonly<{}>, {
width: number;
height: number;
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface PopoverArrowProps extends PopperArrowProps {
}

export declare const PopoverClose: __VLS_WithTemplateSlots_194<DefineComponent<PopoverCloseProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<PopoverCloseProps> & Readonly<{}>, {
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface PopoverCloseProps extends PrimitiveProps {
}

export declare const PopoverContent: __VLS_WithTemplateSlots_195<DefineComponent<PopoverContentProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {
escapeKeyDown: (event: KeyboardEvent) => any;
pointerDownOutside: (event: PointerDownOutsideEvent) => any;
focusOutside: (event: FocusOutsideEvent) => any;
interactOutside: (event: PointerDownOutsideEvent | FocusOutsideEvent) => any;
openAutoFocus: (event: Event) => any;
closeAutoFocus: (event: Event) => any;
}, string, PublicProps, Readonly<PopoverContentProps> & Readonly<{
onEscapeKeyDown?: ((event: KeyboardEvent) => any) | undefined;
onPointerDownOutside?: ((event: PointerDownOutsideEvent) => any) | undefined;
onFocusOutside?: ((event: FocusOutsideEvent) => any) | undefined;
onInteractOutside?: ((event: PointerDownOutsideEvent | FocusOutsideEvent) => any) | undefined;
onOpenAutoFocus?: ((event: Event) => any) | undefined;
onCloseAutoFocus?: ((event: Event) => any) | undefined;
}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
    default?(_: {}): any;
}>;

export declare type PopoverContentEmits = PopoverContentImplEmits;

declare type PopoverContentImplEmits = DismissableLayerEmits & {
    /**
     * Event handler called when auto-focusing on open.
     * Can be prevented.
     */
    openAutoFocus: [event: Event];
    /**
     * Event handler called when auto-focusing on close.
     * Can be prevented.
     */
    closeAutoFocus: [event: Event];
};

declare interface PopoverContentImplProps extends PopperContentProps, DismissableLayerProps {
}

export declare interface PopoverContentProps extends PopoverContentImplProps {
    /**
     * Used to force mounting when more control is needed. Useful when
     * controlling animation with Vue animation libraries.
     */
    forceMount?: boolean;
}

export declare const PopoverPortal: __VLS_WithTemplateSlots_196<DefineComponent<PopoverPortalProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<PopoverPortalProps> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface PopoverPortalProps extends TeleportProps {
}

export declare const PopoverRoot: __VLS_WithTemplateSlots_197<DefineComponent<PopoverRootProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {
"update:open": (value: boolean) => any;
}, string, PublicProps, Readonly<PopoverRootProps> & Readonly<{
"onUpdate:open"?: ((value: boolean) => any) | undefined;
}>, {
defaultOpen: boolean;
open: boolean;
modal: boolean;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, Readonly<{
    default?: (props: {
        /** Current open state */
        open: boolean;
    }) => any;
}> & {
    default?: (props: {
        /** Current open state */
        open: boolean;
    }) => any;
}>;

declare interface PopoverRootContext {
    triggerElement: Ref<HTMLElement | undefined>;
    triggerId: string;
    contentId: string;
    open: Ref<boolean>;
    modal: Ref<boolean>;
    onOpenChange: (value: boolean) => void;
    onOpenToggle: () => void;
    hasCustomAnchor: Ref<boolean>;
}

export declare type PopoverRootEmits = {
    /**
     * Event handler called when the open state of the popover changes.
     */
    'update:open': [value: boolean];
};

export declare interface PopoverRootProps {
    /**
     * The open state of the popover when it is initially rendered. Use when you do not need to control its open state.
     */
    defaultOpen?: boolean;
    /**
     * The controlled open state of the popover.
     */
    open?: boolean;
    /**
     * The modality of the popover. When set to true, interaction with outside elements will be disabled and only popover content will be visible to screen readers.
     *
     * @defaultValue false
     */
    modal?: boolean;
}

export declare const PopoverTrigger: __VLS_WithTemplateSlots_198<DefineComponent<PopoverTriggerProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<PopoverTriggerProps> & Readonly<{}>, {
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface PopoverTriggerProps extends PrimitiveProps {
}

declare interface PopperAnchorProps extends PrimitiveProps {
    /**
     *  The reference (or anchor) element that is being referred to for positioning.
     *
     *  If not provided will use the current component as anchor.
     */
    reference?: ReferenceElement;
}

declare interface PopperArrowProps extends ArrowProps, PrimitiveProps {
}

declare interface PopperContentProps extends PrimitiveProps {
    /**
     * The preferred side of the trigger to render against when open.
     * Will be reversed when collisions occur and avoidCollisions
     * is enabled.
     *
     * @defaultValue "top"
     */
    side?: Side;
    /**
     * The distance in pixels from the trigger.
     *
     * @defaultValue 0
     */
    sideOffset?: number;
    /**
     * The preferred alignment against the trigger.
     * May change when collisions occur.
     *
     * @defaultValue "center"
     */
    align?: Align;
    /**
     * An offset in pixels from the `start` or `end` alignment options.
     *
     * @defaultValue 0
     */
    alignOffset?: number;
    /**
     * When `true`, overrides the side and align preferences
     * to prevent collisions with boundary edges.
     *
     * @defaultValue true
     */
    avoidCollisions?: boolean;
    /**
     * The element used as the collision boundary. By default
     * this is the viewport, though you can provide additional
     * element(s) to be included in this check.
     *
     * @defaultValue []
     */
    collisionBoundary?: Element | null | Array<Element | null>;
    /**
     * The distance in pixels from the boundary edges where collision
     * detection should occur. Accepts a number (same for all sides),
     * or a partial padding object, for example: { top: 20, left: 20 }.
     *
     * @defaultValue 0
     */
    collisionPadding?: number | Partial<Record<Side, number>>;
    /**
     * The padding between the arrow and the edges of the content.
     * If your content has border-radius, this will prevent it from
     * overflowing the corners.
     *
     * @defaultValue 0
     */
    arrowPadding?: number;
    /**
     * The sticky behavior on the align axis. `partial` will keep the
     * content in the boundary as long as the trigger is at least partially
     * in the boundary whilst "always" will keep the content in the boundary
     * regardless.
     *
     * @defaultValue "partial"
     */
    sticky?: 'partial' | 'always';
    /**
     * Whether to hide the content when the trigger becomes fully occluded.
     *
     * @defaultValue false
     */
    hideWhenDetached?: boolean;
    /**
     *  The type of CSS position property to use.
     */
    positionStrategy?: 'absolute' | 'fixed';
    /**
     * Strategy to update the position of the floating element on every animation frame.
     *
     * @defaultValue 'optimized'
     */
    updatePositionStrategy?: 'optimized' | 'always';
    /**
     * Whether to disable the update position for the content when the layout shifted.
     *
     * @defaultValue false
     */
    disableUpdateOnLayoutShift?: boolean;
    /**
     * Force content to be position within the viewport.
     *
     * Might overlap the reference element, which may not be desired.
     *
     * @defaultValue false
     */
    prioritizePosition?: boolean;
    /**
     *  The custom element or virtual element that will be set as the reference
     *  to position the floating element.
     *
     *  If provided, it will replace the default anchor element.
     */
    reference?: ReferenceElement;
}

export declare const Presence: DefineComponent<ExtractPropTypes<    {
present: {
type: BooleanConstructor;
required: true;
};
forceMount: {
type: BooleanConstructor;
};
}>, () => VNode<RendererNode, RendererElement, {
[key: string]: any;
}> | null, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<ExtractPropTypes<    {
present: {
type: BooleanConstructor;
required: true;
};
forceMount: {
type: BooleanConstructor;
};
}>> & Readonly<{}>, {
forceMount: boolean;
}, SlotsType<{
default: (opts: {
present: boolean;
}) => any;
}>, {}, {}, string, ComponentProvideOptions, true, {}, any>;

export declare interface PresenceProps {
    /**
     * Conditional to mount or unmount the child element. Similar to `v-if`
     *
     * @required true
     */
    present: boolean;
    /**
     * Force the element to render all the time.
     *
     * Useful for programmatically render grandchild component with the exposed `present`
     *
     * @defaultValue false
     */
    forceMount?: boolean;
}

export declare const Primitive: DefineComponent<ExtractPropTypes<    {
asChild: {
type: BooleanConstructor;
default: boolean;
};
as: {
type: PropType<AsTag | Component>;
default: string;
};
}>, () => VNode<RendererNode, RendererElement, {
[key: string]: any;
}>, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<ExtractPropTypes<    {
asChild: {
type: BooleanConstructor;
default: boolean;
};
as: {
type: PropType<AsTag | Component>;
default: string;
};
}>> & Readonly<{}>, {
asChild: boolean;
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, true, {}, any>;

export declare interface PrimitiveProps {
    /**
     * Change the default rendered element for the one passed as a child, merging their props and behavior.
     *
     * Read our [Composition](https://www.reka-ui.com/docs/guides/composition) guide for more details.
     */
    asChild?: boolean;
    /**
     * The element or component this component should render as. Can be overwritten by `asChild`.
     * @defaultValue "div"
     */
    as?: AsTag | Component;
}

export declare const ProgressIndicator: __VLS_WithTemplateSlots_199<DefineComponent<ProgressIndicatorProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<ProgressIndicatorProps> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface ProgressIndicatorProps extends PrimitiveProps {
}

export declare const ProgressRoot: __VLS_WithTemplateSlots_200<DefineComponent<ProgressRootProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {
"update:modelValue": (value: string[] | undefined) => any;
"update:max": (value: number) => any;
}, string, PublicProps, Readonly<ProgressRootProps> & Readonly<{
"onUpdate:modelValue"?: ((value: string[] | undefined) => any) | undefined;
"onUpdate:max"?: ((value: number) => any) | undefined;
}>, {
max: number;
getValueLabel: (value: number | null | undefined, max: number) => string | undefined;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, Readonly<{
    default?: (props: {
        /** Current input values */
        modelValue: number | null | undefined;
    }) => any;
}> & {
    default?: (props: {
        /** Current input values */
        modelValue: number | null | undefined;
    }) => any;
}>;

declare interface ProgressRootContext {
    modelValue?: Readonly<Ref<ProgressRootProps['modelValue']>>;
    max: Readonly<Ref<number>>;
    progressState: ComputedRef<ProgressState>;
}

export declare type ProgressRootEmits = {
    /** Event handler called when the progress value changes */
    'update:modelValue': [value: string[] | undefined];
    /** Event handler called when the max value changes */
    'update:max': [value: number];
};

export declare interface ProgressRootProps extends PrimitiveProps {
    /** The progress value. Can be bind as `v-model`. */
    modelValue?: number | null;
    /** The maximum progress value. */
    max?: number;
    /**
     * A function to get the accessible label text in a human-readable format.
     *
     *  If not provided, the value label will be read as the numeric value as a percentage of the max value.
     */
    getValueLabel?: (value: number | null | undefined, max: number) => string | undefined;
    /**
     * A function to get the accessible value text representing the current value in a human-readable format.
     */
    getValueText?: (value: number | null | undefined, max: number) => string | undefined;
}

declare type ProgressState = 'indeterminate' | 'loading' | 'complete';

export declare const RadioGroupIndicator: __VLS_WithTemplateSlots_201<DefineComponent<RadioGroupIndicatorProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<RadioGroupIndicatorProps> & Readonly<{}>, {
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface RadioGroupIndicatorProps extends PrimitiveProps {
    /**
     * Used to force mounting when more control is needed. Useful when
     * controlling animation with Vue animation libraries.
     */
    forceMount?: boolean;
}

export declare const RadioGroupItem: __VLS_WithTemplateSlots_202<DefineComponent<RadioGroupItemProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {
select: (event: RadioGroupItemSelectEvent) => any;
}, string, PublicProps, Readonly<RadioGroupItemProps> & Readonly<{
onSelect?: ((event: RadioGroupItemSelectEvent) => any) | undefined;
}>, {
disabled: boolean;
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, Readonly<{
    default?: (props: {
        /** Current checked state */
        checked: boolean;
        /** Required state */
        required: boolean;
        /** Disabled state */
        disabled: boolean;
    }) => any;
}> & {
    default?: (props: {
        /** Current checked state */
        checked: boolean;
        /** Required state */
        required: boolean;
        /** Disabled state */
        disabled: boolean;
    }) => any;
}>;

declare interface RadioGroupItemContext {
    disabled: ComputedRef<boolean>;
    checked: ComputedRef<boolean>;
}

export declare interface RadioGroupItemProps extends Omit<RadioProps, 'checked'> {
}

export declare type RadioGroupItemSelectEvent = CustomEvent<{
    originalEvent: MouseEvent;
    value?: AcceptableValue;
}>;

export declare const RadioGroupRoot: __VLS_WithTemplateSlots_203<DefineComponent<RadioGroupRootProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {
"update:modelValue": (payload: string) => any;
}, string, PublicProps, Readonly<RadioGroupRootProps> & Readonly<{
"onUpdate:modelValue"?: ((payload: string) => any) | undefined;
}>, {
disabled: boolean;
loop: boolean;
required: boolean;
orientation: DataOrientation;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, Readonly<{
    default?: (props: {
        /** Current input values */
        modelValue: AcceptableValue | undefined;
    }) => any;
}> & {
    default?: (props: {
        /** Current input values */
        modelValue: AcceptableValue | undefined;
    }) => any;
}>;

declare interface RadioGroupRootContext {
    modelValue?: Readonly<Ref<AcceptableValue | undefined>>;
    changeModelValue: (value?: AcceptableValue) => void;
    disabled: Ref<boolean>;
    loop: Ref<boolean>;
    orientation: Ref<DataOrientation | undefined>;
    name?: string;
    required: Ref<boolean>;
}

export declare type RadioGroupRootEmits = {
    /** Event handler called when the radio group value changes */
    'update:modelValue': [payload: string];
};

export declare interface RadioGroupRootProps extends PrimitiveProps, FormFieldProps {
    /** The controlled value of the radio item to check. Can be binded as `v-model`. */
    modelValue?: AcceptableValue;
    /**
     * The value of the radio item that should be checked when initially rendered.
     *
     * Use when you do not need to control the state of the radio items.
     */
    defaultValue?: AcceptableValue;
    /** When `true`, prevents the user from interacting with radio items. */
    disabled?: boolean;
    /** The orientation of the component. */
    orientation?: DataOrientation;
    /** The reading direction of the combobox when applicable. <br> If omitted, inherits globally from `ConfigProvider` or assumes LTR (left-to-right) reading mode. */
    dir?: Direction;
    /** When `true`, keyboard navigation will loop from last item to first, and vice versa. */
    loop?: boolean;
}

declare interface RadioProps extends PrimitiveProps, FormFieldProps {
    id?: string;
    /** The value given as data when submitted with a `name`. */
    value?: AcceptableValue;
    /** When `true`, prevents the user from interacting with the radio item. */
    disabled?: boolean;
    checked?: boolean;
}

export declare const RangeCalendarCell: __VLS_WithTemplateSlots_204<DefineComponent<RangeCalendarCellProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<RangeCalendarCellProps> & Readonly<{}>, {
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface RangeCalendarCellProps extends PrimitiveProps {
    date: DateValue;
}

export declare const RangeCalendarCellTrigger: __VLS_WithTemplateSlots_205<DefineComponent<RangeCalendarCellTriggerProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<RangeCalendarCellTriggerProps> & Readonly<{}>, {
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {
primitiveElement: CreateComponentPublicInstanceWithMixins<Readonly<ExtractPropTypes<    {
asChild: {
type: BooleanConstructor;
default: boolean;
};
as: {
type: PropType<AsTag | Component>;
default: string;
};
}>> & Readonly<{}>, () => VNode<RendererNode, RendererElement, {
[key: string]: any;
}>, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, PublicProps, {
asChild: boolean;
as: AsTag | Component;
}, true, {}, {}, GlobalComponents, GlobalDirectives, string, {}, any, ComponentProvideOptions, {
P: {};
B: {};
D: {};
C: {};
M: {};
Defaults: {};
}, Readonly<ExtractPropTypes<    {
asChild: {
type: BooleanConstructor;
default: boolean;
};
as: {
type: PropType<AsTag | Component>;
default: string;
};
}>> & Readonly<{}>, () => VNode<RendererNode, RendererElement, {
[key: string]: any;
}>, {}, {}, {}, {
asChild: boolean;
as: AsTag | Component;
}> | null;
}, any>, Readonly<RangeCalendarCellTriggerSlot> & RangeCalendarCellTriggerSlot>;

export declare interface RangeCalendarCellTriggerProps extends PrimitiveProps {
    day: DateValue;
    month: DateValue;
}

declare interface RangeCalendarCellTriggerSlot {
    default?: (props: {
        /** Current day */
        dayValue: string;
        /** Current disable state */
        disabled: boolean;
        /** Current selected state */
        selected: boolean;
        /** Current today state */
        today: boolean;
        /** Current outside view state */
        outsideView: boolean;
        /** Current outside visible view state */
        outsideVisibleView: boolean;
        /** Current unavailable state */
        unavailable: boolean;
        /** Current highlighted state */
        highlighted: boolean;
        /** Current highlighted start state */
        highlightedStart: boolean;
        /** Current highlighted end state */
        highlightedEnd: boolean;
        /** Current selection start state */
        selectionStart: boolean;
        /** Current selection end state */
        selectionEnd: boolean;
    }) => any;
}

export declare const RangeCalendarGrid: __VLS_WithTemplateSlots_206<DefineComponent<RangeCalendarGridProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<RangeCalendarGridProps> & Readonly<{}>, {
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare const RangeCalendarGridBody: __VLS_WithTemplateSlots_207<DefineComponent<RangeCalendarGridBodyProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<RangeCalendarGridBodyProps> & Readonly<{}>, {
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface RangeCalendarGridBodyProps extends PrimitiveProps {
}

export declare const RangeCalendarGridHead: __VLS_WithTemplateSlots_208<DefineComponent<RangeCalendarGridHeadProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<RangeCalendarGridHeadProps> & Readonly<{}>, {
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface RangeCalendarGridHeadProps extends PrimitiveProps {
}

export declare interface RangeCalendarGridProps extends PrimitiveProps {
}

export declare const RangeCalendarGridRow: __VLS_WithTemplateSlots_209<DefineComponent<RangeCalendarGridRowProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<RangeCalendarGridRowProps> & Readonly<{}>, {
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface RangeCalendarGridRowProps extends PrimitiveProps {
}

export declare const RangeCalendarHeadCell: __VLS_WithTemplateSlots_210<DefineComponent<RangeCalendarHeadCellProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<RangeCalendarHeadCellProps> & Readonly<{}>, {
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface RangeCalendarHeadCellProps extends PrimitiveProps {
}

export declare const RangeCalendarHeader: __VLS_WithTemplateSlots_211<DefineComponent<RangeCalendarHeaderProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<RangeCalendarHeaderProps> & Readonly<{}>, {
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface RangeCalendarHeaderProps extends PrimitiveProps {
}

export declare const RangeCalendarHeading: __VLS_WithTemplateSlots_212<DefineComponent<RangeCalendarHeadingProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<RangeCalendarHeadingProps> & Readonly<{}>, {
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, Readonly<{
    default?: (props: {
        /** Current month and year */
        headingValue: string;
    }) => any;
}> & {
    default?: (props: {
        /** Current month and year */
        headingValue: string;
    }) => any;
}>;

export declare interface RangeCalendarHeadingProps extends PrimitiveProps {
}

export declare const RangeCalendarNext: __VLS_WithTemplateSlots_213<DefineComponent<RangeCalendarNextProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<RangeCalendarNextProps> & Readonly<{}>, {
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, Readonly<RangeCalendarNextSlot> & RangeCalendarNextSlot>;

export declare interface RangeCalendarNextProps extends PrimitiveProps {
    /** The function to be used for the next page. Overwrites the `nextPage` function set on the `RangeCalendarRoot`. */
    nextPage?: (placeholder: DateValue) => DateValue;
}

declare interface RangeCalendarNextSlot {
    default?: (props: {
        /** Current disable state */
        disabled: boolean;
    }) => any;
}

export declare const RangeCalendarPrev: __VLS_WithTemplateSlots_214<DefineComponent<RangeCalendarPrevProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<RangeCalendarPrevProps> & Readonly<{}>, {
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, Readonly<RangeCalendarPrevSlot> & RangeCalendarPrevSlot>;

export declare interface RangeCalendarPrevProps extends PrimitiveProps {
    /** The function to be used for the prev page. Overwrites the `prevPage` function set on the `RangeCalendarRoot`. */
    prevPage?: (placeholder: DateValue) => DateValue;
}

declare interface RangeCalendarPrevSlot {
    default?: (props: {
        /** Current disable state */
        disabled: boolean;
    }) => any;
}

export declare const RangeCalendarRoot: __VLS_WithTemplateSlots_215<DefineComponent<RangeCalendarRootProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {
"update:modelValue": (date: DateRange) => any;
"update:placeholder": (date: DateValue) => any;
"update:startValue": (date: DateValue | undefined) => any;
}, string, PublicProps, Readonly<RangeCalendarRootProps> & Readonly<{
"onUpdate:modelValue"?: ((date: DateRange) => any) | undefined;
"onUpdate:placeholder"?: ((date: DateValue) => any) | undefined;
"onUpdate:startValue"?: ((date: DateValue | undefined) => any) | undefined;
}>, {
defaultValue: DateRange;
weekStartsOn: 0 | 1 | 2 | 3 | 4 | 5 | 6;
fixedWeeks: boolean;
numberOfMonths: number;
pagedNavigation: boolean;
placeholder: DateValue;
disabled: boolean;
as: AsTag | Component;
preventDeselect: boolean;
weekdayFormat: WeekDayFormat;
readonly: boolean;
initialFocus: boolean;
isDateDisabled: Matcher;
isDateUnavailable: Matcher;
disableDaysOutsideCurrentView: boolean;
isDateHighlightable: Matcher;
allowNonContiguousRanges: boolean;
maximumDays: number;
}, {}, {}, {}, string, ComponentProvideOptions, false, {
primitiveElement: CreateComponentPublicInstanceWithMixins<Readonly<ExtractPropTypes<    {
asChild: {
type: BooleanConstructor;
default: boolean;
};
as: {
type: PropType<AsTag | Component>;
default: string;
};
}>> & Readonly<{}>, () => VNode<RendererNode, RendererElement, {
[key: string]: any;
}>, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, PublicProps, {
asChild: boolean;
as: AsTag | Component;
}, true, {}, {}, GlobalComponents, GlobalDirectives, string, {}, any, ComponentProvideOptions, {
P: {};
B: {};
D: {};
C: {};
M: {};
Defaults: {};
}, Readonly<ExtractPropTypes<    {
asChild: {
type: BooleanConstructor;
default: boolean;
};
as: {
type: PropType<AsTag | Component>;
default: string;
};
}>> & Readonly<{}>, () => VNode<RendererNode, RendererElement, {
[key: string]: any;
}>, {}, {}, {}, {
asChild: boolean;
as: AsTag | Component;
}> | null;
}, any>, Readonly<{
    default?: (props: {
        /** The current date of the placeholder */
        date: DateValue;
        /** The grid of dates */
        grid: Grid<DateValue>[];
        /** The days of the week */
        weekDays: string[];
        /** The start of the week */
        weekStartsOn: 0 | 1 | 2 | 3 | 4 | 5 | 6;
        /** The calendar locale */
        locale: string;
        /** Whether or not to always display 6 weeks in the calendar */
        fixedWeeks: boolean;
        /** The current date range */
        modelValue: DateRange;
    }) => any;
}> & {
    default?: (props: {
        /** The current date of the placeholder */
        date: DateValue;
        /** The grid of dates */
        grid: Grid<DateValue>[];
        /** The days of the week */
        weekDays: string[];
        /** The start of the week */
        weekStartsOn: 0 | 1 | 2 | 3 | 4 | 5 | 6;
        /** The calendar locale */
        locale: string;
        /** Whether or not to always display 6 weeks in the calendar */
        fixedWeeks: boolean;
        /** The current date range */
        modelValue: DateRange;
    }) => any;
}>;

declare type RangeCalendarRootContext = {
    modelValue: Ref<DateRange>;
    startValue: Ref<DateValue | undefined>;
    endValue: Ref<DateValue | undefined>;
    locale: Ref<string>;
    placeholder: Ref<DateValue>;
    pagedNavigation: Ref<boolean>;
    preventDeselect: Ref<boolean>;
    grid: Ref<Grid<DateValue>[]>;
    weekDays: Ref<string[]>;
    weekStartsOn: Ref<0 | 1 | 2 | 3 | 4 | 5 | 6>;
    weekdayFormat: Ref<WeekDayFormat>;
    fixedWeeks: Ref<boolean>;
    numberOfMonths: Ref<number>;
    disabled: Ref<boolean>;
    readonly: Ref<boolean>;
    initialFocus: Ref<boolean>;
    onPlaceholderChange: (date: DateValue) => void;
    fullCalendarLabel: Ref<string>;
    parentElement: Ref<HTMLElement | undefined>;
    headingValue: Ref<string>;
    isInvalid: Ref<boolean>;
    isDateDisabled: Matcher;
    isDateUnavailable?: Matcher;
    isDateHighlightable?: Matcher;
    isOutsideVisibleView: (date: DateValue) => boolean;
    allowNonContiguousRanges: Ref<boolean>;
    highlightedRange: Ref<{
        start: DateValue;
        end: DateValue;
    } | null>;
    focusedValue: Ref<DateValue | undefined>;
    lastPressedDateValue: Ref<DateValue | undefined>;
    isSelected: (date: DateValue) => boolean;
    isSelectionEnd: (date: DateValue) => boolean;
    isSelectionStart: (date: DateValue) => boolean;
    isHighlightedStart: (date: DateValue) => boolean;
    isHighlightedEnd: (date: DateValue) => boolean;
    prevPage: (prevPageFunc?: (date: DateValue) => DateValue) => void;
    nextPage: (nextPageFunc?: (date: DateValue) => DateValue) => void;
    isNextButtonDisabled: (nextPageFunc?: (date: DateValue) => DateValue) => boolean;
    isPrevButtonDisabled: (prevPageFunc?: (date: DateValue) => DateValue) => boolean;
    formatter: Formatter;
    dir: Ref<Direction>;
    disableDaysOutsideCurrentView: Ref<boolean>;
    fixedDate: Ref<'start' | 'end' | undefined>;
    maximumDays: Ref<number | undefined>;
};

export declare type RangeCalendarRootEmits = {
    /** Event handler called whenever the model value changes */
    'update:modelValue': [date: DateRange];
    /** Event handler called whenever the placeholder value changes */
    'update:placeholder': [date: DateValue];
    /** Event handler called whenever the start value changes */
    'update:startValue': [date: DateValue | undefined];
};

export declare interface RangeCalendarRootProps extends PrimitiveProps {
    /** The default placeholder date */
    defaultPlaceholder?: DateValue;
    /** The default value for the calendar */
    defaultValue?: DateRange;
    /** The controlled checked state of the calendar. Can be bound as `v-model`. */
    modelValue?: DateRange | null;
    /** The placeholder date, which is used to determine what month to display when no date is selected. This updates as the user navigates the calendar and can be used to programmatically control the calendar view */
    placeholder?: DateValue;
    /** When combined with `isDateUnavailable`, determines whether non-contiguous ranges, i.e. ranges containing unavailable dates, may be selected. */
    allowNonContiguousRanges?: boolean;
    /** This property causes the previous and next buttons to navigate by the number of months displayed at once, rather than one month */
    pagedNavigation?: boolean;
    /** Whether or not to prevent the user from deselecting a date without selecting another date first */
    preventDeselect?: boolean;
    /** The maximum number of days that can be selected in a range */
    maximumDays?: number;
    /** The day of the week to start the calendar on */
    weekStartsOn?: 0 | 1 | 2 | 3 | 4 | 5 | 6;
    /** The format to use for the weekday strings provided via the weekdays slot prop */
    weekdayFormat?: WeekDayFormat;
    /** The accessible label for the calendar */
    calendarLabel?: string;
    /** Whether or not to always display 6 weeks in the calendar */
    fixedWeeks?: boolean;
    /** The maximum date that can be selected */
    maxValue?: DateValue;
    /** The minimum date that can be selected */
    minValue?: DateValue;
    /** The locale to use for formatting dates */
    locale?: string;
    /** The number of months to display at once */
    numberOfMonths?: number;
    /** Whether or not the calendar is disabled */
    disabled?: boolean;
    /** Whether or not the calendar is readonly */
    readonly?: boolean;
    /** If true, the calendar will focus the selected day, today, or the first day of the month depending on what is visible when the calendar is mounted */
    initialFocus?: boolean;
    /** A function that returns whether or not a date is disabled */
    isDateDisabled?: Matcher;
    /** A function that returns whether or not a date is unavailable */
    isDateUnavailable?: Matcher;
    /** A function that returns whether or not a date is hightable */
    isDateHighlightable?: Matcher;
    /** The reading direction of the calendar when applicable. <br> If omitted, inherits globally from `ConfigProvider` or assumes LTR (left-to-right) reading mode. */
    dir?: Direction;
    /** A function that returns the next page of the calendar. It receives the current placeholder as an argument inside the component. */
    nextPage?: (placeholder: DateValue) => DateValue;
    /** A function that returns the previous page of the calendar. It receives the current placeholder as an argument inside the component. */
    prevPage?: (placeholder: DateValue) => DateValue;
    /** Whether or not to disable days outside the current view. */
    disableDaysOutsideCurrentView?: boolean;
    /** Which part of the range should be fixed */
    fixedDate?: 'start' | 'end';
}

declare type RawProps = VNodeProps & {
    __v_isVNode?: never;
    [Symbol.iterator]?: never;
} & Record<string, any>;

export { ReferenceElement }

declare type ResizeEvent = KeyboardEvent | MouseEvent | TouchEvent;

declare type ResizeHandler = (event: ResizeEvent) => void;

export declare const RovingFocusGroup: __VLS_WithTemplateSlots_216<DefineComponent<RovingFocusGroupProps, {
getItems: (includeDisabledItem?: boolean) => {
ref: HTMLElement;
value?: any;
}[];
}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {
entryFocus: (event: Event) => any;
"update:currentTabStopId": (value: string | null | undefined) => any;
}, string, PublicProps, Readonly<RovingFocusGroupProps> & Readonly<{
onEntryFocus?: ((event: Event) => any) | undefined;
"onUpdate:currentTabStopId"?: ((value: string | null | undefined) => any) | undefined;
}>, {
loop: boolean;
orientation: Orientation;
preventScrollOnEntryFocus: boolean;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare type RovingFocusGroupEmits = {
    'entryFocus': [event: Event];
    'update:currentTabStopId': [value: string | null | undefined];
};

export declare interface RovingFocusGroupProps extends PrimitiveProps {
    /**
     * The orientation of the group.
     * Mainly so arrow navigation is done accordingly (left & right vs. up & down)
     */
    orientation?: Orientation;
    /**
     * The direction of navigation between items.
     */
    dir?: Direction_2;
    /**
     * Whether keyboard navigation should loop around
     * @defaultValue false
     */
    loop?: boolean;
    /** The controlled value of the current stop item. Can be binded as `v-model`. */
    currentTabStopId?: string | null;
    /**
     * The value of the current stop item.
     *
     * Use when you do not need to control the state of the stop item.
     */
    defaultCurrentTabStopId?: string;
    /**
     * When `true`, will prevent scrolling to the focus item when focused.
     */
    preventScrollOnEntryFocus?: boolean;
}

export declare const RovingFocusItem: __VLS_WithTemplateSlots_217<DefineComponent<RovingFocusItemProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<RovingFocusItemProps> & Readonly<{}>, {
as: AsTag | Component;
focusable: boolean;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface RovingFocusItemProps extends PrimitiveProps {
    tabStopId?: string;
    /**
     * When `false`, item will not be focusable.
     * @defaultValue `true`
     */
    focusable?: boolean;
    /** When `true`, item will be initially focused. */
    active?: boolean;
    /** When `true`, shift + arrow key will allow focusing on next/previous item. */
    allowShiftKey?: boolean;
}

export declare const ScrollAreaCorner: __VLS_WithTemplateSlots_218<DefineComponent<ScrollAreaCornerProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<ScrollAreaCornerProps> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface ScrollAreaCornerProps extends PrimitiveProps {
}

export declare const ScrollAreaRoot: __VLS_WithTemplateSlots_219<DefineComponent<ScrollAreaRootProps, {
/** Viewport element within ScrollArea */
viewport: Ref<HTMLElement | undefined, HTMLElement | undefined>;
/** Scroll viewport to top */
scrollTop: () => void;
/** Scroll viewport to top-left */
scrollTopLeft: () => void;
}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<ScrollAreaRootProps> & Readonly<{}>, {
type: ScrollType;
scrollHideDelay: number;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

declare interface ScrollAreaRootContext {
    type: Ref<ScrollType>;
    dir: Ref<Direction_5>;
    scrollHideDelay: Ref<number>;
    scrollArea: Ref<HTMLElement | undefined>;
    viewport: Ref<HTMLElement | undefined>;
    onViewportChange: (viewport: HTMLElement | null) => void;
    content: Ref<HTMLElement | undefined>;
    onContentChange: (content: HTMLElement) => void;
    scrollbarX: Ref<HTMLElement | undefined>;
    onScrollbarXChange: (scrollbar: HTMLElement | null) => void;
    scrollbarXEnabled: Ref<boolean>;
    onScrollbarXEnabledChange: (rendered: boolean) => void;
    scrollbarY: Ref<HTMLElement | undefined>;
    onScrollbarYChange: (scrollbar: HTMLElement | null) => void;
    scrollbarYEnabled: Ref<boolean>;
    onScrollbarYEnabledChange: (rendered: boolean) => void;
    onCornerWidthChange: (width: number) => void;
    onCornerHeightChange: (height: number) => void;
}

export declare interface ScrollAreaRootProps extends PrimitiveProps {
    /**
     * Describes the nature of scrollbar visibility, similar to how the scrollbar preferences in MacOS control visibility of native scrollbars.
     *
     * `auto` - means that scrollbars are visible when content is overflowing on the corresponding orientation. <br>
     * `always` - means that scrollbars are always visible regardless of whether the content is overflowing.<br>
     * `scroll` - means that scrollbars are visible when the user is scrolling along its corresponding orientation.<br>
     * `hover` - when the user is scrolling along its corresponding orientation and when the user is hovering over the scroll area.
     */
    type?: ScrollType;
    /** The reading direction of the combobox when applicable. <br> If omitted, inherits globally from `ConfigProvider` or assumes LTR (left-to-right) reading mode. */
    dir?: Direction_5;
    /** If type is set to either `scroll` or `hover`, this prop determines the length of time, in milliseconds, <br> before the scrollbars are hidden after the user stops interacting with scrollbars. */
    scrollHideDelay?: number;
}

declare interface ScrollAreaScollbarContext {
    as: Ref<PrimitiveProps['as']>;
    orientation: Ref<'vertical' | 'horizontal'>;
    forceMount?: Ref<boolean>;
    isHorizontal: Ref<boolean>;
    asChild: Ref<boolean>;
}

export declare const ScrollAreaScrollbar: __VLS_WithTemplateSlots_220<DefineComponent<ScrollAreaScrollbarProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<ScrollAreaScrollbarProps> & Readonly<{}>, {
as: AsTag | Component;
orientation: "vertical" | "horizontal";
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
    default?(_: {}): any;
    default?(_: {}): any;
    default?(_: {}): any;
}>;

export declare interface ScrollAreaScrollbarProps extends PrimitiveProps {
    /** The orientation of the scrollbar */
    orientation?: 'vertical' | 'horizontal';
    /**
     * Used to force mounting when more control is needed. Useful when
     * controlling animation with Vue animation libraries.
     */
    forceMount?: boolean;
}

export declare const ScrollAreaThumb: __VLS_WithTemplateSlots_221<DefineComponent<ScrollAreaThumbProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<ScrollAreaThumbProps> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface ScrollAreaThumbProps extends PrimitiveProps {
}

export declare const ScrollAreaViewport: __VLS_WithTemplateSlots_222<DefineComponent<ScrollAreaViewportProps, {
viewportElement: Ref<HTMLElement | undefined, HTMLElement | undefined>;
}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<ScrollAreaViewportProps> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {
viewportElement: HTMLDivElement;
}, any>, {
    default?(_: {}): any;
}>;

export declare interface ScrollAreaViewportProps extends PrimitiveProps {
    /**
     * Will add `nonce` attribute to the style tag which can be used by Content Security Policy. <br> If omitted, inherits globally from `ConfigProvider`.
     */
    nonce?: string;
}

/**
 * if padding or margin is number, it will be in px
 * if padding or margin is true, it will be var(--scrollbar-width)
 * otherwise, it will be passed string
 */
declare type ScrollBodyOption = {
    padding?: boolean | number | string;
    margin?: boolean | number | string;
};

declare type ScrollType = 'auto' | 'always' | 'scroll' | 'hover';

declare type SegmentPart = EditableSegmentPart | NonEditableSegmentPart;

declare type SegmentValueObj = DateSegmentObj | DateAndTimeSegmentObj;

export declare const SelectArrow: __VLS_WithTemplateSlots_223<DefineComponent<SelectArrowProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<SelectArrowProps> & Readonly<{}>, {
width: number;
height: number;
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface SelectArrowProps extends PopperArrowProps {
}

export declare const SelectContent: __VLS_WithTemplateSlots_224<DefineComponent<SelectContentProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {
escapeKeyDown: (event: KeyboardEvent) => any;
pointerDownOutside: (event: PointerDownOutsideEvent) => any;
closeAutoFocus: (event: Event) => any;
}, string, PublicProps, Readonly<SelectContentProps> & Readonly<{
onEscapeKeyDown?: ((event: KeyboardEvent) => any) | undefined;
onPointerDownOutside?: ((event: PointerDownOutsideEvent) => any) | undefined;
onCloseAutoFocus?: ((event: Event) => any) | undefined;
}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {
presenceRef: CreateComponentPublicInstanceWithMixins<Readonly<ExtractPropTypes<    {
present: {
type: BooleanConstructor;
required: true;
};
forceMount: {
type: BooleanConstructor;
};
}>> & Readonly<{}>, () => VNode<RendererNode, RendererElement, {
[key: string]: any;
}> | null, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, PublicProps, {
forceMount: boolean;
}, true, {}, SlotsType<    {
default: (opts: {
present: boolean;
}) => any;
}>, GlobalComponents, GlobalDirectives, string, {}, any, ComponentProvideOptions, {
P: {};
B: {};
D: {};
C: {};
M: {};
Defaults: {};
}, Readonly<ExtractPropTypes<    {
present: {
type: BooleanConstructor;
required: true;
};
forceMount: {
type: BooleanConstructor;
};
}>> & Readonly<{}>, () => VNode<RendererNode, RendererElement, {
[key: string]: any;
}> | null, {}, {}, {}, {
forceMount: boolean;
}> | null;
}, any>, {
    default?(_: {}): any;
    default?(_: {}): any;
}>;

export declare type SelectContentEmits = SelectContentImplEmits;

declare type SelectContentImplEmits = {
    closeAutoFocus: [event: Event];
    /**
     * Event handler called when the escape key is down.
     * Can be prevented.
     */
    escapeKeyDown: [event: KeyboardEvent];
    /**
     * Event handler called when a `pointerdown` event happens outside of the `DismissableLayer`.
     * Can be prevented.
     */
    pointerDownOutside: [event: PointerDownOutsideEvent];
};

declare interface SelectContentImplProps extends PopperContentProps {
    /**
     *  The positioning mode to use
     *
     *  `item-aligned (default)` - behaves similarly to a native MacOS menu by positioning content relative to the active item. <br>
     *  `popper` - positions content in the same way as our other primitives, for example `Popover` or `DropdownMenu`.
     */
    position?: 'item-aligned' | 'popper';
    /**
     * The document.body will be lock, and scrolling will be disabled.
     *
     * @defaultValue true
     */
    bodyLock?: boolean;
}

export declare interface SelectContentProps extends SelectContentImplProps {
    /**
     * Used to force mounting when more control is needed. Useful when
     * controlling animation with Vue animation libraries.
     */
    forceMount?: boolean;
}

export declare const SelectGroup: __VLS_WithTemplateSlots_225<DefineComponent<SelectGroupProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<SelectGroupProps> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

declare interface SelectGroupContext {
    id: string;
}

export declare interface SelectGroupProps extends PrimitiveProps {
}

export declare const SelectIcon: __VLS_WithTemplateSlots_226<DefineComponent<SelectIconProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<SelectIconProps> & Readonly<{}>, {
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface SelectIconProps extends PrimitiveProps {
}

export declare const SelectItem: <T extends AcceptableValue = AcceptableValue>(__VLS_props: NonNullable<Awaited<typeof __VLS_setup>>["props"], __VLS_ctx?: __VLS_PrettifyLocal_10<Pick<NonNullable<Awaited<typeof __VLS_setup>>, "attrs" | "emit" | "slots">>, __VLS_expose?: NonNullable<Awaited<typeof __VLS_setup>>["expose"], __VLS_setup?: Promise<{
    props: __VLS_PrettifyLocal_10<Pick<Partial<{}> & Omit<{
        readonly onSelect?: ((event: SelectItemSelectEvent<T>) => any) | undefined;
    } & VNodeProps & AllowedComponentProps & ComponentCustomProps, never>, "onSelect"> & SelectItemProps<AcceptableValue> & Partial<{}>> & PublicProps;
    expose(exposed: ShallowUnwrapRef<    {}>): void;
    attrs: any;
    slots: {
        default?(_: {}): any;
    };
    emit: (evt: "select", event: SelectItemSelectEvent<T>) => void;
}>) => VNode & {
    __ctx?: Awaited<typeof __VLS_setup>;
};

declare interface SelectItemContext<T = AcceptableValue> {
    value: T;
    textId: string;
    disabled: Ref<boolean>;
    isSelected: Ref<boolean>;
    onItemTextChange: (node: HTMLElement | undefined) => void;
}

export declare const SelectItemIndicator: __VLS_WithTemplateSlots_227<DefineComponent<SelectItemIndicatorProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<SelectItemIndicatorProps> & Readonly<{}>, {
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface SelectItemIndicatorProps extends PrimitiveProps {
}

export declare interface SelectItemProps<T = AcceptableValue> extends PrimitiveProps {
    /** The value given as data when submitted with a `name`. */
    value: T;
    /** When `true`, prevents the user from interacting with the item. */
    disabled?: boolean;
    /**
     * Optional text used for typeahead purposes.
     *
     * By default the typeahead behavior will use the `.textContent` of the `SelectItemText` part.
     *
     * Use this when the content is complex, or you have non-textual content inside.
     */
    textValue?: string;
}

export declare type SelectItemSelectEvent<T> = CustomEvent<{
    originalEvent: PointerEvent | KeyboardEvent;
    value?: T;
}>;

export declare const SelectItemText: __VLS_WithTemplateSlots_228<DefineComponent<SelectItemTextProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<SelectItemTextProps> & Readonly<{}>, {
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface SelectItemTextProps extends PrimitiveProps {
}

export declare const SelectLabel: __VLS_WithTemplateSlots_229<DefineComponent<SelectLabelProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<SelectLabelProps> & Readonly<{}>, {
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface SelectLabelProps extends PrimitiveProps {
    for?: string;
}

declare interface SelectOption {
    value: any;
    disabled?: boolean;
    textContent: string;
}

export declare const SelectPortal: __VLS_WithTemplateSlots_230<DefineComponent<SelectPortalProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<SelectPortalProps> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface SelectPortalProps extends TeleportProps {
}

export declare const SelectRoot: <T extends AcceptableValue = AcceptableValue>(__VLS_props: NonNullable<Awaited<typeof __VLS_setup>>["props"], __VLS_ctx?: __VLS_PrettifyLocal_11<Pick<NonNullable<Awaited<typeof __VLS_setup>>, "attrs" | "emit" | "slots">>, __VLS_expose?: NonNullable<Awaited<typeof __VLS_setup>>["expose"], __VLS_setup?: Promise<{
    props: __VLS_PrettifyLocal_11<Pick<Partial<{}> & Omit<{
        readonly "onUpdate:open"?: ((value: boolean) => any) | undefined;
        readonly "onUpdate:modelValue"?: ((value: T) => any) | undefined;
    } & VNodeProps & AllowedComponentProps & ComponentCustomProps, never>, "onUpdate:open" | "onUpdate:modelValue"> & SelectRootProps<T> & Partial<{}>> & PublicProps;
    expose(exposed: ShallowUnwrapRef<    {}>): void;
    attrs: any;
    slots: Readonly<{
        default?: (props: {
            /** Current input values */
            modelValue: T | T[] | undefined;
            /** Current open state */
            open: boolean;
        }) => any;
    }> & {
        default?: (props: {
            /** Current input values */
            modelValue: T | T[] | undefined;
            /** Current open state */
            open: boolean;
        }) => any;
    };
    emit: ((evt: "update:open", value: boolean) => void) & ((evt: "update:modelValue", value: T) => void);
}>) => VNode & {
    __ctx?: Awaited<typeof __VLS_setup>;
};

declare interface SelectRootContext<T> {
    triggerElement: Ref<HTMLElement | undefined>;
    onTriggerChange: (node: HTMLElement | undefined) => void;
    valueElement: Ref<HTMLElement | undefined>;
    onValueElementChange: (node: HTMLElement) => void;
    contentId: string;
    modelValue: Ref<T | Array<T> | undefined>;
    onValueChange: (value: T) => void;
    open: Ref<boolean>;
    multiple: Ref<boolean>;
    required?: Ref<boolean>;
    by?: string | ((a: T, b: T) => boolean);
    onOpenChange: (open: boolean) => void;
    dir: Ref<Direction>;
    triggerPointerDownPosRef: Ref<{
        x: number;
        y: number;
    } | null>;
    isEmptyModelValue: Ref<boolean>;
    disabled?: Ref<boolean>;
    optionsSet: Ref<Set<SelectOption>>;
    onOptionAdd: (option: SelectOption) => void;
    onOptionRemove: (option: SelectOption) => void;
}

export declare type SelectRootEmits<T = AcceptableValue> = {
    /** Event handler called when the value changes. */
    'update:modelValue': [value: T];
    /** Event handler called when the open state of the context menu changes. */
    'update:open': [value: boolean];
};

export declare interface SelectRootProps<T = AcceptableValue> extends FormFieldProps {
    /** The controlled open state of the Select. Can be bind as `v-model:open`. */
    open?: boolean;
    /** The open state of the select when it is initially rendered. Use when you do not need to control its open state. */
    defaultOpen?: boolean;
    /** The value of the select when initially rendered. Use when you do not need to control the state of the Select */
    defaultValue?: T | Array<T>;
    /** The controlled value of the Select. Can be bind as `v-model`. */
    modelValue?: T | Array<T>;
    /** Use this to compare objects by a particular field, or pass your own comparison function for complete control over how objects are compared. */
    by?: string | ((a: T, b: T) => boolean);
    /** The reading direction of the combobox when applicable. <br> If omitted, inherits globally from `ConfigProvider` or assumes LTR (left-to-right) reading mode. */
    dir?: Direction;
    /** Whether multiple options can be selected or not. */
    multiple?: boolean;
    /** Native html input `autocomplete` attribute. */
    autocomplete?: string;
    /** When `true`, prevents the user from interacting with Select */
    disabled?: boolean;
}

export declare const SelectScrollDownButton: __VLS_WithTemplateSlots_231<DefineComponent<SelectScrollDownButtonProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<SelectScrollDownButtonProps> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface SelectScrollDownButtonProps extends PrimitiveProps {
}

export declare const SelectScrollUpButton: __VLS_WithTemplateSlots_232<DefineComponent<SelectScrollUpButtonProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<SelectScrollUpButtonProps> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface SelectScrollUpButtonProps extends PrimitiveProps {
}

export declare const SelectSeparator: __VLS_WithTemplateSlots_233<DefineComponent<SelectSeparatorProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<SelectSeparatorProps> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface SelectSeparatorProps extends PrimitiveProps {
}

export declare const SelectTrigger: __VLS_WithTemplateSlots_234<DefineComponent<SelectTriggerProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<SelectTriggerProps> & Readonly<{}>, {
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface SelectTriggerProps extends PopperAnchorProps {
    disabled?: boolean;
}

export declare const SelectValue: __VLS_WithTemplateSlots_235<DefineComponent<SelectValueProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<SelectValueProps> & Readonly<{}>, {
placeholder: string;
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {
        selectedLabel: string[];
        modelValue: AcceptableValue | AcceptableValue[] | undefined;
    }): any;
}>;

export declare interface SelectValueProps extends PrimitiveProps {
    /** The content that will be rendered inside the `SelectValue` when no `value` or `defaultValue` is set. */
    placeholder?: string;
}

export declare const SelectViewport: __VLS_WithTemplateSlots_236<DefineComponent<SelectViewportProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<SelectViewportProps> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface SelectViewportProps extends PrimitiveProps {
    /**
     * Will add `nonce` attribute to the style tag which can be used by Content Security Policy. <br> If omitted, inherits globally from `ConfigProvider`.
     */
    nonce?: string;
}

export declare const Separator: __VLS_WithTemplateSlots_237<DefineComponent<SeparatorProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<SeparatorProps> & Readonly<{}>, {
orientation: DataOrientation;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface SeparatorProps extends BaseSeparatorProps {
}

declare type Side = (typeof SIDE_OPTIONS)[number];

declare const SIDE_OPTIONS: readonly ["top", "right", "bottom", "left"];

declare interface SingleOrMultipleProps<T = AcceptableValue | AcceptableValue[]> {
    /**
     * Determines whether a "single" or "multiple" items can be selected at a time.
     *
     * This prop will overwrite the inferred type from `modelValue` and `defaultValue`.
     */
    type?: SingleOrMultipleType;
    /**
     * The controlled value of the active item(s).
     *
     * Use this when you need to control the state of the items. Can be binded with `v-model`
     */
    modelValue?: T;
    /**
     * The default active value of the item(s).
     *
     * Use when you do not need to control the state of the item(s).
     */
    defaultValue?: T;
}

declare type SingleOrMultipleType = 'single' | 'multiple';

export declare const SliderRange: __VLS_WithTemplateSlots_238<DefineComponent<SliderRangeProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<SliderRangeProps> & Readonly<{}>, {
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface SliderRangeProps extends PrimitiveProps {
}

export declare const SliderRoot: __VLS_WithTemplateSlots_239<DefineComponent<SliderRootProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {
"update:modelValue": (payload: number[] | undefined) => any;
valueCommit: (payload: number[]) => any;
}, string, PublicProps, Readonly<SliderRootProps> & Readonly<{
"onUpdate:modelValue"?: ((payload: number[] | undefined) => any) | undefined;
onValueCommit?: ((payload: number[]) => any) | undefined;
}>, {
defaultValue: number[];
disabled: boolean;
as: AsTag | Component;
orientation: DataOrientation;
step: number;
min: number;
max: number;
inverted: boolean;
minStepsBetweenThumbs: number;
thumbAlignment: ThumbAlignment;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, Readonly<{
    default?: (props: {
        /** Current slider values */
        modelValue: number[] | null;
    }) => any;
}> & {
    default?: (props: {
        /** Current slider values */
        modelValue: number[] | null;
    }) => any;
}>;

declare interface SliderRootContext {
    orientation: Ref<DataOrientation>;
    disabled: Ref<boolean>;
    min: Ref<number>;
    max: Ref<number>;
    modelValue?: Readonly<Ref<number[] | null | undefined>>;
    currentModelValue: ComputedRef<number[]>;
    valueIndexToChangeRef: Ref<number>;
    thumbElements: Ref<HTMLElement[]>;
    thumbAlignment: Ref<ThumbAlignment>;
}

export declare type SliderRootEmits = {
    /**
     * Event handler called when the slider value changes
     */
    'update:modelValue': [payload: number[] | undefined];
    /**
     * Event handler called when the value changes at the end of an interaction.
     *
     * Useful when you only need to capture a final value e.g. to update a backend service.
     */
    'valueCommit': [payload: number[]];
};

export declare interface SliderRootProps extends PrimitiveProps, FormFieldProps {
    /** The value of the slider when initially rendered. Use when you do not need to control the state of the slider. */
    defaultValue?: number[];
    /** The controlled value of the slider. Can be bind as `v-model`. */
    modelValue?: number[] | null;
    /** When `true`, prevents the user from interacting with the slider. */
    disabled?: boolean;
    /** The orientation of the slider. */
    orientation?: DataOrientation;
    /** The reading direction of the combobox when applicable. <br> If omitted, inherits globally from `ConfigProvider` or assumes LTR (left-to-right) reading mode. */
    dir?: Direction;
    /** Whether the slider is visually inverted. */
    inverted?: boolean;
    /** The minimum value for the range. */
    min?: number;
    /** The maximum value for the range. */
    max?: number;
    /** The stepping interval. */
    step?: number;
    /** The minimum permitted steps between multiple thumbs. */
    minStepsBetweenThumbs?: number;
    /**
     * The alignment of the slider thumb.
     * - `contain`: thumbs will be contained within the bounds of the track.
     * - `overflow`: thumbs will not be bound by the track. No extra offset will be added.
     * @defaultValue 'contain'
     */
    thumbAlignment?: ThumbAlignment;
}

export declare const SliderThumb: __VLS_WithTemplateSlots_240<DefineComponent<SliderThumbProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<SliderThumbProps> & Readonly<{}>, {
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface SliderThumbProps extends PrimitiveProps {
}

export declare const SliderTrack: __VLS_WithTemplateSlots_241<DefineComponent<SliderTrackProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<SliderTrackProps> & Readonly<{}>, {
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface SliderTrackProps extends PrimitiveProps {
}

export declare const Slot: DefineComponent<    {}, () => VNode<RendererNode, RendererElement, {
[key: string]: any;
}> | VNode<RendererNode, RendererElement, {
[key: string]: any;
}>[] | null, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<{}> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, true, {}, any>;

export declare const SplitterGroup: __VLS_WithTemplateSlots_242<DefineComponent<SplitterGroupProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {
layout: (val: number[]) => any;
}, string, PublicProps, Readonly<SplitterGroupProps> & Readonly<{
onLayout?: ((val: number[]) => any) | undefined;
}>, {
storage: PanelGroupStorage;
autoSaveId: string | null;
keyboardResizeBy: number | null;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, Readonly<{
    default?: (props: {
        /** Current size of layout */
        layout: number[];
    }) => any;
}> & {
    default?: (props: {
        /** Current size of layout */
        layout: number[];
    }) => any;
}>;

export declare type SplitterGroupEmits = {
    /** Event handler called when group layout changes */
    layout: [val: number[]];
};

export declare interface SplitterGroupProps extends PrimitiveProps {
    /** Group id; falls back to `useId` when not provided. */
    id?: string | null;
    /** Unique id used to auto-save group arrangement via `localStorage`. */
    autoSaveId?: string | null;
    /** The group orientation of splitter. */
    direction: Direction_6;
    /** Step size when arrow key was pressed. */
    keyboardResizeBy?: number | null;
    /** Custom storage API; defaults to localStorage */
    storage?: PanelGroupStorage;
}

export declare const SplitterPanel: __VLS_WithTemplateSlots_243<DefineComponent<SplitterPanelProps, {
/** If panel is `collapsible`, collapse it fully. */
collapse: () => void;
/** If panel is currently collapsed, expand it to its most recent size. */
expand: () => void;
/** Gets the current size of the panel as a percentage (1 - 100). */
getSize(): number;
/** Resize panel to the specified percentage (1 - 100). */
resize: (size: number) => void;
/** Returns `true` if the panel is currently collapsed */
isCollapsed: ComputedRef<boolean>;
/** Returns `true` if the panel is currently not collapsed */
isExpanded: ComputedRef<boolean>;
}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {
resize: (size: number, prevSize: number | undefined) => any;
collapse: () => any;
expand: () => any;
}, string, PublicProps, Readonly<SplitterPanelProps> & Readonly<{
onResize?: ((size: number, prevSize: number | undefined) => any) | undefined;
onCollapse?: (() => any) | undefined;
onExpand?: (() => any) | undefined;
}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, Readonly<{
    default?: (props: {
        /** Is the panel collapsed */
        isCollapsed: boolean;
        /** Is the panel expanded */
        isExpanded: boolean;
        /** If panel is `collapsible`, collapse it fully. */
        collapse: () => void;
        /** If panel is currently collapsed, expand it to its most recent size. */
        expand: () => void;
        /** Resize panel to the specified percentage (1 - 100). */
        resize: (size: number) => void;
    }) => any;
}> & {
    default?: (props: {
        /** Is the panel collapsed */
        isCollapsed: boolean;
        /** Is the panel expanded */
        isExpanded: boolean;
        /** If panel is `collapsible`, collapse it fully. */
        collapse: () => void;
        /** If panel is currently collapsed, expand it to its most recent size. */
        expand: () => void;
        /** Resize panel to the specified percentage (1 - 100). */
        resize: (size: number) => void;
    }) => any;
}>;

export declare type SplitterPanelEmits = {
    /** Event handler called when panel is collapsed. */
    collapse: [];
    /** Event handler called when panel is expanded. */
    expand: [];
    /** Event handler called when panel is resized; size parameter is a numeric value between 1-100.  */
    resize: [size: number, prevSize: number | undefined];
};

export declare interface SplitterPanelProps extends PrimitiveProps {
    /** The size of panel when it is collapsed. */
    collapsedSize?: number;
    /** Should panel collapse when resized beyond its `minSize`. When `true`, it will be collapsed to `collapsedSize`. */
    collapsible?: boolean;
    /** Initial size of panel (numeric value between 1-100) */
    defaultSize?: number;
    /** Panel id (unique within group); falls back to `useId` when not provided */
    id?: string;
    /** The maximum allowable size of panel (numeric value between 1-100); defaults to `100` */
    maxSize?: number;
    /** The minimum allowable size of panel (numeric value between 1-100); defaults to `10` */
    minSize?: number;
    /** The order of panel within group; required for groups with conditionally rendered panels */
    order?: number;
}

export declare const SplitterResizeHandle: __VLS_WithTemplateSlots_244<DefineComponent<SplitterResizeHandleProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {
dragging: (isDragging: boolean) => any;
}, string, PublicProps, Readonly<SplitterResizeHandleProps> & Readonly<{
onDragging?: ((isDragging: boolean) => any) | undefined;
}>, {
tabindex: number;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare type SplitterResizeHandleEmits = {
    /** Event handler called when dragging the handler. */
    dragging: [isDragging: boolean];
};

export declare interface SplitterResizeHandleProps extends PrimitiveProps {
    /** Resize handle id (unique within group); falls back to `useId` when not provided */
    id?: string;
    /** Allow this much margin when determining resizable handle hit detection */
    hitAreaMargins?: PointerHitAreaMargins;
    /** Tabindex for the handle */
    tabindex?: number;
    /** Disable drag handle */
    disabled?: boolean;
}

export declare const StepperDescription: __VLS_WithTemplateSlots_245<DefineComponent<StepperDescriptionProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<StepperDescriptionProps> & Readonly<{}>, {
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface StepperDescriptionProps extends PrimitiveProps {
}

export declare const StepperIndicator: __VLS_WithTemplateSlots_246<DefineComponent<StepperIndicatorProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<StepperIndicatorProps> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, Readonly<{
    default?: (props: {
        /** Current step */
        step: number;
    }) => any;
}> & {
    default?: (props: {
        /** Current step */
        step: number;
    }) => any;
}>;

export declare interface StepperIndicatorProps extends PrimitiveProps {
}

export declare const StepperItem: __VLS_WithTemplateSlots_247<DefineComponent<StepperItemProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<StepperItemProps> & Readonly<{}>, {
disabled: boolean;
completed: boolean;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, Readonly<{
    default?: (props: {
        /** The current state of the stepper item */
        state: StepperState;
    }) => any;
}> & {
    default?: (props: {
        /** The current state of the stepper item */
        state: StepperState;
    }) => any;
}>;

declare interface StepperItemContext {
    titleId: string;
    descriptionId: string;
    step: Ref<number>;
    state: Ref<StepperState>;
    disabled: Ref<boolean>;
    isFocusable: Ref<boolean>;
}

export declare interface StepperItemProps extends PrimitiveProps {
    /** A unique value that associates the stepper item with an index */
    step: number;
    /** When `true`, prevents the user from interacting with the step. */
    disabled?: boolean;
    /** Shows whether the step is completed. */
    completed?: boolean;
}

export declare const StepperRoot: __VLS_WithTemplateSlots_248<DefineComponent<StepperRootProps, {
goToStep: (step: number) => void;
nextStep: () => void;
prevStep: () => void;
modelValue: WritableComputedRef<number | undefined, number | undefined>;
totalSteps: ComputedRef<number>;
isNextDisabled: ComputedRef<boolean>;
isPrevDisabled: ComputedRef<boolean>;
isFirstStep: ComputedRef<boolean>;
isLastStep: ComputedRef<boolean>;
hasNext: () => boolean;
hasPrev: () => boolean;
}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {
"update:modelValue": (payload: number | undefined) => any;
}, string, PublicProps, Readonly<StepperRootProps> & Readonly<{
"onUpdate:modelValue"?: ((payload: number | undefined) => any) | undefined;
}>, {
defaultValue: number;
orientation: DataOrientation;
linear: boolean;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, Readonly<{
    default?: (props: {
        /** Current step */
        modelValue: number | undefined;
        /** Total number of steps */
        totalSteps: number;
        /** Whether or not the next step is disabled */
        isNextDisabled: boolean;
        /** Whether or not the previous step is disabled */
        isPrevDisabled: boolean;
        /** Whether or not the first step is active */
        isFirstStep: boolean;
        /** Whether or not the last step is active */
        isLastStep: boolean;
        /** Go to a specific step */
        goToStep: (step: number) => void;
        /** Go to the next step */
        nextStep: () => void;
        /** Go to the previous step */
        prevStep: () => void;
        /** Whether or not there is a next step */
        hasNext: () => boolean;
        /** Whether or not there is a previous step */
        hasPrev: () => boolean;
    }) => any;
}> & {
    default?: (props: {
        /** Current step */
        modelValue: number | undefined;
        /** Total number of steps */
        totalSteps: number;
        /** Whether or not the next step is disabled */
        isNextDisabled: boolean;
        /** Whether or not the previous step is disabled */
        isPrevDisabled: boolean;
        /** Whether or not the first step is active */
        isFirstStep: boolean;
        /** Whether or not the last step is active */
        isLastStep: boolean;
        /** Go to a specific step */
        goToStep: (step: number) => void;
        /** Go to the next step */
        nextStep: () => void;
        /** Go to the previous step */
        prevStep: () => void;
        /** Whether or not there is a next step */
        hasNext: () => boolean;
        /** Whether or not there is a previous step */
        hasPrev: () => boolean;
    }) => any;
}>;

declare interface StepperRootContext {
    modelValue: Ref<number | undefined>;
    changeModelValue: (value: number) => void;
    orientation: Ref<DataOrientation>;
    dir: Ref<Direction>;
    linear: Ref<boolean>;
    totalStepperItems: Ref<Set<HTMLElement>>;
}

export declare type StepperRootEmits = {
    /** Event handler called when the value changes */
    'update:modelValue': [payload: number | undefined];
};

export declare interface StepperRootProps extends PrimitiveProps {
    /**
     * The value of the step that should be active when initially rendered. Use when you do not need to control the state of the steps.
     */
    defaultValue?: number;
    /**
     * The orientation the steps are laid out.
     * Mainly so arrow navigation is done accordingly (left & right vs. up & down).
     * @defaultValue horizontal
     */
    orientation?: DataOrientation;
    /**
     * The reading direction of the combobox when applicable. <br> If omitted, inherits globally from `ConfigProvider` or assumes LTR (left-to-right) reading mode.
     */
    dir?: Direction;
    /** The controlled value of the step to activate. Can be bound as `v-model`. */
    modelValue?: number;
    /** Whether or not the steps must be completed in order. */
    linear?: boolean;
}

export declare const StepperSeparator: __VLS_WithTemplateSlots_249<DefineComponent<StepperSeparatorProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<StepperSeparatorProps> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface StepperSeparatorProps extends SeparatorProps {
}

declare type StepperState = 'completed' | 'active' | 'inactive';

export declare const StepperTitle: __VLS_WithTemplateSlots_250<DefineComponent<StepperTitleProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<StepperTitleProps> & Readonly<{}>, {
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface StepperTitleProps extends PrimitiveProps {
}

export declare const StepperTrigger: __VLS_WithTemplateSlots_251<DefineComponent<StepperTriggerProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<StepperTriggerProps> & Readonly<{}>, {
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface StepperTriggerProps extends PrimitiveProps {
}

declare type StringOrNumber = string | number;

declare type SubmitMode = 'blur' | 'enter' | 'none' | 'both';

declare type SwipeDirection = 'up' | 'down' | 'left' | 'right';

declare type SwipeEvent = {
    currentTarget: EventTarget & HTMLElement;
} & Omit<CustomEvent<{
    originalEvent: PointerEvent;
    delta: {
        x: number;
        y: number;
    };
}>, 'currentTarget'>;

export declare const SwitchRoot: __VLS_WithTemplateSlots_252<DefineComponent<SwitchRootProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {
"update:modelValue": (payload: boolean) => any;
}, string, PublicProps, Readonly<SwitchRootProps> & Readonly<{
"onUpdate:modelValue"?: ((payload: boolean) => any) | undefined;
}>, {
value: string;
as: AsTag | Component;
modelValue: boolean | null;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, Readonly<{
    default?: (props: {
        /** Current value */
        modelValue: boolean;
    }) => any;
}> & {
    default?: (props: {
        /** Current value */
        modelValue: boolean;
    }) => any;
}>;

declare interface SwitchRootContext {
    modelValue?: Ref<boolean>;
    toggleCheck: () => void;
    disabled: Ref<boolean>;
}

export declare type SwitchRootEmits = {
    /** Event handler called when the value of the switch changes. */
    'update:modelValue': [payload: boolean];
};

export declare interface SwitchRootProps extends PrimitiveProps, FormFieldProps {
    /** The state of the switch when it is initially rendered. Use when you do not need to control its state. */
    defaultValue?: boolean;
    /** The controlled state of the switch. Can be bind as `v-model`. */
    modelValue?: boolean | null;
    /** When `true`, prevents the user from interacting with the switch. */
    disabled?: boolean;
    id?: string;
    /** The value given as data when submitted with a `name`. */
    value?: string;
}

export declare const SwitchThumb: __VLS_WithTemplateSlots_253<DefineComponent<SwitchThumbProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<SwitchThumbProps> & Readonly<{}>, {
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface SwitchThumbProps extends PrimitiveProps {
}

export declare const TabsContent: __VLS_WithTemplateSlots_254<DefineComponent<TabsContentProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<TabsContentProps> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface TabsContentProps extends PrimitiveProps {
    /** A unique value that associates the content with a trigger. */
    value: StringOrNumber;
    /**
     * Used to force mounting when more control is needed. Useful when
     * controlling animation with Vue animation libraries.
     */
    forceMount?: boolean;
}

export declare const TabsIndicator: __VLS_WithTemplateSlots_255<DefineComponent<TabsIndicatorProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<TabsIndicatorProps> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface TabsIndicatorProps extends PrimitiveProps {
}

export declare const TabsList: __VLS_WithTemplateSlots_256<DefineComponent<TabsListProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<TabsListProps> & Readonly<{}>, {
loop: boolean;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface TabsListProps extends PrimitiveProps {
    /** When `true`, keyboard navigation will loop from last tab to first, and vice versa. */
    loop?: boolean;
}

export declare const TabsRoot: <T extends StringOrNumber = StringOrNumber>(__VLS_props: NonNullable<Awaited<typeof __VLS_setup>>["props"], __VLS_ctx?: __VLS_PrettifyLocal_12<Pick<NonNullable<Awaited<typeof __VLS_setup>>, "attrs" | "emit" | "slots">>, __VLS_expose?: NonNullable<Awaited<typeof __VLS_setup>>["expose"], __VLS_setup?: Promise<{
    props: __VLS_PrettifyLocal_12<Pick<Partial<{}> & Omit<{
        readonly "onUpdate:modelValue"?: ((payload: T) => any) | undefined;
    } & VNodeProps & AllowedComponentProps & ComponentCustomProps, never>, "onUpdate:modelValue"> & TabsRootProps<T> & Partial<{}>> & PublicProps;
    expose(exposed: ShallowUnwrapRef<    {}>): void;
    attrs: any;
    slots: Readonly<{
        default?: (props: {
            /** Current input values */
            modelValue: T | undefined;
        }) => any;
    }> & {
        default?: (props: {
            /** Current input values */
            modelValue: T | undefined;
        }) => any;
    };
    emit: (evt: "update:modelValue", payload: T) => void;
}>) => VNode & {
    __ctx?: Awaited<typeof __VLS_setup>;
};

declare interface TabsRootContext {
    modelValue: Ref<StringOrNumber | undefined>;
    changeModelValue: (value: StringOrNumber) => void;
    orientation: Ref<DataOrientation>;
    dir: Ref<Direction>;
    unmountOnHide: Ref<boolean>;
    activationMode: 'automatic' | 'manual';
    baseId: string;
    tabsList: Ref<HTMLElement | undefined>;
}

export declare type TabsRootEmits<T extends StringOrNumber = StringOrNumber> = {
    /** Event handler called when the value changes */
    'update:modelValue': [payload: T];
};

export declare interface TabsRootProps<T extends StringOrNumber = StringOrNumber> extends PrimitiveProps {
    /**
     * The value of the tab that should be active when initially rendered. Use when you do not need to control the state of the tabs
     */
    defaultValue?: T;
    /**
     * The orientation the tabs are laid out.
     * Mainly so arrow navigation is done accordingly (left & right vs. up & down)
     * @defaultValue horizontal
     */
    orientation?: DataOrientation;
    /**
     * The reading direction of the combobox when applicable. <br> If omitted, inherits globally from `ConfigProvider` or assumes LTR (left-to-right) reading mode.
     */
    dir?: Direction;
    /**
     * Whether a tab is activated automatically (on focus) or manually (on click).
     * @defaultValue automatic
     */
    activationMode?: 'automatic' | 'manual';
    /** The controlled value of the tab to activate. Can be bind as `v-model`. */
    modelValue?: T;
    /**
     * When `true`, the element will be unmounted on closed state.
     *
     * @defaultValue `true`
     */
    unmountOnHide?: boolean;
}

export declare const TabsTrigger: __VLS_WithTemplateSlots_257<DefineComponent<TabsTriggerProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<TabsTriggerProps> & Readonly<{}>, {
disabled: boolean;
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface TabsTriggerProps extends PrimitiveProps {
    /** A unique value that associates the trigger with a content. */
    value: StringOrNumber;
    /** When `true`, prevents the user from interacting with the tab. */
    disabled?: boolean;
}

export declare const TagsInputClear: __VLS_WithTemplateSlots_258<DefineComponent<TagsInputClearProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<TagsInputClearProps> & Readonly<{}>, {
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface TagsInputClearProps extends PrimitiveProps {
}

export declare const TagsInputInput: __VLS_WithTemplateSlots_259<DefineComponent<TagsInputInputProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<TagsInputInputProps> & Readonly<{}>, {
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface TagsInputInputProps extends PrimitiveProps {
    /** The placeholder character to use for empty tags input. */
    placeholder?: string;
    /** Focus on element when mounted. */
    autoFocus?: boolean;
    /** Maximum number of character allowed. */
    maxLength?: number;
}

export declare const TagsInputItem: __VLS_WithTemplateSlots_260<DefineComponent<TagsInputItemProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<TagsInputItemProps> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

declare interface TagsInputItemContext {
    value: Ref<AcceptableInputValue>;
    displayValue: ComputedRef<string>;
    isSelected: Ref<boolean>;
    disabled?: Ref<boolean>;
    textId: string;
}

export declare const TagsInputItemDelete: __VLS_WithTemplateSlots_261<DefineComponent<TagsInputItemDeleteProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<TagsInputItemDeleteProps> & Readonly<{}>, {
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface TagsInputItemDeleteProps extends PrimitiveProps {
}

export declare interface TagsInputItemProps extends PrimitiveProps {
    /** Value associated with the tags */
    value: AcceptableInputValue;
    /** When `true`, prevents the user from interacting with the tags input. */
    disabled?: boolean;
}

export declare const TagsInputItemText: __VLS_WithTemplateSlots_262<DefineComponent<TagsInputItemTextProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<TagsInputItemTextProps> & Readonly<{}>, {
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface TagsInputItemTextProps extends PrimitiveProps {
}

export declare const TagsInputRoot: <T extends AcceptableInputValue = string>(__VLS_props: NonNullable<Awaited<typeof __VLS_setup>>["props"], __VLS_ctx?: __VLS_PrettifyLocal_13<Pick<NonNullable<Awaited<typeof __VLS_setup>>, "attrs" | "emit" | "slots">>, __VLS_expose?: NonNullable<Awaited<typeof __VLS_setup>>["expose"], __VLS_setup?: Promise<{
    props: __VLS_PrettifyLocal_13<Pick<Partial<{}> & Omit<{
        readonly onInvalid?: ((payload: T) => any) | undefined;
        readonly "onUpdate:modelValue"?: ((payload: T[]) => any) | undefined;
        readonly onAddTag?: ((payload: T) => any) | undefined;
        readonly onRemoveTag?: ((payload: T) => any) | undefined;
    } & VNodeProps & AllowedComponentProps & ComponentCustomProps, never>, "onInvalid" | "onUpdate:modelValue" | "onAddTag" | "onRemoveTag"> & TagsInputRootProps<T> & Partial<{}>> & PublicProps;
    expose(exposed: ShallowUnwrapRef<    {}>): void;
    attrs: any;
    slots: Readonly<{
        default?: (props: {
            /** Current input values */
            modelValue: AcceptableInputValue[];
        }) => any;
    }> & {
        default?: (props: {
            /** Current input values */
            modelValue: AcceptableInputValue[];
        }) => any;
    };
    emit: ((evt: "invalid", payload: T) => void) & ((evt: "update:modelValue", payload: T[]) => void) & ((evt: "addTag", payload: T) => void) & ((evt: "removeTag", payload: T) => void);
}>) => VNode & {
    __ctx?: Awaited<typeof __VLS_setup>;
};

declare interface TagsInputRootContext<T = AcceptableInputValue> {
    modelValue: Ref<Array<T>>;
    onAddValue: (payload: string) => boolean;
    onRemoveValue: (index: number) => void;
    onInputKeydown: (event: KeyboardEvent) => void;
    selectedElement: Ref<HTMLElement | undefined>;
    isInvalidInput: Ref<boolean>;
    addOnPaste: Ref<boolean>;
    addOnTab: Ref<boolean>;
    addOnBlur: Ref<boolean>;
    disabled: Ref<boolean>;
    delimiter: Ref<string | RegExp>;
    dir: Ref<Direction>;
    max: Ref<number>;
    id: Ref<string | undefined> | undefined;
    displayValue: (value: T) => string;
}

export declare type TagsInputRootEmits<T = AcceptableInputValue> = {
    /** Event handler called when the value changes */
    'update:modelValue': [payload: Array<T>];
    /** Event handler called when the value is invalid */
    'invalid': [payload: T];
    /** Event handler called when tag is added */
    'addTag': [payload: T];
    /** Event handler called when tag is removed */
    'removeTag': [payload: T];
};

export declare interface TagsInputRootProps<T = AcceptableInputValue> extends PrimitiveProps, FormFieldProps {
    /** The controlled value of the tags input. Can be bind as `v-model`. */
    modelValue?: Array<T> | null;
    /** The value of the tags that should be added. Use when you do not need to control the state of the tags input */
    defaultValue?: Array<T>;
    /** When `true`, allow adding tags on paste. Work in conjunction with delimiter prop. */
    addOnPaste?: boolean;
    /** When `true` allow adding tags on tab keydown */
    addOnTab?: boolean;
    /** When `true` allow adding tags blur input */
    addOnBlur?: boolean;
    /** When `true`, allow duplicated tags. */
    duplicate?: boolean;
    /** When `true`, prevents the user from interacting with the tags input. */
    disabled?: boolean;
    /** The character or regular expression to trigger the addition of a new tag. Also used to split tags for `@paste` event */
    delimiter?: string | RegExp;
    /** The reading direction of the combobox when applicable. <br> If omitted, inherits globally from `ConfigProvider` or assumes LTR (left-to-right) reading mode. */
    dir?: Direction;
    /** Maximum number of tags. */
    max?: number;
    id?: string;
    /** Convert the input value to the desired type. Mandatory when using objects as values and using `TagsInputInput` */
    convertValue?: (value: string) => T;
    /** Display the value of the tag. Useful when you want to apply modifications to the value like adding a suffix or when using object as values */
    displayValue?: (value: T) => string;
}

declare interface TeleportProps {
    /**
     * Vue native teleport component prop `:to`
     *
     * {@link https://vuejs.org/guide/built-ins/teleport.html#basic-usage}
     */
    to?: string | HTMLElement;
    /**
     * Disable teleport and render the component inline
     *
     * {@link https://vuejs.org/guide/built-ins/teleport.html#disabling-teleport}
     */
    disabled?: boolean;
    /**
     * Defer the resolving of a Teleport target until other parts of the
     * application have mounted (requires Vue 3.5.0+)
     *
     * {@link https://vuejs.org/guide/built-ins/teleport.html#deferred-teleport}
     */
    defer?: boolean;
    /**
     * Used to force mounting when more control is needed. Useful when
     * controlling animation with Vue animation libraries.
     */
    forceMount?: boolean;
}

declare type ThumbAlignment = 'contain' | 'overflow';

declare const TIME_SEGMENT_PARTS: readonly ["hour", "minute", "second", "dayPeriod"];

export declare const TimeFieldInput: __VLS_WithTemplateSlots_263<DefineComponent<TimeFieldInputProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<TimeFieldInputProps> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface TimeFieldInputProps extends PrimitiveProps {
    /** The part of the date to render */
    part: SegmentPart;
}

export declare const TimeFieldRoot: __VLS_WithTemplateSlots_264<DefineComponent<TimeFieldRootProps, {
/** Helper to set the focused element inside the DateField */
setFocusedElement: (el: HTMLElement) => void;
}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {
"update:modelValue": (date: TimeValue | undefined) => any;
"update:placeholder": (date: TimeValue) => any;
}, string, PublicProps, Readonly<TimeFieldRootProps> & Readonly<{
"onUpdate:modelValue"?: ((date: TimeValue | undefined) => any) | undefined;
"onUpdate:placeholder"?: ((date: TimeValue) => any) | undefined;
}>, {
defaultValue: TimeValue;
placeholder: TimeValue;
disabled: boolean;
readonly: boolean;
}, {}, {}, {}, string, ComponentProvideOptions, false, {
primitiveElement: CreateComponentPublicInstanceWithMixins<Readonly<ExtractPropTypes<    {
asChild: {
type: BooleanConstructor;
default: boolean;
};
as: {
type: PropType<AsTag | Component>;
default: string;
};
}>> & Readonly<{}>, () => VNode<RendererNode, RendererElement, {
[key: string]: any;
}>, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, PublicProps, {
asChild: boolean;
as: AsTag | Component;
}, true, {}, {}, GlobalComponents, GlobalDirectives, string, {}, any, ComponentProvideOptions, {
P: {};
B: {};
D: {};
C: {};
M: {};
Defaults: {};
}, Readonly<ExtractPropTypes<    {
asChild: {
type: BooleanConstructor;
default: boolean;
};
as: {
type: PropType<AsTag | Component>;
default: string;
};
}>> & Readonly<{}>, () => VNode<RendererNode, RendererElement, {
[key: string]: any;
}>, {}, {}, {}, {
asChild: boolean;
as: AsTag | Component;
}> | null;
}, any>, Readonly<{
    default?: (props: {
        /** The current time of the field */
        modelValue: TimeValue | undefined;
        /** The time field segment contents */
        segments: {
            part: SegmentPart;
            value: string;
        }[];
        /** Value if the input is invalid */
        isInvalid: boolean;
    }) => any;
}> & {
    default?: (props: {
        /** The current time of the field */
        modelValue: TimeValue | undefined;
        /** The time field segment contents */
        segments: {
            part: SegmentPart;
            value: string;
        }[];
        /** Value if the input is invalid */
        isInvalid: boolean;
    }) => any;
}>;

declare type TimeFieldRootContext = {
    locale: Ref<string>;
    modelValue: Ref<DateValue | undefined>;
    placeholder: Ref<DateValue>;
    isInvalid: Ref<boolean>;
    disabled: Ref<boolean>;
    readonly: Ref<boolean>;
    formatter: Formatter;
    hourCycle: HourCycle;
    step: Ref<DateStep>;
    segmentValues: Ref<SegmentValueObj>;
    segmentContents: Ref<{
        part: SegmentPart;
        value: string;
    }[]>;
    elements: Ref<Set<HTMLElement>>;
    focusNext: () => void;
    setFocusedElement: (el: HTMLElement) => void;
};

export declare type TimeFieldRootEmits = {
    /** Event handler called whenever the model value changes */
    'update:modelValue': [date: TimeValue | undefined];
    /** Event handler called whenever the placeholder value changes */
    'update:placeholder': [date: TimeValue];
};

export declare interface TimeFieldRootProps extends PrimitiveProps, FormFieldProps {
    /** The default value for the calendar */
    defaultValue?: TimeValue;
    /** The default placeholder date */
    defaultPlaceholder?: TimeValue;
    /** The placeholder date, which is used to determine what time to display when no time is selected. This updates as the user navigates the field */
    placeholder?: TimeValue;
    /** The controlled checked state of the field. Can be bound as `v-model`. */
    modelValue?: TimeValue | null;
    /** The hour cycle used for formatting times. Defaults to the local preference */
    hourCycle?: HourCycle;
    /** The stepping interval for the time fields. Defaults to `1`. */
    step?: DateStep;
    /** The granularity to use for formatting times. Defaults to minute if a Time is provided, otherwise defaults to minute. The field will render segments for each part of the date up to and including the specified granularity */
    granularity?: 'hour' | 'minute' | 'second';
    /** Whether or not to hide the time zone segment of the field */
    hideTimeZone?: boolean;
    /** The maximum date that can be selected */
    maxValue?: TimeValue;
    /** The minimum date that can be selected */
    minValue?: TimeValue;
    /** The locale to use for formatting dates */
    locale?: string;
    /** Whether or not the time field is disabled */
    disabled?: boolean;
    /** Whether or not the time field is readonly */
    readonly?: boolean;
    /** Id of the element */
    id?: string;
    /** The reading direction of the time field when applicable. <br> If omitted, inherits globally from `ConfigProvider` or assumes LTR (left-to-right) reading mode. */
    dir?: Direction;
}

declare type TimeSegmentObj = {
    [K in TimeSegmentPart]: K extends 'dayPeriod' ? DayPeriod : number | null;
};

declare type TimeSegmentPart = (typeof TIME_SEGMENT_PARTS)[number];

declare type TimeValue = Time | CalendarDateTime | ZonedDateTime;

export declare const ToastAction: __VLS_WithTemplateSlots_265<DefineComponent<ToastActionProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<ToastActionProps> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface ToastActionProps extends ToastCloseProps {
    /**
     * A short description for an alternate way to carry out the action. For screen reader users
     * who will not be able to navigate to the button easily/quickly.
     * @example <ToastAction altText="Goto account settings to upgrade">Upgrade</ToastAction>
     * @example <ToastAction altText="Undo (Alt+U)">Undo</ToastAction>
     */
    altText: string;
}

export declare const ToastClose: __VLS_WithTemplateSlots_266<DefineComponent<ToastCloseProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<ToastCloseProps> & Readonly<{}>, {
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface ToastCloseProps extends PrimitiveProps {
}

export declare const ToastDescription: __VLS_WithTemplateSlots_267<DefineComponent<ToastDescriptionProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<ToastDescriptionProps> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface ToastDescriptionProps extends PrimitiveProps {
}

export declare const ToastPortal: __VLS_WithTemplateSlots_268<DefineComponent<ToastPortalProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<ToastPortalProps> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface ToastPortalProps extends TeleportProps {
}

export declare const ToastProvider: __VLS_WithTemplateSlots_269<DefineComponent<ToastProviderProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<ToastProviderProps> & Readonly<{}>, {
label: string;
duration: number;
swipeDirection: SwipeDirection;
swipeThreshold: number;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

declare type ToastProviderContext = {
    label: Ref<string>;
    duration: Ref<number>;
    swipeDirection: Ref<SwipeDirection>;
    swipeThreshold: Ref<number>;
    toastCount: Ref<number>;
    viewport: Ref<HTMLElement | undefined>;
    onViewportChange: (viewport: HTMLElement) => void;
    onToastAdd: () => void;
    onToastRemove: () => void;
    isFocusedToastEscapeKeyDownRef: Ref<boolean>;
    isClosePausedRef: Ref<boolean>;
};

export declare interface ToastProviderProps {
    /**
     * An author-localized label for each toast. Used to help screen reader users
     * associate the interruption with a toast.
     * @defaultValue 'Notification'
     */
    label?: string;
    /**
     * Time in milliseconds that each toast should remain visible for.
     * @defaultValue 5000
     */
    duration?: number;
    /**
     * Direction of pointer swipe that should close the toast.
     * @defaultValue 'right'
     */
    swipeDirection?: SwipeDirection;
    /**
     * Distance in pixels that the swipe must pass before a close is triggered.
     * @defaultValue 50
     */
    swipeThreshold?: number;
}

export declare const ToastRoot: __VLS_WithTemplateSlots_270<DefineComponent<ToastRootProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {
pause: () => any;
"update:open": (value: boolean) => any;
escapeKeyDown: (event: KeyboardEvent) => any;
resume: () => any;
swipeStart: (event: SwipeEvent) => any;
swipeMove: (event: SwipeEvent) => any;
swipeCancel: (event: SwipeEvent) => any;
swipeEnd: (event: SwipeEvent) => any;
}, string, PublicProps, Readonly<ToastRootProps> & Readonly<{
onPause?: (() => any) | undefined;
"onUpdate:open"?: ((value: boolean) => any) | undefined;
onEscapeKeyDown?: ((event: KeyboardEvent) => any) | undefined;
onResume?: (() => any) | undefined;
onSwipeStart?: ((event: SwipeEvent) => any) | undefined;
onSwipeMove?: ((event: SwipeEvent) => any) | undefined;
onSwipeCancel?: ((event: SwipeEvent) => any) | undefined;
onSwipeEnd?: ((event: SwipeEvent) => any) | undefined;
}>, {
type: "foreground" | "background";
as: AsTag | Component;
defaultOpen: boolean;
open: boolean;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, Readonly<{
    default?: (props: {
        /** Current open state */
        open: boolean;
        /** Remaining time (in ms) */
        remaining: number;
        /** Total time the toast will remain visible for (in ms) */
        duration: number;
    }) => any;
}> & {
    default?: (props: {
        /** Current open state */
        open: boolean;
        /** Remaining time (in ms) */
        remaining: number;
        /** Total time the toast will remain visible for (in ms) */
        duration: number;
    }) => any;
}>;

export declare type ToastRootEmits = Omit<ToastRootImplEmits, 'close'> & {
    /** Event handler called when the open state changes */
    'update:open': [value: boolean];
};

declare type ToastRootImplEmits = {
    close: [];
    /** Event handler called when the escape key is down. It can be prevented by calling `event.preventDefault`. */
    escapeKeyDown: [event: KeyboardEvent];
    /** Event handler called when the dismiss timer is paused. This occurs when the pointer is moved over the viewport, the viewport is focused or when the window is blurred. */
    pause: [];
    /** Event handler called when the dismiss timer is resumed. This occurs when the pointer is moved away from the viewport, the viewport is blurred or when the window is focused. */
    resume: [];
    /** Event handler called when starting a swipe interaction. It can be prevented by calling `event.preventDefault`. */
    swipeStart: [event: SwipeEvent];
    /** Event handler called during a swipe interaction. It can be prevented by calling `event.preventDefault`. */
    swipeMove: [event: SwipeEvent];
    /** Event handler called when swipe interaction is cancelled. It can be prevented by calling `event.preventDefault`. */
    swipeCancel: [event: SwipeEvent];
    /** Event handler called at the end of a swipe interaction. It can be prevented by calling `event.preventDefault`. */
    swipeEnd: [event: SwipeEvent];
};

declare interface ToastRootImplProps extends PrimitiveProps {
    /**
     * Control the sensitivity of the toast for accessibility purposes.
     *
     * For toasts that are the result of a user action, choose `foreground`. Toasts generated from background tasks should use `background`.
     */
    type?: 'foreground' | 'background';
    /**
     * The controlled open state of the dialog. Can be bind as `v-model:open`.
     */
    open?: boolean;
    /**
     * Time in milliseconds that toast should remain visible for. Overrides value
     * given to `ToastProvider`.
     */
    duration?: number;
}

export declare interface ToastRootProps extends ToastRootImplProps {
    /** The open state of the dialog when it is initially rendered. Use when you do not need to control its open state. */
    defaultOpen?: boolean;
    /**
     * Used to force mounting when more control is needed. Useful when
     * controlling animation with Vue animation libraries.
     */
    forceMount?: boolean;
}

export declare const ToastTitle: __VLS_WithTemplateSlots_271<DefineComponent<ToastTitleProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<ToastTitleProps> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface ToastTitleProps extends PrimitiveProps {
}

export declare const ToastViewport: __VLS_WithTemplateSlots_272<DefineComponent<ToastViewportProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<ToastViewportProps> & Readonly<{}>, {
label: string | ((hotkey: string) => string);
as: AsTag | Component;
hotkey: string[];
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface ToastViewportProps extends PrimitiveProps {
    /**
     * The keys to use as the keyboard shortcut that will move focus to the toast viewport.
     * @defaultValue ['F8']
     */
    hotkey?: string[];
    /**
     * An author-localized label for the toast viewport to provide context for screen reader users
     * when navigating page landmarks. The available `{hotkey}` placeholder will be replaced for you.
     * Alternatively, you can pass in a custom function to generate the label.
     * @defaultValue 'Notifications ({hotkey})'
     */
    label?: string | ((hotkey: string) => string);
}

export declare const Toggle: __VLS_WithTemplateSlots_273<DefineComponent<ToggleProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {
"update:modelValue": (value: boolean) => any;
}, string, PublicProps, Readonly<ToggleProps> & Readonly<{
"onUpdate:modelValue"?: ((value: boolean) => any) | undefined;
}>, {
disabled: boolean;
as: AsTag | Component;
modelValue: boolean | null;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, Readonly<{
    default?: (props: {
        /** Current value */
        modelValue: boolean;
        /** Current state */
        state: DataState;
        /** Current pressed state */
        pressed: boolean;
        /** Current disabled state */
        disabled: boolean;
    }) => any;
}> & {
    default?: (props: {
        /** Current value */
        modelValue: boolean;
        /** Current state */
        state: DataState;
        /** Current pressed state */
        pressed: boolean;
        /** Current disabled state */
        disabled: boolean;
    }) => any;
}>;

export declare type ToggleEmits = {
    /** Event handler called when the value of the toggle changes. */
    'update:modelValue': [value: boolean];
};

export declare const ToggleGroupItem: __VLS_WithTemplateSlots_274<DefineComponent<ToggleGroupItemProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<ToggleGroupItemProps> & Readonly<{}>, {
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {
        modelValue: boolean;
        state: DataState;
        pressed: boolean;
        disabled: boolean;
    }): any;
}>;

export declare interface ToggleGroupItemProps extends Omit<ToggleProps, 'name' | 'required' | 'modelValue' | 'defaultValue'> {
    /**
     * A string value for the toggle group item. All items within a toggle group should use a unique value.
     */
    value: AcceptableValue;
}

export declare const ToggleGroupRoot: __VLS_WithTemplateSlots_275<DefineComponent<ToggleGroupRootProps<AcceptableValue | AcceptableValue[]>, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {
"update:modelValue": (payload: AcceptableValue | AcceptableValue[]) => any;
}, string, PublicProps, Readonly<ToggleGroupRootProps<AcceptableValue | AcceptableValue[]>> & Readonly<{
"onUpdate:modelValue"?: ((payload: AcceptableValue | AcceptableValue[]) => any) | undefined;
}>, {
disabled: boolean;
loop: boolean;
rovingFocus: boolean;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, Readonly<{
    default?: (props: {
        /** Current toggle values */
        modelValue: AcceptableValue | AcceptableValue[] | undefined;
    }) => any;
}> & {
    default?: (props: {
        /** Current toggle values */
        modelValue: AcceptableValue | AcceptableValue[] | undefined;
    }) => any;
}>;

declare interface ToggleGroupRootContext {
    isSingle: ComputedRef<boolean>;
    modelValue: Ref<AcceptableValue | AcceptableValue[] | undefined>;
    changeModelValue: (value: AcceptableValue) => void;
    dir?: Ref<Direction>;
    orientation?: DataOrientation;
    loop: Ref<boolean>;
    rovingFocus: Ref<boolean>;
    disabled?: Ref<boolean>;
}

export declare type ToggleGroupRootEmits = {
    /** Event handler called when the value changes. */
    'update:modelValue': [payload: AcceptableValue | AcceptableValue[]];
};

export declare interface ToggleGroupRootProps<T = AcceptableValue | AcceptableValue[]> extends PrimitiveProps, FormFieldProps, SingleOrMultipleProps<T> {
    /** When `false`, navigating through the items using arrow keys will be disabled. */
    rovingFocus?: boolean;
    /** When `true`, prevents the user from interacting with the toggle group and all its items. */
    disabled?: boolean;
    /** The orientation of the component, which determines how focus moves: `horizontal` for left/right arrows and `vertical` for up/down arrows. */
    orientation?: DataOrientation;
    /** The reading direction of the combobox when applicable. <br> If omitted, inherits globally from `ConfigProvider` or assumes LTR (left-to-right) reading mode. */
    dir?: Direction;
    /** When `loop` and `rovingFocus` is `true`, keyboard navigation will loop from last item to first, and vice versa. */
    loop?: boolean;
}

export declare interface ToggleProps extends PrimitiveProps, FormFieldProps {
    /**
     * The pressed state of the toggle when it is initially rendered. Use when you do not need to control its open state.
     */
    defaultValue?: boolean;
    /**
     * The controlled pressed state of the toggle. Can be bind as `v-model`.
     */
    modelValue?: boolean | null;
    /**
     * When `true`, prevents the user from interacting with the toggle.
     */
    disabled?: boolean;
}

export declare const ToolbarButton: __VLS_WithTemplateSlots_276<DefineComponent<ToolbarButtonProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<ToolbarButtonProps> & Readonly<{}>, {
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface ToolbarButtonProps extends PrimitiveProps {
    disabled?: boolean;
}

export declare const ToolbarLink: __VLS_WithTemplateSlots_277<DefineComponent<ToolbarLinkProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<ToolbarLinkProps> & Readonly<{}>, {
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface ToolbarLinkProps extends PrimitiveProps {
}

export declare const ToolbarRoot: __VLS_WithTemplateSlots_278<DefineComponent<ToolbarRootProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<ToolbarRootProps> & Readonly<{}>, {
orientation: DataOrientation;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

declare interface ToolbarRootContext {
    orientation: Ref<DataOrientation>;
    dir: Ref<Direction>;
}

export declare interface ToolbarRootProps extends PrimitiveProps {
    /** The orientation of the toolbar */
    orientation?: DataOrientation;
    /** The reading direction of the combobox when applicable. <br> If omitted, inherits globally from `ConfigProvider` or assumes LTR (left-to-right) reading mode. */
    dir?: Direction;
    /** When `true`, keyboard navigation will loop from last tab to first, and vice versa. */
    loop?: boolean;
}

export declare const ToolbarSeparator: __VLS_WithTemplateSlots_279<DefineComponent<ToolbarSeparatorProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<ToolbarSeparatorProps> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface ToolbarSeparatorProps extends PrimitiveProps {
}

export declare const ToolbarToggleGroup: __VLS_WithTemplateSlots_280<DefineComponent<ToolbarToggleGroupProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {
"update:modelValue": (payload: AcceptableValue | AcceptableValue[]) => any;
}, string, PublicProps, Readonly<ToolbarToggleGroupProps> & Readonly<{
"onUpdate:modelValue"?: ((payload: AcceptableValue | AcceptableValue[]) => any) | undefined;
}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare type ToolbarToggleGroupEmits = ToggleGroupRootEmits;

export declare interface ToolbarToggleGroupProps extends ToggleGroupRootProps {
}

export declare const ToolbarToggleItem: __VLS_WithTemplateSlots_281<DefineComponent<ToolbarToggleItemProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<ToolbarToggleItemProps> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface ToolbarToggleItemProps extends ToggleGroupItemProps {
}

export declare const TooltipArrow: __VLS_WithTemplateSlots_282<DefineComponent<TooltipArrowProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<TooltipArrowProps> & Readonly<{}>, {
width: number;
height: number;
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface TooltipArrowProps extends PrimitiveProps {
    /**
     * The width of the arrow in pixels.
     *
     * @defaultValue 10
     */
    width?: number;
    /**
     * The height of the arrow in pixels.
     *
     * @defaultValue 5
     */
    height?: number;
}

export declare const TooltipContent: __VLS_WithTemplateSlots_283<DefineComponent<TooltipContentProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {
escapeKeyDown: (event: KeyboardEvent) => any;
pointerDownOutside: (event: Event) => any;
}, string, PublicProps, Readonly<TooltipContentProps> & Readonly<{
onEscapeKeyDown?: ((event: KeyboardEvent) => any) | undefined;
onPointerDownOutside?: ((event: Event) => any) | undefined;
}>, {
side: Side;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare type TooltipContentEmits = TooltipContentImplEmits;

declare type TooltipContentImplEmits = {
    /** Event handler called when focus moves to the destructive action after opening. It can be prevented by calling `event.preventDefault` */
    escapeKeyDown: [event: KeyboardEvent];
    /** Event handler called when a pointer event occurs outside the bounds of the component. It can be prevented by calling `event.preventDefault`. */
    pointerDownOutside: [event: Event];
};

declare interface TooltipContentImplProps extends PrimitiveProps, Pick<PopperContentProps, 'side' | 'sideOffset' | 'align' | 'alignOffset' | 'avoidCollisions' | 'collisionBoundary' | 'collisionPadding' | 'arrowPadding' | 'sticky' | 'hideWhenDetached' | 'positionStrategy' | 'updatePositionStrategy'> {
    /**
     * By default, screenreaders will announce the content inside
     * the component. If this is not descriptive enough, or you have
     * content that cannot be announced, use aria-label as a more
     * descriptive label.
     *
     * @defaultValue String
     */
    ariaLabel?: string;
}

export declare interface TooltipContentProps extends TooltipContentImplProps {
    /**
     * Used to force mounting when more control is needed. Useful when
     * controlling animation with Vue animation libraries.
     */
    forceMount?: boolean;
}

declare interface TooltipContext {
    contentId: string;
    open: Ref<boolean>;
    stateAttribute: Ref<'closed' | 'delayed-open' | 'instant-open'>;
    trigger: Ref<HTMLElement | undefined>;
    onTriggerChange: (trigger: HTMLElement | undefined) => void;
    onTriggerEnter: () => void;
    onTriggerLeave: () => void;
    onOpen: () => void;
    onClose: () => void;
    disableHoverableContent: Ref<boolean>;
    disableClosingTrigger: Ref<boolean>;
    disabled: Ref<boolean>;
    ignoreNonKeyboardFocus: Ref<boolean>;
}

export declare const TooltipPortal: __VLS_WithTemplateSlots_284<DefineComponent<TooltipPortalProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<TooltipPortalProps> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface TooltipPortalProps extends TeleportProps {
}

export declare const TooltipProvider: __VLS_WithTemplateSlots_285<DefineComponent<TooltipProviderProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<TooltipProviderProps> & Readonly<{}>, {
delayDuration: number;
skipDelayDuration: number;
disableHoverableContent: boolean;
ignoreNonKeyboardFocus: boolean;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

declare interface TooltipProviderContext {
    isOpenDelayed: Ref<boolean>;
    delayDuration: Ref<number>;
    onOpen: () => void;
    onClose: () => void;
    isPointerInTransitRef: Ref<boolean>;
    disableHoverableContent: Ref<boolean>;
    disableClosingTrigger: Ref<boolean>;
    disabled: Ref<boolean>;
    ignoreNonKeyboardFocus: Ref<boolean>;
}

export declare interface TooltipProviderProps {
    /**
     * The duration from when the pointer enters the trigger until the tooltip gets opened.
     * @defaultValue 700
     */
    delayDuration?: number;
    /**
     * How much time a user has to enter another trigger without incurring a delay again.
     * @defaultValue 300
     */
    skipDelayDuration?: number;
    /**
     * When `true`, trying to hover the content will result in the tooltip closing as the pointer leaves the trigger.
     * @defaultValue false
     */
    disableHoverableContent?: boolean;
    /**
     * When `true`, clicking on trigger will not close the content.
     * @defaultValue false
     */
    disableClosingTrigger?: boolean;
    /**
     * When `true`, disable tooltip
     * @defaultValue false
     */
    disabled?: boolean;
    /**
     * Prevent the tooltip from opening if the focus did not come from
     * the keyboard by matching against the `:focus-visible` selector.
     * This is useful if you want to avoid opening it when switching
     * browser tabs or closing a dialog.
     * @defaultValue false
     */
    ignoreNonKeyboardFocus?: boolean;
}

export declare const TooltipRoot: __VLS_WithTemplateSlots_286<DefineComponent<TooltipRootProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {
"update:open": (value: boolean) => any;
}, string, PublicProps, Readonly<TooltipRootProps> & Readonly<{
"onUpdate:open"?: ((value: boolean) => any) | undefined;
}>, {
disabled: boolean;
defaultOpen: boolean;
open: boolean;
delayDuration: number;
disableHoverableContent: boolean;
disableClosingTrigger: boolean;
ignoreNonKeyboardFocus: boolean;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, Readonly<{
    default?: (props: {
        /** Current open state */
        open: boolean;
    }) => any;
}> & {
    default?: (props: {
        /** Current open state */
        open: boolean;
    }) => any;
}>;

export declare type TooltipRootEmits = {
    /** Event handler called when the open state of the tooltip changes. */
    'update:open': [value: boolean];
};

export declare interface TooltipRootProps {
    /**
     * The open state of the tooltip when it is initially rendered.
     * Use when you do not need to control its open state.
     */
    defaultOpen?: boolean;
    /**
     * The controlled open state of the tooltip.
     */
    open?: boolean;
    /**
     * Override the duration given to the `Provider` to customise
     * the open delay for a specific tooltip.
     *
     * @defaultValue 700
     */
    delayDuration?: number;
    /**
     * Prevents Tooltip.Content from remaining open when hovering.
     * Disabling this has accessibility consequences. Inherits
     * from Tooltip.Provider.
     */
    disableHoverableContent?: boolean;
    /**
     * When `true`, clicking on trigger will not close the content.
     * @defaultValue false
     */
    disableClosingTrigger?: boolean;
    /**
     * When `true`, disable tooltip
     * @defaultValue false
     */
    disabled?: boolean;
    /**
     * Prevent the tooltip from opening if the focus did not come from
     * the keyboard by matching against the `:focus-visible` selector.
     * This is useful if you want to avoid opening it when switching
     * browser tabs or closing a dialog.
     * @defaultValue false
     */
    ignoreNonKeyboardFocus?: boolean;
}

export declare const TooltipTrigger: __VLS_WithTemplateSlots_287<DefineComponent<TooltipTriggerProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<TooltipTriggerProps> & Readonly<{}>, {
as: AsTag | Component;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface TooltipTriggerProps extends PopperAnchorProps {
}

export declare const TreeItem: <T extends Record<string, any>>(__VLS_props: NonNullable<Awaited<typeof __VLS_setup>>["props"], __VLS_ctx?: __VLS_PrettifyLocal_14<Pick<NonNullable<Awaited<typeof __VLS_setup>>, "attrs" | "emit" | "slots">>, __VLS_expose?: NonNullable<Awaited<typeof __VLS_setup>>["expose"], __VLS_setup?: Promise<{
    props: __VLS_PrettifyLocal_14<Pick<Partial<{}> & Omit<{
        readonly onSelect?: ((event: TreeItemSelectEvent<T>) => any) | undefined;
        readonly onToggle?: ((event: TreeItemToggleEvent<T>) => any) | undefined;
    } & VNodeProps & AllowedComponentProps & ComponentCustomProps, never>, "onSelect" | "onToggle"> & TreeItemProps<T> & Partial<{}>> & PublicProps;
    expose(exposed: ShallowUnwrapRef<    {
    isExpanded: ComputedRef<boolean>;
    isSelected: ComputedRef<boolean>;
    isIndeterminate: ComputedRef<boolean | undefined>;
    handleToggle: () => void;
    handleSelect: () => void;
    }>): void;
    attrs: any;
    slots: Readonly<{
        default?: (props: {
            isExpanded: boolean;
            isSelected: boolean;
            isIndeterminate: boolean | undefined;
            handleToggle: () => void;
            handleSelect: () => void;
        }) => any;
    }> & {
        default?: (props: {
            isExpanded: boolean;
            isSelected: boolean;
            isIndeterminate: boolean | undefined;
            handleToggle: () => void;
            handleSelect: () => void;
        }) => any;
    };
    emit: ((evt: "select", event: TreeItemSelectEvent<T>) => void) & ((evt: "toggle", event: TreeItemToggleEvent<T>) => void);
}>) => VNode & {
    __ctx?: Awaited<typeof __VLS_setup>;
};

export declare type TreeItemEmits<T> = {
    /** Event handler called when the selecting item. <br> It can be prevented by calling `event.preventDefault`. */
    select: [event: TreeItemSelectEvent<T>];
    /** Event handler called when the selecting item. <br> It can be prevented by calling `event.preventDefault`. */
    toggle: [event: TreeItemToggleEvent<T>];
};

export declare interface TreeItemProps<T> extends PrimitiveProps {
    /** Value given to this item */
    value: T;
    /** Level of depth */
    level: number;
}

export declare type TreeItemSelectEvent<T> = CustomEvent<{
    originalEvent: PointerEvent | KeyboardEvent;
    value?: T;
    isExpanded: boolean;
    isSelected: boolean;
}>;

export declare type TreeItemToggleEvent<T> = CustomEvent<{
    originalEvent: PointerEvent | KeyboardEvent;
    value?: T;
    isExpanded: boolean;
    isSelected: boolean;
}>;

export declare const TreeRoot: <T extends Record<string, any>, U extends Record<string, any>, M extends boolean = false>(__VLS_props: NonNullable<Awaited<typeof __VLS_setup>>["props"], __VLS_ctx?: __VLS_PrettifyLocal_15<Pick<NonNullable<Awaited<typeof __VLS_setup>>, "attrs" | "emit" | "slots">>, __VLS_expose?: NonNullable<Awaited<typeof __VLS_setup>>["expose"], __VLS_setup?: Promise<{
    props: __VLS_PrettifyLocal_15<Pick<Partial<{}> & Omit<{
        readonly "onUpdate:modelValue"?: ((val: M extends true ? U[] : U) => any) | undefined;
        readonly "onUpdate:expanded"?: ((val: string[]) => any) | undefined;
    } & VNodeProps & AllowedComponentProps & ComponentCustomProps, never>, "onUpdate:modelValue" | "onUpdate:expanded"> & TreeRootProps<T, U, M> & Partial<{}>> & PublicProps;
    expose(exposed: ShallowUnwrapRef<    {}>): void;
    attrs: any;
    slots: Readonly<{
        default?: (props: {
            flattenItems: FlattenedItem<T>[];
            modelValue: M extends true ? U[] : U;
            expanded: string[];
        }) => any;
    }> & {
        default?: (props: {
            flattenItems: FlattenedItem<T>[];
            modelValue: M extends true ? U[] : U;
            expanded: string[];
        }) => any;
    };
    emit: ((evt: "update:modelValue", val: M extends true ? U[] : U) => void) & ((evt: "update:expanded", val: string[]) => void);
}>) => VNode & {
    __ctx?: Awaited<typeof __VLS_setup>;
};

declare interface TreeRootContext<T = Record<string, any>> {
    modelValue: Ref<T | T[]>;
    selectedKeys: Ref<string[]>;
    onSelect: (val: T) => void;
    expanded: Ref<string[]>;
    onToggle: (val: T) => void;
    items: Ref<T[]>;
    expandedItems: Ref<T[]>;
    getKey: (val: T) => string;
    getChildren: (val: T) => T[] | undefined;
    multiple: Ref<boolean>;
    disabled: Ref<boolean>;
    dir: Ref<Direction>;
    propagateSelect: Ref<boolean>;
    bubbleSelect: Ref<boolean>;
    isVirtual: Ref<boolean>;
    virtualKeydownHook: EventHook<KeyboardEvent>;
    handleMultipleReplace: ReturnType<typeof useSelectionBehavior>['handleMultipleReplace'];
}

export declare type TreeRootEmits<T = Record<string, any>, M extends boolean = false> = {
    'update:modelValue': [val: M extends true ? T[] : T];
    'update:expanded': [val: string[]];
};

export declare interface TreeRootProps<T = Record<string, any>, U extends Record<string, any> = Record<string, any>, M extends boolean = false> extends PrimitiveProps {
    /** The controlled value of the tree. Can be binded with with `v-model`. */
    modelValue?: M extends true ? U[] : U;
    /** The value of the tree when initially rendered. Use when you do not need to control the state of the tree */
    defaultValue?: M extends true ? U[] : U;
    /** List of items */
    items?: T[];
    /** The controlled value of the expanded item. Can be binded with with `v-model`. */
    expanded?: string[];
    /** The value of the expanded tree when initially rendered. Use when you do not need to control the state of the expanded tree */
    defaultExpanded?: string[];
    /** This function is passed the index of each item and should return a unique key for that item */
    getKey: (val: T) => string;
    /** This function is passed the index of each item and should return a list of children for that item */
    getChildren?: (val: T) => T[] | undefined;
    /** How multiple selection should behave in the collection. */
    selectionBehavior?: 'toggle' | 'replace';
    /** Whether multiple options can be selected or not.  */
    multiple?: M;
    /** The reading direction of the listbox when applicable. <br> If omitted, inherits globally from `ConfigProvider` or assumes LTR (left-to-right) reading mode. */
    dir?: Direction;
    /** When `true`, prevents the user from interacting with tree  */
    disabled?: boolean;
    /** When `true`, selecting parent will select the descendants. */
    propagateSelect?: boolean;
    /** When `true`, selecting children will update the parent state. */
    bubbleSelect?: boolean;
}

export declare const TreeVirtualizer: __VLS_WithTemplateSlots_288<DefineComponent<TreeVirtualizerProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<TreeVirtualizerProps> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, HTMLDivElement>, Readonly<{
    default?: (props: {
        item: FlattenedItem<Record<string, any>>;
        virtualizer: Virtualizer<Element | Window, Element>;
        virtualItem: VirtualItem;
    }) => any;
}> & {
    default?: (props: {
        item: FlattenedItem<Record<string, any>>;
        virtualizer: Virtualizer<Element | Window, Element>;
        virtualItem: VirtualItem;
    }) => any;
}>;

export declare interface TreeVirtualizerProps {
    /** Number of items rendered outside the visible area */
    overscan?: number;
    /** Estimated size (in px) of each item */
    estimateSize?: number;
    /** Text content for each item to achieve type-ahead feature */
    textContent?: (item: Record<string, any>) => string;
}

declare type UnionToIntersection<T> = (T extends any ? (x: T) => any : never) extends (x: infer R) => any ? R : never;

export declare function useBodyScrollLock(initialState?: boolean | undefined): WritableComputedRef<boolean, boolean>;

/**
 * Creates a wrapper around the `DateFormatter`, which is
 * an improved version of the {@link Intl.DateTimeFormat} API,
 * that is used internally by the various date builders to
 * easily format dates in a consistent way.
 *
 * @see [DateFormatter](https://react-spectrum.adobe.com/internationalized/date/DateFormatter.html)
 */
export declare function useDateFormatter(initialLocale: string, opts?: DateFormatterOptions): Formatter;

/**
 * The `useEmitAsProps` function is a TypeScript utility that converts emitted events into props for a
 * Vue component.
 * @param emit - The `emit` parameter is a function that is used to emit events from a component. It
 * takes two parameters: `name` which is the name of the event to be emitted, and `...args` which are
 * the arguments to be passed along with the event.
 * @returns The function `useEmitAsProps` returns an object that maps event names to functions that
 * call the `emit` function with the corresponding event name and arguments.
 */
export declare function useEmitAsProps<Name extends string>(emit: (name: Name, ...args: any[]) => void): Record<string, any>;

/**
 * Provides locale-aware string filtering functions.
 * Uses `Intl.Collator` for comparison to ensure proper Unicode handling.
 *
 * @param options - Optional collator options to customize comparison behavior.
 *   See [Intl.CollatorOptions](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/Collator/Collator#options) for details.
 * @returns An object with methods to check if a string starts with, ends with, or contains a substring.
 *
 * @example
 * const { startsWith, endsWith, contains } = useFilter();
 *
 * startsWith('hello', 'he'); // true
 * endsWith('hello', 'lo'); // true
 * contains('hello', 'ell'); // true
 */
export declare function useFilter(options?: MaybeRef<Intl.CollatorOptions>): {
    startsWith: (string: string, substring: string) => boolean;
    endsWith: (string: string, substring: string) => boolean;
    contains: (string: string, substring: string) => boolean;
};

export declare function useForwardExpose<T extends ComponentPublicInstance>(): {
    forwardRef: (ref: Element | T | null) => void;
    currentRef: Ref<Element | T | null | undefined, Element | T | null | undefined>;
    currentElement: ComputedRef<HTMLElement>;
};

/**
 * The `useForwardProps` function in TypeScript takes in a set of props and returns a computed value
 * that combines default props with assigned props from the current instance.
 * @param {T} props - The `props` parameter is an object that represents the props passed to a
 * component.
 * @returns computed value that combines the default props, preserved props, and assigned props.
 */
export declare function useForwardProps<T extends Record<string, any>>(props: MaybeRefOrGetter<T>): ComputedRef<T>;

/**
 * The function `useForwardPropsEmits` takes in props and an optional emit function, and returns a
 * computed object that combines the parsed props and emits as props.
 * @param {T} props - The `props` parameter is of type `T`, which is a generic type that extends the
 * parameters of the `useForwardProps` function. It represents the props object that is passed to the
 * `useForwardProps` function.
 * @param [emit] - The `emit` parameter is a function that can be used to emit events. It takes two
 * arguments: `name`, which is the name of the event to be emitted, and `args`, which are the arguments
 * to be passed along with the event.
 * @returns a computed property that combines the parsed
 * props and emits as props.
 */
export declare function useForwardPropsEmits<T extends Record<string, any>, Name extends string>(props: MaybeRefOrGetter<T>, emit?: (name: Name, ...args: any[]) => void): ComputedRef<T & Record<string, any>>;

/**
 * The `useId` function generates a unique identifier using a provided deterministic ID or a default
 * one prefixed with "reka-", or the provided one via `useId` props from `<ConfigProvider>`.
 * @param {string | null | undefined} [deterministicId] - The `useId` function you provided takes an
 * optional parameter `deterministicId`, which can be a string, null, or undefined. If
 * `deterministicId` is provided, the function will return it. Otherwise, it will generate an id using
 * the `useId` function obtained
 */
export declare function useId(deterministicId?: string | null | undefined, prefix?: string): string;

declare function useSelectionBehavior<T>(modelValue: Ref<T | T[]>, props: UnwrapNestedRefs<{
    multiple?: boolean;
    selectionBehavior?: 'toggle' | 'replace';
}>): {
    firstValue: Ref<any, any>;
    onSelectItem: (val: T, condition: (existingValue: T) => boolean) => T | T[];
    handleMultipleReplace: (intent: "first" | "last" | "prev" | "next", currentElement: HTMLElement | Element | null, getItems: () => {
        ref: HTMLElement;
        value?: any;
    }[], options: any[]) => void;
};

/**
 * The `useStateMachine` function is a TypeScript function that creates a state machine and returns the
 * current state and a dispatch function to update the state based on events.
 * @param initialState - The `initialState` parameter is the initial state of the state machine. It
 * represents the starting point of the state machine's state.
 * @param machine - The `machine` parameter is an object that represents a state machine. It should
 * have keys that correspond to the possible states of the machine, and the values should be objects
 * that represent the possible events and their corresponding next states.
 * @returns The `useStateMachine` function returns an object with two properties: `state` and
 * `dispatch`.
 */
export declare function useStateMachine<M>(initialState: MachineState<M>, machine: M & Machine<MachineState<M>>): {
    state: Ref<keyof M, keyof M>;
    dispatch: (event: MachineEvent<M>) => void;
};

export declare const Viewport: __VLS_WithTemplateSlots_289<DefineComponent<ViewportProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<ViewportProps> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface ViewportProps extends PrimitiveProps {
    /**
     * Will add `nonce` attribute to the style tag which can be used by Content Security Policy. <br> If omitted, inherits globally from `ConfigProvider`.
     */
    nonce?: string;
}

export declare const VisuallyHidden: __VLS_WithTemplateSlots<DefineComponent<VisuallyHiddenProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<VisuallyHiddenProps> & Readonly<{}>, {
as: AsTag | Component;
feature: "focusable" | "fully-hidden";
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>, {
    default?(_: {}): any;
}>;

export declare interface VisuallyHiddenProps extends PrimitiveProps {
    feature?: 'focusable' | 'fully-hidden';
}

declare type WeekDayFormat = 'narrow' | 'short' | 'long';

export declare function withDefault<T, C = T extends ((...args: any) => any) | (new (...args: any) => any) ? T : T extends {
    props?: infer Props;
} ? DefineComponent<Props extends Readonly<(infer PropNames)[]> | (infer PropNames)[] ? {
    [key in PropNames extends string ? PropNames : string]?: any;
} : Props> : DefineComponent, P extends ComponentProps<C> = ComponentProps<C>>(originalComponent: T, options?: MountingOptions<P>): T;

export { }
