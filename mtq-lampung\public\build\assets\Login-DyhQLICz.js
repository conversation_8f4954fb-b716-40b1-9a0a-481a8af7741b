var O=s=>{throw TypeError(s)};var Q=(s,a,r)=>a.has(s)||O("Cannot "+r);var V=(s,a,r)=>(Q(s,a,"read from private field"),r?r.call(s):a.get(s)),q=(s,a,r)=>a.has(s)?O("Cannot add the same private member more than once"):a instanceof WeakSet?a.add(s):a.set(s,r);import{c as g,d as w,l as W,h as b,o as y,m as k,a as U,r as X,F as Y,n as Z,p as G,w as m,e as p,u as n,q as I,s as ee,k as C,v as te,j as D,y as ae,x as re,b as $,g as se,t as ne,i as _}from"./app-BxByyVXe.js";import{_ as j,a as R,b as P}from"./Label.vue_vue_type_script_setup_true_lang-Dc3XvMbX.js";import{_ as T}from"./TextLink.vue_vue_type_script_setup_true_lang-1wzIiHGk.js";import{P as x,c as oe,a as ie,_ as le}from"./AppLogoIcon.vue_vue_type_script_setup_true_lang-43VByX6w.js";import{u as ue,_ as de,c as N,i as ce,a as pe,b as fe,g as me,w as ve,f as ye,P as be,d as he}from"./RovingFocusGroup-hazQWYmS.js";import{t as ge,u as $e,a as H,b as we}from"./useForwardExpose-67BWFZEI.js";import{L as _e,_ as ke}from"./AuthLayout.vue_vue_type_script_setup_true_lang-DidZ5Moo.js";function K(s){return typeof s=="string"?`'${s}'`:new Ve().serialize(s)}const Ve=function(){var a;class s{constructor(){q(this,a,new Map)}compare(e,t){const o=typeof e,i=typeof t;return o==="string"&&i==="string"?e.localeCompare(t):o==="number"&&i==="number"?e-t:String.prototype.localeCompare.call(this.serialize(e,!0),this.serialize(t,!0))}serialize(e,t){if(e===null)return"null";switch(typeof e){case"string":return t?e:`'${e}'`;case"bigint":return`${e}n`;case"object":return this.$object(e);case"function":return this.$function(e)}return String(e)}serializeObject(e){const t=Object.prototype.toString.call(e);if(t!=="[object Object]")return this.serializeBuiltInType(t.length<10?`unknown:${t}`:t.slice(8,-1),e);const o=e.constructor,i=o===Object||o===void 0?"":o.name;if(i!==""&&globalThis[i]===o)return this.serializeBuiltInType(i,e);if(typeof e.toJSON=="function"){const l=e.toJSON();return i+(l!==null&&typeof l=="object"?this.$object(l):`(${this.serialize(l)})`)}return this.serializeObjectEntries(i,Object.entries(e))}serializeBuiltInType(e,t){const o=this["$"+e];if(o)return o.call(this,t);if(typeof(t==null?void 0:t.entries)=="function")return this.serializeObjectEntries(e,t.entries());throw new Error(`Cannot serialize ${e}`)}serializeObjectEntries(e,t){const o=Array.from(t).sort((l,v)=>this.compare(l[0],v[0]));let i=`${e}{`;for(let l=0;l<o.length;l++){const[v,u]=o[l];i+=`${this.serialize(v,!0)}:${this.serialize(u)}`,l<o.length-1&&(i+=",")}return i+"}"}$object(e){let t=V(this,a).get(e);return t===void 0&&(V(this,a).set(e,`#${V(this,a).size}`),t=this.serializeObject(e),V(this,a).set(e,t)),t}$function(e){const t=Function.prototype.toString.call(e);return t.slice(-15)==="[native code] }"?`${e.name||""}()[native]`:`${e.name}(${e.length})${t.replace(/\s*\n\s*/g,"")}`}$Array(e){let t="[";for(let o=0;o<e.length;o++)t+=this.serialize(e[o]),o<e.length-1&&(t+=",");return t+"]"}$Date(e){try{return`Date(${e.toISOString()})`}catch{return"Date(null)"}}$ArrayBuffer(e){return`ArrayBuffer[${new Uint8Array(e).join(",")}]`}$Set(e){return`Set${this.$Array(Array.from(e).sort((t,o)=>this.compare(t,o)))}`}$Map(e){return this.serializeObjectEntries("Map",e.entries())}}a=new WeakMap;for(const r of["Error","RegExp","URL"])s.prototype["$"+r]=function(e){return`${r}(${e})`};for(const r of["Int8Array","Uint8Array","Uint8ClampedArray","Int16Array","Uint16Array","Int32Array","Uint32Array","Float32Array","Float64Array"])s.prototype["$"+r]=function(e){return`${r}[${e.join(",")}]`};for(const r of["BigInt64Array","BigUint64Array"])s.prototype["$"+r]=function(e){return`${r}[${e.join("n,")}${e.length>0?"n":""}]`};return s}();function E(s,a){return s===a||K(s)===K(a)}function S(s){return s==null}function M(s,a){return S(s)?!1:Array.isArray(s)?s.some(r=>E(r,a)):E(s,a)}function Be(s){return g(()=>{var a;return ge(s)?!!((a=$e(s))!=null&&a.closest("form")):!0})}const L=w({inheritAttrs:!1,__name:"VisuallyHiddenInputBubble",props:{name:{},value:{},checked:{type:Boolean,default:void 0},required:{type:Boolean},disabled:{type:Boolean},feature:{default:"fully-hidden"}},setup(s){const a=s,{primitiveElement:r,currentElement:e}=ue(),t=g(()=>a.checked??a.value);return W(t,(o,i)=>{if(!e.value)return;const l=e.value,v=window.HTMLInputElement.prototype,c=Object.getOwnPropertyDescriptor(v,"value").set;if(c&&o!==i){const f=new Event("input",{bubbles:!0}),h=new Event("change",{bubbles:!0});c.call(l,o),l.dispatchEvent(f),l.dispatchEvent(h)}}),(o,i)=>(y(),b(de,k({ref_key:"primitiveElement",ref:r},{...a,...o.$attrs},{as:"input"}),null,16))}}),Ce=w({inheritAttrs:!1,__name:"VisuallyHiddenInput",props:{name:{},value:{},checked:{type:Boolean,default:void 0},required:{type:Boolean},disabled:{type:Boolean},feature:{default:"fully-hidden"}},setup(s){const a=s,r=g(()=>typeof a.value=="object"&&Array.isArray(a.value)&&a.value.length===0&&a.required),e=g(()=>typeof a.value=="string"||typeof a.value=="number"||typeof a.value=="boolean"?[{name:a.name,value:a.value}]:typeof a.value=="object"&&Array.isArray(a.value)?a.value.flatMap((t,o)=>typeof t=="object"?Object.entries(t).map(([i,l])=>({name:`[${a.name}][${o}][${i}]`,value:l})):{name:`[${a.name}][${o}]`,value:t}):a.value!==null&&typeof a.value=="object"&&!Array.isArray(a.value)?Object.entries(a.value).map(([t,o])=>({name:`[${a.name}][${t}]`,value:o})):[]);return(t,o)=>r.value?(y(),b(L,k({key:t.name},{...a,...t.$attrs},{name:t.name,value:t.value}),null,16,["name","value"])):(y(!0),U(Y,{key:1},X(e.value,i=>(y(),b(L,k({key:i.name,ref_for:!0},{...a,...t.$attrs},{name:i.name,value:i.value}),null,16,["name","value"]))),128))}}),[Ae,Xe]=N("CheckboxGroupRoot");function A(s){return s==="indeterminate"}function J(s){return A(s)?"indeterminate":s?"checked":"unchecked"}const Ie=w({__name:"RovingFocusItem",props:{tabStopId:{},focusable:{type:Boolean,default:!0},active:{type:Boolean},allowShiftKey:{type:Boolean},asChild:{type:Boolean},as:{default:"span"}},setup(s){const a=s,r=ce(),e=pe(),t=g(()=>a.tabStopId||e),o=g(()=>r.currentTabStopId.value===t.value),{getItems:i,CollectionItem:l}=fe();Z(()=>{a.focusable&&r.onFocusableItemAdd()}),G(()=>{a.focusable&&r.onFocusableItemRemove()});function v(u){if(u.key==="Tab"&&u.shiftKey){r.onItemShiftTab();return}if(u.target!==u.currentTarget)return;const c=me(u,r.orientation.value,r.dir.value);if(c!==void 0){if(u.metaKey||u.ctrlKey||u.altKey||!a.allowShiftKey&&u.shiftKey)return;u.preventDefault();let f=[...i().map(h=>h.ref).filter(h=>h.dataset.disabled!=="")];if(c==="last")f.reverse();else if(c==="prev"||c==="next"){c==="prev"&&f.reverse();const h=f.indexOf(u.currentTarget);f=r.loop.value?ve(f,h+1):f.slice(h+1)}ee(()=>ye(f))}}return(u,c)=>(y(),b(n(l),null,{default:m(()=>[p(n(x),{tabindex:o.value?0:-1,"data-orientation":n(r).orientation.value,"data-active":u.active?"":void 0,"data-disabled":u.focusable?void 0:"",as:u.as,"as-child":u.asChild,onMousedown:c[0]||(c[0]=f=>{u.focusable?n(r).onItemFocus(t.value):f.preventDefault()}),onFocus:c[1]||(c[1]=f=>n(r).onItemFocus(t.value)),onKeydown:v},{default:m(()=>[I(u.$slots,"default")]),_:3},8,["tabindex","data-orientation","data-active","data-disabled","as","as-child"])]),_:3}))}}),[je,Ee]=N("CheckboxRoot"),Se=w({inheritAttrs:!1,__name:"CheckboxRoot",props:{defaultValue:{type:[Boolean,String]},modelValue:{type:[Boolean,String,null],default:void 0},disabled:{type:Boolean},value:{default:"on"},id:{},asChild:{type:Boolean},as:{default:"button"},name:{},required:{type:Boolean}},emits:["update:modelValue"],setup(s,{emit:a}){const r=s,e=a,{forwardRef:t,currentElement:o}=H(),i=Ae(null),l=we(r,"modelValue",e,{defaultValue:r.defaultValue,passive:r.modelValue===void 0}),v=g(()=>(i==null?void 0:i.disabled.value)||r.disabled),u=g(()=>S(i==null?void 0:i.modelValue.value)?l.value==="indeterminate"?"indeterminate":l.value:M(i.modelValue.value,r.value));function c(){if(S(i==null?void 0:i.modelValue.value))l.value=A(l.value)?!0:!l.value;else{const d=[...i.modelValue.value||[]];if(M(d,r.value)){const F=d.findIndex(B=>E(B,r.value));d.splice(F,1)}else d.push(r.value);i.modelValue.value=d}}const f=Be(o),h=g(()=>{var d;return r.id&&o.value?(d=document.querySelector(`[for="${r.id}"]`))==null?void 0:d.innerText:void 0});return Ee({disabled:v,state:u}),(d,F)=>{var B,z;return y(),b(ae((B=n(i))!=null&&B.rovingFocus.value?n(Ie):n(x)),k(d.$attrs,{id:d.id,ref:n(t),role:"checkbox","as-child":d.asChild,as:d.as,type:d.as==="button"?"button":void 0,"aria-checked":n(A)(u.value)?"mixed":u.value,"aria-required":d.required,"aria-label":d.$attrs["aria-label"]||h.value,"data-state":n(J)(u.value),"data-disabled":v.value?"":void 0,disabled:v.value,focusable:(z=n(i))!=null&&z.rovingFocus.value?!v.value:void 0,onKeydown:te(D(()=>{},["prevent"]),["enter"]),onClick:c}),{default:m(()=>[I(d.$slots,"default",{modelValue:n(l),state:u.value}),n(f)&&d.name&&!n(i)?(y(),b(n(Ce),{key:0,type:"checkbox",checked:!!u.value,name:d.name,value:d.value,disabled:v.value,required:d.required},null,8,["checked","name","value","disabled","required"])):C("",!0)]),_:3},16,["id","as-child","as","type","aria-checked","aria-required","aria-label","data-state","data-disabled","disabled","focusable","onKeydown"])}}}),xe=w({__name:"CheckboxIndicator",props:{forceMount:{type:Boolean},asChild:{type:Boolean},as:{default:"span"}},setup(s){const{forwardRef:a}=H(),r=je();return(e,t)=>(y(),b(n(be),{present:e.forceMount||n(A)(n(r).state.value)||n(r).state.value===!0},{default:m(()=>[p(n(x),k({ref:n(a),"data-state":n(J)(n(r).state.value),"data-disabled":n(r).disabled.value?"":void 0,style:{pointerEvents:"none"},"as-child":e.asChild,as:e.as},e.$attrs),{default:m(()=>[I(e.$slots,"default")]),_:3},16,["data-state","data-disabled","as-child","as"])]),_:3},8,["present"]))}});/**
 * @license lucide-vue-next v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Fe=oe("CheckIcon",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]),ze=w({__name:"Checkbox",props:{defaultValue:{type:[Boolean,String]},modelValue:{type:[Boolean,String,null]},disabled:{type:Boolean},value:{},id:{},asChild:{type:Boolean},as:{type:[String,Object,Function]},name:{},required:{type:Boolean},class:{}},emits:["update:modelValue"],setup(s,{emit:a}){const r=s,e=a,t=g(()=>{const{class:i,...l}=r;return l}),o=he(t,e);return(i,l)=>(y(),b(n(Se),k({"data-slot":"checkbox"},n(o),{class:n(ie)("peer border-input data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",r.class)}),{default:m(()=>[p(n(xe),{"data-slot":"checkbox-indicator",class:"flex items-center justify-center text-current transition-none"},{default:m(()=>[I(i.$slots,"default",{},()=>[p(n(Fe),{class:"size-3.5"})])]),_:3})]),_:3},16,["class"]))}}),Oe={key:0,class:"mb-4 text-center text-sm font-medium text-green-600"},qe={class:"grid gap-6"},Re={class:"grid gap-2"},Pe={class:"grid gap-2"},Te={class:"flex items-center justify-between"},Ke={class:"flex items-center justify-between"},Me={class:"text-center text-sm text-muted-foreground"},Ye=w({__name:"Login",props:{status:{},canResetPassword:{type:Boolean}},setup(s){const a=re({email:"",password:"",remember:!1}),r=()=>{a.post(route("login"),{onFinish:()=>a.reset("password")})};return(e,t)=>(y(),b(ke,{title:"Log in to your account",description:"Enter your email and password below to log in"},{default:m(()=>[p(n(se),{title:"Log in"}),e.status?(y(),U("div",Oe,ne(e.status),1)):C("",!0),$("form",{onSubmit:D(r,["prevent"]),class:"flex flex-col gap-6"},[$("div",qe,[$("div",Re,[p(n(j),{for:"email"},{default:m(()=>t[3]||(t[3]=[_("Email address")])),_:1,__:[3]}),p(n(R),{id:"email",type:"email",required:"",autofocus:"",tabindex:1,autocomplete:"email",modelValue:n(a).email,"onUpdate:modelValue":t[0]||(t[0]=o=>n(a).email=o),placeholder:"<EMAIL>"},null,8,["modelValue"]),p(P,{message:n(a).errors.email},null,8,["message"])]),$("div",Pe,[$("div",Te,[p(n(j),{for:"password"},{default:m(()=>t[4]||(t[4]=[_("Password")])),_:1,__:[4]}),e.canResetPassword?(y(),b(T,{key:0,href:e.route("password.request"),class:"text-sm",tabindex:5},{default:m(()=>t[5]||(t[5]=[_(" Forgot password? ")])),_:1,__:[5]},8,["href"])):C("",!0)]),p(n(R),{id:"password",type:"password",required:"",tabindex:2,autocomplete:"current-password",modelValue:n(a).password,"onUpdate:modelValue":t[1]||(t[1]=o=>n(a).password=o),placeholder:"Password"},null,8,["modelValue"]),p(P,{message:n(a).errors.password},null,8,["message"])]),$("div",Ke,[p(n(j),{for:"remember",class:"flex items-center space-x-3"},{default:m(()=>[p(n(ze),{id:"remember",modelValue:n(a).remember,"onUpdate:modelValue":t[2]||(t[2]=o=>n(a).remember=o),tabindex:3},null,8,["modelValue"]),t[6]||(t[6]=$("span",null,"Remember me",-1))]),_:1,__:[6]})]),p(n(le),{type:"submit",class:"mt-4 w-full",tabindex:4,disabled:n(a).processing},{default:m(()=>[n(a).processing?(y(),b(n(_e),{key:0,class:"h-4 w-4 animate-spin"})):C("",!0),t[7]||(t[7]=_(" Log in "))]),_:1,__:[7]},8,["disabled"])]),$("div",Me,[t[9]||(t[9]=_(" Don't have an account? ")),p(T,{href:e.route("register"),tabindex:5},{default:m(()=>t[8]||(t[8]=[_("Sign up")])),_:1,__:[8]},8,["href"])])],32)]),_:1}))}});export{Ye as default};
