{"version": 3, "file": "useFormControl.js", "sources": ["../../src/shared/useFormControl.ts"], "sourcesContent": ["import type { MaybeElementRef } from '@vueuse/core'\nimport { toValue, unrefElement } from '@vueuse/core'\nimport { computed } from 'vue'\n\nexport function useFormControl(el: MaybeElementRef) {\n  // We set this to true by default so that events bubble to forms without JS (SSR)\n  return computed(() => toValue(el) ? Boolean(unrefElement(el)?.closest('form')) : true)\n}\n"], "names": [], "mappings": ";;;AAIO,SAAS,eAAe,EAAqB,EAAA;AAElD,EAAA,OAAO,QAAS,CAAA,MAAM,OAAQ,CAAA,EAAE,CAAI,GAAA,OAAA,CAAQ,YAAa,CAAA,EAAE,CAAG,EAAA,OAAA,CAAQ,MAAM,CAAC,IAAI,IAAI,CAAA;AACvF;;;;"}