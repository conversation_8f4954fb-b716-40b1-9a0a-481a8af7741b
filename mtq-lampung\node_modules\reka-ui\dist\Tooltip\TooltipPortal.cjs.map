{"version": 3, "file": "TooltipPortal.cjs", "sources": ["../../src/Tooltip/TooltipPortal.vue"], "sourcesContent": ["<script lang=\"ts\">\nimport type { TeleportProps } from '@/Teleport'\n\nexport interface TooltipPortalProps extends TeleportProps {}\n</script>\n\n<script setup lang=\"ts\">\nimport { TeleportPrimitive } from '@/Teleport'\n\nconst props = defineProps<TooltipPortalProps>()\n</script>\n\n<template>\n  <TeleportPrimitive v-bind=\"props\">\n    <slot />\n  </TeleportPrimitive>\n</template>\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AASA,IAAA,MAAM,KAAQ,GAAA,OAAA;;;;;;;;;;;;;;"}