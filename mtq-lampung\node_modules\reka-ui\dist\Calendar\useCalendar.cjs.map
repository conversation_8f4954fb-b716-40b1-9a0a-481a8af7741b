{"version": 3, "file": "useCalendar.cjs", "sources": ["../../src/Calendar/useCalendar.ts"], "sourcesContent": ["/*\n  * Adapted from https://github.com/melt-ui/melt-ui/blob/develop/src/lib/builders/calendar/create.ts\n*/\n\nimport type { DateFields, DateValue } from '@internationalized/date'\nimport type { Ref } from 'vue'\nimport type { Grid, Matcher, WeekDayFormat } from '@/date'\nimport type { DateFormatterOptions } from '@/shared/useDateFormatter'\nimport { isEqualMonth, isSameDay } from '@internationalized/date'\nimport { computed, ref, watch } from 'vue'\nimport { createMonths, getDaysInMonth, isAfter, isBefore, toDate } from '@/date'\nimport { useDateFormatter } from '@/shared'\n\nexport type UseCalendarProps = {\n  locale: Ref<string>\n  placeholder: Ref<DateValue>\n  weekStartsOn: Ref<0 | 1 | 2 | 3 | 4 | 5 | 6>\n  fixedWeeks: Ref<boolean>\n  numberOfMonths: Ref<number>\n  minValue: Ref<DateValue | undefined>\n  maxValue: Ref<DateValue | undefined>\n  disabled: Ref<boolean>\n  weekdayFormat: Ref<WeekDayFormat>\n  pagedNavigation: Ref<boolean>\n  isDateDisabled?: Matcher\n  isDateUnavailable?: Matcher\n  calendarLabel: Ref<string | undefined>\n  nextPage: Ref<((placeholder: DateValue) => DateValue) | undefined>\n  prevPage: Ref<((placeholder: DateValue) => DateValue) | undefined>\n}\n\nexport type UseCalendarStateProps = {\n  isDateDisabled: Matcher\n  isDateUnavailable: Matcher\n  date: Ref<DateValue | DateValue[] | undefined>\n}\n\nexport function useCalendarState(props: UseCalendarStateProps) {\n  function isDateSelected(dateObj: DateValue) {\n    if (Array.isArray(props.date.value))\n      return props.date.value.some(d => isSameDay(d, dateObj))\n\n    else if (!props.date.value)\n      return false\n\n    else\n      return isSameDay(props.date.value, dateObj)\n  }\n\n  const isInvalid = computed(\n    () => {\n      if (Array.isArray(props.date.value)) {\n        if (!props.date.value.length)\n          return false\n        for (const dateObj of props.date.value) {\n          if (props.isDateDisabled?.(dateObj))\n            return true\n          if (props.isDateUnavailable?.(dateObj))\n            return true\n        }\n      }\n      else {\n        if (!props.date.value)\n          return false\n        if (props.isDateDisabled?.(props.date.value))\n          return true\n        if (props.isDateUnavailable?.(props.date.value))\n          return true\n      }\n      return false\n    },\n  )\n\n  return {\n    isDateSelected,\n    isInvalid,\n  }\n}\n\nfunction handleNextDisabled(lastPeriodInView: DateValue, nextPageFunc: (date: DateValue) => DateValue): DateValue {\n  const firstPeriodOfNextPage = nextPageFunc(lastPeriodInView)\n  const diff = firstPeriodOfNextPage.compare(lastPeriodInView)\n  const duration: DateFields = {}\n  if (diff >= 7)\n    duration.day = 1\n  if (diff >= getDaysInMonth(lastPeriodInView))\n    duration.month = 1\n  return firstPeriodOfNextPage.set({ ...duration })\n}\nfunction handlePrevDisabled(firstPeriodInView: DateValue, prevPageFunc: (date: DateValue) => DateValue): DateValue {\n  const lastPeriodOfPrevPage = prevPageFunc(firstPeriodInView)\n  const diff = firstPeriodInView.compare(lastPeriodOfPrevPage)\n  const duration: DateFields = {}\n  if (diff >= 7)\n    duration.day = 35\n  if (diff >= getDaysInMonth(firstPeriodInView))\n    duration.month = 13\n  return lastPeriodOfPrevPage.set({ ...duration })\n}\nfunction handleNextPage(date: DateValue, nextPageFunc: (date: DateValue) => DateValue): DateValue {\n  return nextPageFunc(date)\n}\n\nfunction handlePrevPage(date: DateValue, prevPageFunc: (date: DateValue) => DateValue): DateValue {\n  return prevPageFunc(date)\n}\n\nexport function useCalendar(props: UseCalendarProps) {\n  const formatter = useDateFormatter(props.locale.value)\n\n  const headingFormatOptions = computed(() => {\n    const options: DateFormatterOptions = {\n      calendar: props.placeholder.value.calendar.identifier,\n    }\n\n    if (props.placeholder.value.calendar.identifier === 'gregory' && props.placeholder.value.era === 'BC')\n      options.era = 'short'\n\n    return options\n  })\n\n  const grid = ref<Grid<DateValue>[]>(createMonths({\n    dateObj: props.placeholder.value,\n    weekStartsOn: props.weekStartsOn.value,\n    locale: props.locale.value,\n    fixedWeeks: props.fixedWeeks.value,\n    numberOfMonths: props.numberOfMonths.value,\n  })) as Ref<Grid<DateValue>[]>\n\n  const visibleView = computed(() => {\n    return grid.value.map(month => month.value)\n  })\n\n  function isOutsideVisibleView(date: DateValue) {\n    return !visibleView.value.some(month => isEqualMonth(date, month))\n  }\n\n  const isNextButtonDisabled = (nextPageFunc?: (date: DateValue) => DateValue) => {\n    if (!props.maxValue.value || !grid.value.length)\n      return false\n    if (props.disabled.value)\n      return true\n\n    const lastPeriodInView = grid.value[grid.value.length - 1].value\n\n    if (!nextPageFunc && !props.nextPage.value) {\n      const firstPeriodOfNextPage = lastPeriodInView.add({ months: 1 }).set({ day: 1 })\n      return isAfter(firstPeriodOfNextPage, props.maxValue.value)\n    }\n\n    const firstPeriodOfNextPage = handleNextDisabled(lastPeriodInView, nextPageFunc || props.nextPage.value!)\n    return isAfter(firstPeriodOfNextPage, props.maxValue.value)\n  }\n\n  const isPrevButtonDisabled = (prevPageFunc?: (date: DateValue) => DateValue) => {\n    if (!props.minValue.value || !grid.value.length)\n      return false\n    if (props.disabled.value)\n      return true\n    const firstPeriodInView = grid.value[0].value\n\n    if (!prevPageFunc && !props.prevPage.value) {\n      const lastPeriodOfPrevPage = firstPeriodInView.subtract({ months: 1 }).set({ day: 35 })\n      return isBefore(lastPeriodOfPrevPage, props.minValue.value)\n    }\n\n    const lastPeriodOfPrevPage = handlePrevDisabled(firstPeriodInView, prevPageFunc || props.prevPage.value!)\n    return isBefore(lastPeriodOfPrevPage, props.minValue.value)\n  }\n\n  function isDateDisabled(dateObj: DateValue) {\n    if (props.isDateDisabled?.(dateObj) || props.disabled.value)\n      return true\n    if (props.maxValue.value && isAfter(dateObj, props.maxValue.value))\n      return true\n    if (props.minValue.value && isBefore(dateObj, props.minValue.value))\n      return true\n    return false\n  }\n\n  const isDateUnavailable = (date: DateValue) => {\n    if (props.isDateUnavailable?.(date))\n      return true\n    return false\n  }\n\n  const weekdays = computed(() => {\n    if (!grid.value.length)\n      return []\n    return grid.value[0].rows[0].map((date) => {\n      return formatter.dayOfWeek(toDate(date), props.weekdayFormat.value)\n    })\n  })\n\n  const nextPage = (nextPageFunc?: (date: DateValue) => DateValue) => {\n    const firstDate = grid.value[0].value\n\n    if (!nextPageFunc && !props.nextPage.value) {\n      const newDate = firstDate.add({ months: props.pagedNavigation.value ? props.numberOfMonths.value : 1 })\n\n      const newGrid = createMonths({\n        dateObj: newDate,\n        weekStartsOn: props.weekStartsOn.value,\n        locale: props.locale.value,\n        fixedWeeks: props.fixedWeeks.value,\n        numberOfMonths: props.numberOfMonths.value,\n      })\n\n      grid.value = newGrid\n\n      props.placeholder.value = newGrid[0].value.set({ day: 1 })\n      return\n    }\n\n    const newDate = handleNextPage(firstDate, nextPageFunc || props.nextPage.value!)\n    const newGrid = createMonths({\n      dateObj: newDate,\n      weekStartsOn: props.weekStartsOn.value,\n      locale: props.locale.value,\n      fixedWeeks: props.fixedWeeks.value,\n      numberOfMonths: props.numberOfMonths.value,\n    })\n\n    grid.value = newGrid\n\n    const duration: DateFields = {}\n\n    // Do not adjust the placeholder if the nextPageFunc is defined (overwrite)\n    if (!nextPageFunc) {\n      const diff = newGrid[0].value.compare(firstDate)\n      if (diff >= getDaysInMonth(firstDate))\n        duration.day = 1\n\n      if (diff >= 365)\n        duration.month = 1\n    }\n\n    props.placeholder.value = newGrid[0].value.set({ ...duration })\n  }\n\n  const prevPage = (prevPageFunc?: (date: DateValue) => DateValue) => {\n    const firstDate = grid.value[0].value\n\n    if (!prevPageFunc && !props.prevPage.value) {\n      const newDate = firstDate.subtract({ months: props.pagedNavigation.value ? props.numberOfMonths.value : 1 })\n\n      const newGrid = createMonths({\n        dateObj: newDate,\n        weekStartsOn: props.weekStartsOn.value,\n        locale: props.locale.value,\n        fixedWeeks: props.fixedWeeks.value,\n        numberOfMonths: props.numberOfMonths.value,\n      })\n\n      grid.value = newGrid\n\n      props.placeholder.value = newGrid[0].value.set({ day: 1 })\n      return\n    }\n\n    const newDate = handlePrevPage(firstDate, prevPageFunc || props.prevPage.value!)\n    const newGrid = createMonths({\n      dateObj: newDate,\n      weekStartsOn: props.weekStartsOn.value,\n      locale: props.locale.value,\n      fixedWeeks: props.fixedWeeks.value,\n      numberOfMonths: props.numberOfMonths.value,\n    })\n\n    grid.value = newGrid\n\n    const duration: DateFields = {}\n\n    // Do not adjust the placeholder if the prevPageFunc is defined (overwrite)\n    if (!prevPageFunc) {\n      const diff = firstDate.compare(newGrid[0].value)\n      if (diff >= getDaysInMonth(firstDate))\n        duration.day = 1\n\n      if (diff >= 365)\n        duration.month = 1\n    }\n\n    props.placeholder.value = newGrid[0].value.set({ ...duration })\n  }\n\n  watch(props.placeholder, (value) => {\n    if (visibleView.value.some(month => isEqualMonth(month, value)))\n      return\n    grid.value = createMonths({\n      dateObj: value,\n      weekStartsOn: props.weekStartsOn.value,\n      locale: props.locale.value,\n      fixedWeeks: props.fixedWeeks.value,\n      numberOfMonths: props.numberOfMonths.value,\n    })\n  })\n\n  watch([props.locale, props.weekStartsOn, props.fixedWeeks, props.numberOfMonths], () => {\n    grid.value = createMonths({\n      dateObj: props.placeholder.value,\n      weekStartsOn: props.weekStartsOn.value,\n      locale: props.locale.value,\n      fixedWeeks: props.fixedWeeks.value,\n      numberOfMonths: props.numberOfMonths.value,\n    })\n  })\n\n  const headingValue = computed(() => {\n    if (!grid.value.length)\n      return ''\n\n    if (props.locale.value !== formatter.getLocale())\n      formatter.setLocale(props.locale.value)\n\n    if (grid.value.length === 1) {\n      const month = grid.value[0].value\n      return `${formatter.fullMonthAndYear(toDate(month), headingFormatOptions.value)}`\n    }\n\n    const startMonth = toDate(grid.value[0].value)\n    const endMonth = toDate(grid.value[grid.value.length - 1].value)\n\n    const startMonthName = formatter.fullMonth(startMonth, headingFormatOptions.value)\n    const endMonthName = formatter.fullMonth(endMonth, headingFormatOptions.value)\n    const startMonthYear = formatter.fullYear(startMonth, headingFormatOptions.value)\n    const endMonthYear = formatter.fullYear(endMonth, headingFormatOptions.value)\n\n    const content\n    = startMonthYear === endMonthYear\n      ? `${startMonthName} - ${endMonthName} ${endMonthYear}`\n      : `${startMonthName} ${startMonthYear} - ${endMonthName} ${endMonthYear}`\n\n    return content\n  })\n\n  const fullCalendarLabel = computed(() => `${props.calendarLabel.value ?? 'Event Date'}, ${headingValue.value}`)\n\n  return {\n    isDateDisabled,\n    isDateUnavailable,\n    isNextButtonDisabled,\n    isPrevButtonDisabled,\n    grid,\n    weekdays,\n    visibleView,\n    isOutsideVisibleView,\n    formatter,\n    nextPage,\n    prevPage,\n    headingValue,\n    fullCalendarLabel,\n  }\n}\n"], "names": ["isSameDay", "computed", "getDaysInMonth", "useDateFormatter", "ref", "createMonths", "date", "isEqualMonth", "firstPeriodOfNextPage", "isAfter", "lastPeriodOfPrevPage", "isBefore", "toDate", "newDate", "newGrid", "watch"], "mappings": ";;;;;;;;AAqCO,SAAS,iBAAiB,KAA8B,EAAA;AAC7D,EAAA,SAAS,eAAe,OAAoB,EAAA;AAC1C,IAAA,IAAI,KAAM,CAAA,OAAA,CAAQ,KAAM,CAAA,IAAA,CAAK,KAAK,CAAA;AAChC,MAAO,OAAA,KAAA,CAAM,KAAK,KAAM,CAAA,IAAA,CAAK,OAAKA,cAAU,CAAA,CAAA,EAAG,OAAO,CAAC,CAAA;AAAA,SAEhD,IAAA,CAAC,MAAM,IAAK,CAAA,KAAA;AACnB,MAAO,OAAA,KAAA;AAAA;AAGP,MAAA,OAAOA,cAAU,CAAA,KAAA,CAAM,IAAK,CAAA,KAAA,EAAO,OAAO,CAAA;AAAA;AAG9C,EAAA,MAAM,SAAY,GAAAC,YAAA;AAAA,IAChB,MAAM;AACJ,MAAA,IAAI,KAAM,CAAA,OAAA,CAAQ,KAAM,CAAA,IAAA,CAAK,KAAK,CAAG,EAAA;AACnC,QAAI,IAAA,CAAC,KAAM,CAAA,IAAA,CAAK,KAAM,CAAA,MAAA;AACpB,UAAO,OAAA,KAAA;AACT,QAAW,KAAA,MAAA,OAAA,IAAW,KAAM,CAAA,IAAA,CAAK,KAAO,EAAA;AACtC,UAAI,IAAA,KAAA,CAAM,iBAAiB,OAAO,CAAA;AAChC,YAAO,OAAA,IAAA;AACT,UAAI,IAAA,KAAA,CAAM,oBAAoB,OAAO,CAAA;AACnC,YAAO,OAAA,IAAA;AAAA;AACX,OAEG,MAAA;AACH,QAAI,IAAA,CAAC,MAAM,IAAK,CAAA,KAAA;AACd,UAAO,OAAA,KAAA;AACT,QAAA,IAAI,KAAM,CAAA,cAAA,GAAiB,KAAM,CAAA,IAAA,CAAK,KAAK,CAAA;AACzC,UAAO,OAAA,IAAA;AACT,QAAA,IAAI,KAAM,CAAA,iBAAA,GAAoB,KAAM,CAAA,IAAA,CAAK,KAAK,CAAA;AAC5C,UAAO,OAAA,IAAA;AAAA;AAEX,MAAO,OAAA,KAAA;AAAA;AACT,GACF;AAEA,EAAO,OAAA;AAAA,IACL,cAAA;AAAA,IACA;AAAA,GACF;AACF;AAEA,SAAS,kBAAA,CAAmB,kBAA6B,YAAyD,EAAA;AAChH,EAAM,MAAA,qBAAA,GAAwB,aAAa,gBAAgB,CAAA;AAC3D,EAAM,MAAA,IAAA,GAAO,qBAAsB,CAAA,OAAA,CAAQ,gBAAgB,CAAA;AAC3D,EAAA,MAAM,WAAuB,EAAC;AAC9B,EAAA,IAAI,IAAQ,IAAA,CAAA;AACV,IAAA,QAAA,CAAS,GAAM,GAAA,CAAA;AACjB,EAAI,IAAA,IAAA,IAAQC,gCAAe,gBAAgB,CAAA;AACzC,IAAA,QAAA,CAAS,KAAQ,GAAA,CAAA;AACnB,EAAA,OAAO,qBAAsB,CAAA,GAAA,CAAI,EAAE,GAAG,UAAU,CAAA;AAClD;AACA,SAAS,kBAAA,CAAmB,mBAA8B,YAAyD,EAAA;AACjH,EAAM,MAAA,oBAAA,GAAuB,aAAa,iBAAiB,CAAA;AAC3D,EAAM,MAAA,IAAA,GAAO,iBAAkB,CAAA,OAAA,CAAQ,oBAAoB,CAAA;AAC3D,EAAA,MAAM,WAAuB,EAAC;AAC9B,EAAA,IAAI,IAAQ,IAAA,CAAA;AACV,IAAA,QAAA,CAAS,GAAM,GAAA,EAAA;AACjB,EAAI,IAAA,IAAA,IAAQA,gCAAe,iBAAiB,CAAA;AAC1C,IAAA,QAAA,CAAS,KAAQ,GAAA,EAAA;AACnB,EAAA,OAAO,oBAAqB,CAAA,GAAA,CAAI,EAAE,GAAG,UAAU,CAAA;AACjD;AACA,SAAS,cAAA,CAAe,MAAiB,YAAyD,EAAA;AAChG,EAAA,OAAO,aAAa,IAAI,CAAA;AAC1B;AAEA,SAAS,cAAA,CAAe,MAAiB,YAAyD,EAAA;AAChG,EAAA,OAAO,aAAa,IAAI,CAAA;AAC1B;AAEO,SAAS,YAAY,KAAyB,EAAA;AACnD,EAAA,MAAM,SAAY,GAAAC,wCAAA,CAAiB,KAAM,CAAA,MAAA,CAAO,KAAK,CAAA;AAErD,EAAM,MAAA,oBAAA,GAAuBF,aAAS,MAAM;AAC1C,IAAA,MAAM,OAAgC,GAAA;AAAA,MACpC,QAAU,EAAA,KAAA,CAAM,WAAY,CAAA,KAAA,CAAM,QAAS,CAAA;AAAA,KAC7C;AAEA,IAAI,IAAA,KAAA,CAAM,YAAY,KAAM,CAAA,QAAA,CAAS,eAAe,SAAa,IAAA,KAAA,CAAM,WAAY,CAAA,KAAA,CAAM,GAAQ,KAAA,IAAA;AAC/F,MAAA,OAAA,CAAQ,GAAM,GAAA,OAAA;AAEhB,IAAO,OAAA,OAAA;AAAA,GACR,CAAA;AAED,EAAM,MAAA,IAAA,GAAOG,QAAuBC,0BAAa,CAAA;AAAA,IAC/C,OAAA,EAAS,MAAM,WAAY,CAAA,KAAA;AAAA,IAC3B,YAAA,EAAc,MAAM,YAAa,CAAA,KAAA;AAAA,IACjC,MAAA,EAAQ,MAAM,MAAO,CAAA,KAAA;AAAA,IACrB,UAAA,EAAY,MAAM,UAAW,CAAA,KAAA;AAAA,IAC7B,cAAA,EAAgB,MAAM,cAAe,CAAA;AAAA,GACtC,CAAC,CAAA;AAEF,EAAM,MAAA,WAAA,GAAcJ,aAAS,MAAM;AACjC,IAAA,OAAO,IAAK,CAAA,KAAA,CAAM,GAAI,CAAA,CAAA,KAAA,KAAS,MAAM,KAAK,CAAA;AAAA,GAC3C,CAAA;AAED,EAAA,SAAS,qBAAqBK,MAAiB,EAAA;AAC7C,IAAO,OAAA,CAAC,YAAY,KAAM,CAAA,IAAA,CAAK,WAASC,iBAAa,CAAAD,MAAA,EAAM,KAAK,CAAC,CAAA;AAAA;AAGnE,EAAM,MAAA,oBAAA,GAAuB,CAAC,YAAkD,KAAA;AAC9E,IAAA,IAAI,CAAC,KAAM,CAAA,QAAA,CAAS,KAAS,IAAA,CAAC,KAAK,KAAM,CAAA,MAAA;AACvC,MAAO,OAAA,KAAA;AACT,IAAA,IAAI,MAAM,QAAS,CAAA,KAAA;AACjB,MAAO,OAAA,IAAA;AAET,IAAA,MAAM,mBAAmB,IAAK,CAAA,KAAA,CAAM,KAAK,KAAM,CAAA,MAAA,GAAS,CAAC,CAAE,CAAA,KAAA;AAE3D,IAAA,IAAI,CAAC,YAAA,IAAgB,CAAC,KAAA,CAAM,SAAS,KAAO,EAAA;AAC1C,MAAA,MAAME,sBAAwB,GAAA,gBAAA,CAAiB,GAAI,CAAA,EAAE,MAAQ,EAAA,CAAA,EAAG,CAAA,CAAE,GAAI,CAAA,EAAE,GAAK,EAAA,CAAA,EAAG,CAAA;AAChF,MAAA,OAAOC,wBAAQD,CAAAA,sBAAAA,EAAuB,KAAM,CAAA,QAAA,CAAS,KAAK,CAAA;AAAA;AAG5D,IAAA,MAAM,wBAAwB,kBAAmB,CAAA,gBAAA,EAAkB,YAAgB,IAAA,KAAA,CAAM,SAAS,KAAM,CAAA;AACxG,IAAA,OAAOC,wBAAQ,CAAA,qBAAA,EAAuB,KAAM,CAAA,QAAA,CAAS,KAAK,CAAA;AAAA,GAC5D;AAEA,EAAM,MAAA,oBAAA,GAAuB,CAAC,YAAkD,KAAA;AAC9E,IAAA,IAAI,CAAC,KAAM,CAAA,QAAA,CAAS,KAAS,IAAA,CAAC,KAAK,KAAM,CAAA,MAAA;AACvC,MAAO,OAAA,KAAA;AACT,IAAA,IAAI,MAAM,QAAS,CAAA,KAAA;AACjB,MAAO,OAAA,IAAA;AACT,IAAA,MAAM,iBAAoB,GAAA,IAAA,CAAK,KAAM,CAAA,CAAC,CAAE,CAAA,KAAA;AAExC,IAAA,IAAI,CAAC,YAAA,IAAgB,CAAC,KAAA,CAAM,SAAS,KAAO,EAAA;AAC1C,MAAA,MAAMC,qBAAuB,GAAA,iBAAA,CAAkB,QAAS,CAAA,EAAE,MAAQ,EAAA,CAAA,EAAG,CAAA,CAAE,GAAI,CAAA,EAAE,GAAK,EAAA,EAAA,EAAI,CAAA;AACtF,MAAA,OAAOC,yBAASD,CAAAA,qBAAAA,EAAsB,KAAM,CAAA,QAAA,CAAS,KAAK,CAAA;AAAA;AAG5D,IAAA,MAAM,uBAAuB,kBAAmB,CAAA,iBAAA,EAAmB,YAAgB,IAAA,KAAA,CAAM,SAAS,KAAM,CAAA;AACxG,IAAA,OAAOC,yBAAS,CAAA,oBAAA,EAAsB,KAAM,CAAA,QAAA,CAAS,KAAK,CAAA;AAAA,GAC5D;AAEA,EAAA,SAAS,eAAe,OAAoB,EAAA;AAC1C,IAAA,IAAI,KAAM,CAAA,cAAA,GAAiB,OAAO,CAAA,IAAK,MAAM,QAAS,CAAA,KAAA;AACpD,MAAO,OAAA,IAAA;AACT,IAAA,IAAI,MAAM,QAAS,CAAA,KAAA,IAASF,yBAAQ,OAAS,EAAA,KAAA,CAAM,SAAS,KAAK,CAAA;AAC/D,MAAO,OAAA,IAAA;AACT,IAAA,IAAI,MAAM,QAAS,CAAA,KAAA,IAASE,0BAAS,OAAS,EAAA,KAAA,CAAM,SAAS,KAAK,CAAA;AAChE,MAAO,OAAA,IAAA;AACT,IAAO,OAAA,KAAA;AAAA;AAGT,EAAM,MAAA,iBAAA,GAAoB,CAAC,IAAoB,KAAA;AAC7C,IAAI,IAAA,KAAA,CAAM,oBAAoB,IAAI,CAAA;AAChC,MAAO,OAAA,IAAA;AACT,IAAO,OAAA,KAAA;AAAA,GACT;AAEA,EAAM,MAAA,QAAA,GAAWV,aAAS,MAAM;AAC9B,IAAI,IAAA,CAAC,KAAK,KAAM,CAAA,MAAA;AACd,MAAA,OAAO,EAAC;AACV,IAAO,OAAA,IAAA,CAAK,MAAM,CAAC,CAAA,CAAE,KAAK,CAAC,CAAA,CAAE,GAAI,CAAA,CAAC,IAAS,KAAA;AACzC,MAAA,OAAO,UAAU,SAAU,CAAAW,uBAAA,CAAO,IAAI,CAAG,EAAA,KAAA,CAAM,cAAc,KAAK,CAAA;AAAA,KACnE,CAAA;AAAA,GACF,CAAA;AAED,EAAM,MAAA,QAAA,GAAW,CAAC,YAAkD,KAAA;AAClE,IAAA,MAAM,SAAY,GAAA,IAAA,CAAK,KAAM,CAAA,CAAC,CAAE,CAAA,KAAA;AAEhC,IAAA,IAAI,CAAC,YAAA,IAAgB,CAAC,KAAA,CAAM,SAAS,KAAO,EAAA;AAC1C,MAAA,MAAMC,QAAU,GAAA,SAAA,CAAU,GAAI,CAAA,EAAE,MAAQ,EAAA,KAAA,CAAM,eAAgB,CAAA,KAAA,GAAQ,KAAM,CAAA,cAAA,CAAe,KAAQ,GAAA,CAAA,EAAG,CAAA;AAEtG,MAAA,MAAMC,WAAUT,0BAAa,CAAA;AAAA,QAC3B,OAASQ,EAAAA,QAAAA;AAAA,QACT,YAAA,EAAc,MAAM,YAAa,CAAA,KAAA;AAAA,QACjC,MAAA,EAAQ,MAAM,MAAO,CAAA,KAAA;AAAA,QACrB,UAAA,EAAY,MAAM,UAAW,CAAA,KAAA;AAAA,QAC7B,cAAA,EAAgB,MAAM,cAAe,CAAA;AAAA,OACtC,CAAA;AAED,MAAA,IAAA,CAAK,KAAQC,GAAAA,QAAAA;AAEb,MAAM,KAAA,CAAA,WAAA,CAAY,KAAQA,GAAAA,QAAAA,CAAQ,CAAC,CAAA,CAAE,MAAM,GAAI,CAAA,EAAE,GAAK,EAAA,CAAA,EAAG,CAAA;AACzD,MAAA;AAAA;AAGF,IAAA,MAAM,UAAU,cAAe,CAAA,SAAA,EAAW,YAAgB,IAAA,KAAA,CAAM,SAAS,KAAM,CAAA;AAC/E,IAAA,MAAM,UAAUT,0BAAa,CAAA;AAAA,MAC3B,OAAS,EAAA,OAAA;AAAA,MACT,YAAA,EAAc,MAAM,YAAa,CAAA,KAAA;AAAA,MACjC,MAAA,EAAQ,MAAM,MAAO,CAAA,KAAA;AAAA,MACrB,UAAA,EAAY,MAAM,UAAW,CAAA,KAAA;AAAA,MAC7B,cAAA,EAAgB,MAAM,cAAe,CAAA;AAAA,KACtC,CAAA;AAED,IAAA,IAAA,CAAK,KAAQ,GAAA,OAAA;AAEb,IAAA,MAAM,WAAuB,EAAC;AAG9B,IAAA,IAAI,CAAC,YAAc,EAAA;AACjB,MAAA,MAAM,OAAO,OAAQ,CAAA,CAAC,CAAE,CAAA,KAAA,CAAM,QAAQ,SAAS,CAAA;AAC/C,MAAI,IAAA,IAAA,IAAQH,gCAAe,SAAS,CAAA;AAClC,QAAA,QAAA,CAAS,GAAM,GAAA,CAAA;AAEjB,MAAA,IAAI,IAAQ,IAAA,GAAA;AACV,QAAA,QAAA,CAAS,KAAQ,GAAA,CAAA;AAAA;AAGrB,IAAM,KAAA,CAAA,WAAA,CAAY,KAAQ,GAAA,OAAA,CAAQ,CAAC,CAAA,CAAE,MAAM,GAAI,CAAA,EAAE,GAAG,QAAA,EAAU,CAAA;AAAA,GAChE;AAEA,EAAM,MAAA,QAAA,GAAW,CAAC,YAAkD,KAAA;AAClE,IAAA,MAAM,SAAY,GAAA,IAAA,CAAK,KAAM,CAAA,CAAC,CAAE,CAAA,KAAA;AAEhC,IAAA,IAAI,CAAC,YAAA,IAAgB,CAAC,KAAA,CAAM,SAAS,KAAO,EAAA;AAC1C,MAAA,MAAMW,QAAU,GAAA,SAAA,CAAU,QAAS,CAAA,EAAE,MAAQ,EAAA,KAAA,CAAM,eAAgB,CAAA,KAAA,GAAQ,KAAM,CAAA,cAAA,CAAe,KAAQ,GAAA,CAAA,EAAG,CAAA;AAE3G,MAAA,MAAMC,WAAUT,0BAAa,CAAA;AAAA,QAC3B,OAASQ,EAAAA,QAAAA;AAAA,QACT,YAAA,EAAc,MAAM,YAAa,CAAA,KAAA;AAAA,QACjC,MAAA,EAAQ,MAAM,MAAO,CAAA,KAAA;AAAA,QACrB,UAAA,EAAY,MAAM,UAAW,CAAA,KAAA;AAAA,QAC7B,cAAA,EAAgB,MAAM,cAAe,CAAA;AAAA,OACtC,CAAA;AAED,MAAA,IAAA,CAAK,KAAQC,GAAAA,QAAAA;AAEb,MAAM,KAAA,CAAA,WAAA,CAAY,KAAQA,GAAAA,QAAAA,CAAQ,CAAC,CAAA,CAAE,MAAM,GAAI,CAAA,EAAE,GAAK,EAAA,CAAA,EAAG,CAAA;AACzD,MAAA;AAAA;AAGF,IAAA,MAAM,UAAU,cAAe,CAAA,SAAA,EAAW,YAAgB,IAAA,KAAA,CAAM,SAAS,KAAM,CAAA;AAC/E,IAAA,MAAM,UAAUT,0BAAa,CAAA;AAAA,MAC3B,OAAS,EAAA,OAAA;AAAA,MACT,YAAA,EAAc,MAAM,YAAa,CAAA,KAAA;AAAA,MACjC,MAAA,EAAQ,MAAM,MAAO,CAAA,KAAA;AAAA,MACrB,UAAA,EAAY,MAAM,UAAW,CAAA,KAAA;AAAA,MAC7B,cAAA,EAAgB,MAAM,cAAe,CAAA;AAAA,KACtC,CAAA;AAED,IAAA,IAAA,CAAK,KAAQ,GAAA,OAAA;AAEb,IAAA,MAAM,WAAuB,EAAC;AAG9B,IAAA,IAAI,CAAC,YAAc,EAAA;AACjB,MAAA,MAAM,OAAO,SAAU,CAAA,OAAA,CAAQ,OAAQ,CAAA,CAAC,EAAE,KAAK,CAAA;AAC/C,MAAI,IAAA,IAAA,IAAQH,gCAAe,SAAS,CAAA;AAClC,QAAA,QAAA,CAAS,GAAM,GAAA,CAAA;AAEjB,MAAA,IAAI,IAAQ,IAAA,GAAA;AACV,QAAA,QAAA,CAAS,KAAQ,GAAA,CAAA;AAAA;AAGrB,IAAM,KAAA,CAAA,WAAA,CAAY,KAAQ,GAAA,OAAA,CAAQ,CAAC,CAAA,CAAE,MAAM,GAAI,CAAA,EAAE,GAAG,QAAA,EAAU,CAAA;AAAA,GAChE;AAEA,EAAMa,SAAA,CAAA,KAAA,CAAM,WAAa,EAAA,CAAC,KAAU,KAAA;AAClC,IAAA,IAAI,YAAY,KAAM,CAAA,IAAA,CAAK,WAASR,iBAAa,CAAA,KAAA,EAAO,KAAK,CAAC,CAAA;AAC5D,MAAA;AACF,IAAA,IAAA,CAAK,QAAQF,0BAAa,CAAA;AAAA,MACxB,OAAS,EAAA,KAAA;AAAA,MACT,YAAA,EAAc,MAAM,YAAa,CAAA,KAAA;AAAA,MACjC,MAAA,EAAQ,MAAM,MAAO,CAAA,KAAA;AAAA,MACrB,UAAA,EAAY,MAAM,UAAW,CAAA,KAAA;AAAA,MAC7B,cAAA,EAAgB,MAAM,cAAe,CAAA;AAAA,KACtC,CAAA;AAAA,GACF,CAAA;AAED,EAAMU,SAAA,CAAA,CAAC,KAAM,CAAA,MAAA,EAAQ,KAAM,CAAA,YAAA,EAAc,MAAM,UAAY,EAAA,KAAA,CAAM,cAAc,CAAA,EAAG,MAAM;AACtF,IAAA,IAAA,CAAK,QAAQV,0BAAa,CAAA;AAAA,MACxB,OAAA,EAAS,MAAM,WAAY,CAAA,KAAA;AAAA,MAC3B,YAAA,EAAc,MAAM,YAAa,CAAA,KAAA;AAAA,MACjC,MAAA,EAAQ,MAAM,MAAO,CAAA,KAAA;AAAA,MACrB,UAAA,EAAY,MAAM,UAAW,CAAA,KAAA;AAAA,MAC7B,cAAA,EAAgB,MAAM,cAAe,CAAA;AAAA,KACtC,CAAA;AAAA,GACF,CAAA;AAED,EAAM,MAAA,YAAA,GAAeJ,aAAS,MAAM;AAClC,IAAI,IAAA,CAAC,KAAK,KAAM,CAAA,MAAA;AACd,MAAO,OAAA,EAAA;AAET,IAAA,IAAI,KAAM,CAAA,MAAA,CAAO,KAAU,KAAA,SAAA,CAAU,SAAU,EAAA;AAC7C,MAAU,SAAA,CAAA,SAAA,CAAU,KAAM,CAAA,MAAA,CAAO,KAAK,CAAA;AAExC,IAAI,IAAA,IAAA,CAAK,KAAM,CAAA,MAAA,KAAW,CAAG,EAAA;AAC3B,MAAA,MAAM,KAAQ,GAAA,IAAA,CAAK,KAAM,CAAA,CAAC,CAAE,CAAA,KAAA;AAC5B,MAAO,OAAA,CAAA,EAAG,UAAU,gBAAiB,CAAAW,uBAAA,CAAO,KAAK,CAAG,EAAA,oBAAA,CAAqB,KAAK,CAAC,CAAA,CAAA;AAAA;AAGjF,IAAA,MAAM,aAAaA,uBAAO,CAAA,IAAA,CAAK,KAAM,CAAA,CAAC,EAAE,KAAK,CAAA;AAC7C,IAAM,MAAA,QAAA,GAAWA,wBAAO,IAAK,CAAA,KAAA,CAAM,KAAK,KAAM,CAAA,MAAA,GAAS,CAAC,CAAA,CAAE,KAAK,CAAA;AAE/D,IAAA,MAAM,cAAiB,GAAA,SAAA,CAAU,SAAU,CAAA,UAAA,EAAY,qBAAqB,KAAK,CAAA;AACjF,IAAA,MAAM,YAAe,GAAA,SAAA,CAAU,SAAU,CAAA,QAAA,EAAU,qBAAqB,KAAK,CAAA;AAC7E,IAAA,MAAM,cAAiB,GAAA,SAAA,CAAU,QAAS,CAAA,UAAA,EAAY,qBAAqB,KAAK,CAAA;AAChF,IAAA,MAAM,YAAe,GAAA,SAAA,CAAU,QAAS,CAAA,QAAA,EAAU,qBAAqB,KAAK,CAAA;AAE5E,IAAA,MAAM,UACJ,cAAmB,KAAA,YAAA,GACjB,CAAG,EAAA,cAAc,MAAM,YAAY,CAAA,CAAA,EAAI,YAAY,CAAA,CAAA,GACnD,GAAG,cAAc,CAAA,CAAA,EAAI,cAAc,CAAM,GAAA,EAAA,YAAY,IAAI,YAAY,CAAA,CAAA;AAEzE,IAAO,OAAA,OAAA;AAAA,GACR,CAAA;AAED,EAAM,MAAA,iBAAA,GAAoBX,YAAS,CAAA,MAAM,CAAG,EAAA,KAAA,CAAM,aAAc,CAAA,KAAA,IAAS,YAAY,CAAA,EAAA,EAAK,YAAa,CAAA,KAAK,CAAE,CAAA,CAAA;AAE9G,EAAO,OAAA;AAAA,IACL,cAAA;AAAA,IACA,iBAAA;AAAA,IACA,oBAAA;AAAA,IACA,oBAAA;AAAA,IACA,IAAA;AAAA,IACA,QAAA;AAAA,IACA,WAAA;AAAA,IACA,oBAAA;AAAA,IACA,SAAA;AAAA,IACA,QAAA;AAAA,IACA,QAAA;AAAA,IACA,YAAA;AAAA,IACA;AAAA,GACF;AACF;;;;;"}