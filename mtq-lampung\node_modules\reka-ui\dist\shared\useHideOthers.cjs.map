{"version": 3, "file": "useHideOthers.cjs", "sources": ["../../src/shared/useHideOthers.ts"], "sourcesContent": ["import type { MaybeElementRef } from '@vueuse/core'\nimport { unrefElement } from '@vueuse/core'\nimport { hideOthers } from 'aria-hidden'\nimport { onUnmounted, watch } from 'vue'\n\n/**\n * The `useHideOthers` function is a TypeScript function that takes a target element reference and\n * hides all other elements in ARIA when the target element is present, and restores the visibility of the\n * hidden elements when the target element is removed.\n * @param {MaybeElementRef} target - The `target` parameter is a reference to the element that you want\n * to hide other elements when it is clicked or focused.\n */\nexport function useHideOthers(target: MaybeElementRef) {\n  let undo: ReturnType<typeof hideOthers>\n  watch(() => unrefElement(target), (el) => {\n    // disable hideOthers on test mode\n    if (import.meta.env.MODE === 'test')\n      return\n    if (el)\n      undo = hideOthers(el)\n    else if (undo)\n      undo()\n  })\n\n  onUnmounted(() => {\n    if (undo)\n      undo()\n  })\n}\n"], "names": ["watch", "unrefElement", "hideOthers", "onUnmounted"], "mappings": ";;;;;;AAYO,SAAS,cAAc,MAAyB,EAAA;AACjD,EAAA,IAAA,IAAA;AACJ,EAAAA,SAAA,CAAM,MAAMC,iBAAA,CAAa,MAAM,CAAA,EAAG,CAAC,EAAO,KAAA;AAIpC,IAAA,IAAA,EAAA;AACF,MAAA,IAAA,GAAOC,sBAAW,EAAE,CAAA;AAAA,SACb,IAAA,IAAA;AACF,MAAA,IAAA,EAAA;AAAA,GACR,CAAA;AAED,EAAAC,eAAA,CAAY,MAAM;AACZ,IAAA,IAAA,IAAA;AACG,MAAA,IAAA,EAAA;AAAA,GACR,CAAA;AACH;;;;"}