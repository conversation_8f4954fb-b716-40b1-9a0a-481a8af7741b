{"version": 3, "file": "useEmitAsProps.js", "sources": ["../../src/shared/useEmitAsProps.ts"], "sourcesContent": ["import { camelize, getCurrentInstance, toH<PERSON>ler<PERSON><PERSON> } from 'vue'\n\n// Vue doesn't have emits forwarding, in order to bind the emits we have to convert events into `onXXX` handlers\n// issue: https://github.com/vuejs/core/issues/5917\n/**\n * The `useEmitAsProps` function is a TypeScript utility that converts emitted events into props for a\n * Vue component.\n * @param emit - The `emit` parameter is a function that is used to emit events from a component. It\n * takes two parameters: `name` which is the name of the event to be emitted, and `...args` which are\n * the arguments to be passed along with the event.\n * @returns The function `useEmitAsProps` returns an object that maps event names to functions that\n * call the `emit` function with the corresponding event name and arguments.\n */\nexport function useEmitAsProps<Name extends string>(\n  emit: (name: Name, ...args: any[]) => void,\n) {\n  const vm = getCurrentInstance()\n\n  const events = vm?.type.emits as Name[]\n  const result: Record<string, any> = {}\n\n  if (!events?.length) {\n    console.warn(\n      `No emitted event found. Please check component: ${vm?.type.__name}`,\n    )\n  }\n\n  events?.forEach((ev) => {\n    result[toHandler<PERSON>ey(camelize(ev))] = (...arg: any) => emit(ev, ...arg)\n  })\n  return result\n}\n"], "names": [], "mappings": ";;AAaO,SAAS,eACd,IACA,EAAA;AACA,EAAA,MAAM,KAAK,kBAAmB,EAAA;AAE9B,EAAM,MAAA,MAAA,GAAS,IAAI,IAAK,CAAA,KAAA;AACxB,EAAA,MAAM,SAA8B,EAAC;AAErC,EAAI,IAAA,CAAC,QAAQ,MAAQ,EAAA;AACnB,IAAQ,OAAA,CAAA,IAAA;AAAA,MACN,CAAA,gDAAA,EAAmD,EAAI,EAAA,IAAA,CAAK,MAAM,CAAA;AAAA,KACpE;AAAA;AAGF,EAAQ,MAAA,EAAA,OAAA,CAAQ,CAAC,EAAO,KAAA;AACtB,IAAO,MAAA,CAAA,YAAA,CAAa,QAAS,CAAA,EAAE,CAAC,CAAC,CAAI,GAAA,CAAA,GAAI,GAAa,KAAA,IAAA,CAAK,EAAI,EAAA,GAAG,GAAG,CAAA;AAAA,GACtE,CAAA;AACD,EAAO,OAAA,MAAA;AACT;;;;"}