{"version": 3, "file": "utils.cjs", "sources": ["../../src/HoverCard/utils.ts"], "sourcesContent": ["export function excludeTouch(eventHandler: () => void) {\n  return (event: PointerEvent) => event.pointerType === 'touch' ? undefined : eventHandler()\n}\n\n/**\n * Returns a list of nodes that can be in the tab sequence.\n * @see: https://developer.mozilla.org/en-US/docs/Web/API/TreeWalker\n */\nexport function getTabbableNodes(container: HTMLElement) {\n  const nodes: HTMLElement[] = []\n  const walker = document.createTreeWalker(container, NodeFilter.SHOW_ELEMENT, {\n    acceptNode: (node: any) => {\n      // `.tabIndex` is not the same as the `tabindex` attribute. It works on the\n      // runtime's understanding of tabbability, so this automatically accounts\n      // for any kind of element that could be tabbed to.\n      return node.tabIndex >= 0 ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_SKIP\n    },\n  })\n  while (walker.nextNode()) nodes.push(walker.currentNode as HTMLElement)\n  return nodes\n}\n"], "names": [], "mappings": ";;AAAO,SAAS,aAAa,YAA0B,EAAA;AACrD,EAAA,OAAO,CAAC,KAAwB,KAAA,KAAA,CAAM,WAAgB,KAAA,OAAA,GAAU,SAAY,YAAa,EAAA;AAC3F;AAMO,SAAS,iBAAiB,SAAwB,EAAA;AACvD,EAAA,MAAM,QAAuB,EAAC;AAC9B,EAAA,MAAM,MAAS,GAAA,QAAA,CAAS,gBAAiB,CAAA,SAAA,EAAW,WAAW,YAAc,EAAA;AAAA,IAC3E,UAAA,EAAY,CAAC,IAAc,KAAA;AAIzB,MAAA,OAAO,IAAK,CAAA,QAAA,IAAY,CAAI,GAAA,UAAA,CAAW,gBAAgB,UAAW,CAAA,WAAA;AAAA;AACpE,GACD,CAAA;AACD,EAAA,OAAO,OAAO,QAAS,EAAA,EAAS,KAAA,CAAA,IAAA,CAAK,OAAO,WAA0B,CAAA;AACtE,EAAO,OAAA,KAAA;AACT;;;;;"}