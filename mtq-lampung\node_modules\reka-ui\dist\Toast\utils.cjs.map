{"version": 3, "file": "utils.cjs", "sources": ["../../src/Toast/utils.ts"], "sourcesContent": ["export const TOAST_SWIPE_START = 'toast.swipeStart'\nexport const TOAST_SWIPE_MOVE = 'toast.swipeMove'\nexport const TOAST_SWIPE_CANCEL = 'toast.swipeCancel'\nexport const TOAST_SWIPE_END = 'toast.swipeEnd'\n\nexport const VIEWPORT_NAME = 'ToastViewport'\nexport const VIEWPORT_DEFAULT_HOTKEY = ['F8']\nexport const VIEWPORT_PAUSE = 'toast.viewportPause'\nexport const VIEWPORT_RESUME = 'toast.viewportResume'\n\nexport type SwipeDirection = 'up' | 'down' | 'left' | 'right'\n\nexport type SwipeEvent = { currentTarget: EventTarget & HTMLElement } & Omit<\n  CustomEvent<{ originalEvent: PointerEvent, delta: { x: number, y: number } }>,\n  'currentTarget'\n>\n\nexport function handleAndDispatchCustomEvent<\n  E extends CustomEvent,\n  OriginalEvent extends Event,\n>(\n  name: string,\n  handler: ((event: E) => void) | undefined,\n  detail: { originalEvent: OriginalEvent } & (E extends CustomEvent<infer D>\n    ? D\n    : never),\n) {\n  const currentTarget = detail.originalEvent.currentTarget as HTMLElement\n  const event = new CustomEvent(name, {\n    bubbles: false,\n    cancelable: true,\n    detail,\n  })\n  if (handler)\n    currentTarget.addEventListener(name, handler as EventListener, { once: true })\n\n  currentTarget.dispatchEvent(event)\n}\n\nexport function isDeltaInDirection(delta: { x: number, y: number }, direction: SwipeDirection, threshold = 0) {\n  const deltaX = Math.abs(delta.x)\n  const deltaY = Math.abs(delta.y)\n  const isDeltaX = deltaX > deltaY\n  if (direction === 'left' || direction === 'right')\n    return isDeltaX && deltaX > threshold\n  else\n    return !isDeltaX && deltaY > threshold\n}\n\nexport function isHTMLElement(node: any): node is HTMLElement {\n  return node.nodeType === node.ELEMENT_NODE\n}\n\nexport function getAnnounceTextContent(container: HTMLElement) {\n  const textContent: string[] = []\n  const childNodes = Array.from(container.childNodes)\n\n  childNodes.forEach((node) => {\n    if (node.nodeType === node.TEXT_NODE && node.textContent)\n      textContent.push(node.textContent)\n    if (isHTMLElement(node)) {\n      const isHidden = node.ariaHidden || node.hidden || node.style.display === 'none'\n      const isExcluded = node.dataset.rekaToastAnnounceExclude === ''\n\n      if (!isHidden) {\n        if (isExcluded) {\n          const altText = node.dataset.rekaToastAnnounceAlt\n          if (altText)\n            textContent.push(altText)\n        }\n        else {\n          textContent.push(...getAnnounceTextContent(node))\n        }\n      }\n    }\n  })\n  // We return a collection of text rather than a single concatenated string.\n  // This allows SR VO to naturally pause break between nodes while announcing.\n  return textContent\n}\n"], "names": [], "mappings": ";;AAAO,MAAM,iBAAoB,GAAA;AAC1B,MAAM,gBAAmB,GAAA;AACzB,MAAM,kBAAqB,GAAA;AAC3B,MAAM,eAAkB,GAAA;AAIxB,MAAM,cAAiB,GAAA;AACvB,MAAM,eAAkB,GAAA;AASf,SAAA,4BAAA,CAId,IACA,EAAA,OAAA,EACA,MAGA,EAAA;AACA,EAAM,MAAA,aAAA,GAAgB,OAAO,aAAc,CAAA,aAAA;AAC3C,EAAM,MAAA,KAAA,GAAQ,IAAI,WAAA,CAAY,IAAM,EAAA;AAAA,IAClC,OAAS,EAAA,KAAA;AAAA,IACT,UAAY,EAAA,IAAA;AAAA,IACZ;AAAA,GACD,CAAA;AACD,EAAI,IAAA,OAAA;AACF,IAAA,aAAA,CAAc,iBAAiB,IAAM,EAAA,OAAA,EAA0B,EAAE,IAAA,EAAM,MAAM,CAAA;AAE/E,EAAA,aAAA,CAAc,cAAc,KAAK,CAAA;AACnC;AAEO,SAAS,kBAAmB,CAAA,KAAA,EAAiC,SAA2B,EAAA,SAAA,GAAY,CAAG,EAAA;AAC5G,EAAA,MAAM,MAAS,GAAA,IAAA,CAAK,GAAI,CAAA,KAAA,CAAM,CAAC,CAAA;AAC/B,EAAA,MAAM,MAAS,GAAA,IAAA,CAAK,GAAI,CAAA,KAAA,CAAM,CAAC,CAAA;AAC/B,EAAA,MAAM,WAAW,MAAS,GAAA,MAAA;AAC1B,EAAI,IAAA,SAAA,KAAc,UAAU,SAAc,KAAA,OAAA;AACxC,IAAA,OAAO,YAAY,MAAS,GAAA,SAAA;AAAA;AAE5B,IAAO,OAAA,CAAC,YAAY,MAAS,GAAA,SAAA;AACjC;AAEO,SAAS,cAAc,IAAgC,EAAA;AAC5D,EAAO,OAAA,IAAA,CAAK,aAAa,IAAK,CAAA,YAAA;AAChC;AAEO,SAAS,uBAAuB,SAAwB,EAAA;AAC7D,EAAA,MAAM,cAAwB,EAAC;AAC/B,EAAA,MAAM,UAAa,GAAA,KAAA,CAAM,IAAK,CAAA,SAAA,CAAU,UAAU,CAAA;AAElD,EAAW,UAAA,CAAA,OAAA,CAAQ,CAAC,IAAS,KAAA;AAC3B,IAAA,IAAI,IAAK,CAAA,QAAA,KAAa,IAAK,CAAA,SAAA,IAAa,IAAK,CAAA,WAAA;AAC3C,MAAY,WAAA,CAAA,IAAA,CAAK,KAAK,WAAW,CAAA;AACnC,IAAI,IAAA,aAAA,CAAc,IAAI,CAAG,EAAA;AACvB,MAAA,MAAM,WAAW,IAAK,CAAA,UAAA,IAAc,KAAK,MAAU,IAAA,IAAA,CAAK,MAAM,OAAY,KAAA,MAAA;AAC1E,MAAM,MAAA,UAAA,GAAa,IAAK,CAAA,OAAA,CAAQ,wBAA6B,KAAA,EAAA;AAE7D,MAAA,IAAI,CAAC,QAAU,EAAA;AACb,QAAA,IAAI,UAAY,EAAA;AACd,UAAM,MAAA,OAAA,GAAU,KAAK,OAAQ,CAAA,oBAAA;AAC7B,UAAI,IAAA,OAAA;AACF,YAAA,WAAA,CAAY,KAAK,OAAO,CAAA;AAAA,SAEvB,MAAA;AACH,UAAA,WAAA,CAAY,IAAK,CAAA,GAAG,sBAAuB,CAAA,IAAI,CAAC,CAAA;AAAA;AAClD;AACF;AACF,GACD,CAAA;AAGD,EAAO,OAAA,WAAA;AACT;;;;;;;;;;;;"}