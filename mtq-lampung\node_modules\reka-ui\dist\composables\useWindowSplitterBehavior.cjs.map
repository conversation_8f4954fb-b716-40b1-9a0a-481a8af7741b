{"version": 3, "file": "useWindowSplitterBehavior.cjs", "sources": ["../../src/Splitter/utils/composables/useWindowSplitterBehavior.ts"], "sourcesContent": ["import type { Ref } from 'vue'\nimport type { Resi<PERSON><PERSON><PERSON><PERSON> } from '../types'\nimport { watchEffect } from 'vue'\nimport { assert } from '../assert'\nimport { getResizeHandleElement, getResizeHandleElementIndex, getResizeHandleElementsForGroup } from '../dom'\n\n// https://www.w3.org/WAI/ARIA/apg/patterns/windowsplitter/\n\nexport function useWindowSplitterResizeHandlerBehavior({\n  disabled,\n  handleId,\n  resizeHandler,\n  panelGroupElement,\n}: {\n  disabled: Ref<boolean>\n  handleId: string\n  resizeHandler: Ref<ResizeHandler | null>\n  panelGroupElement: Ref<ParentNode | null>\n}): void {\n  watchEffect((onCleanup) => {\n    const _panelGroupElement = panelGroupElement.value\n    if (disabled.value || resizeHandler.value === null || _panelGroupElement === null)\n      return\n\n    const handleElement = getResizeHandleElement(handleId, _panelGroupElement)\n    if (handleElement == null)\n      return\n\n    const onKeyDown = (event: KeyboardEvent) => {\n      if (event.defaultPrevented)\n        return\n\n      switch (event.key) {\n        case 'ArrowDown':\n        case 'ArrowLeft':\n        case 'ArrowRight':\n        case 'ArrowUp':\n        case 'End':\n        case 'Home': {\n          event.preventDefault()\n\n          resizeHandler.value?.(event)\n          break\n        }\n        case 'F6': {\n          event.preventDefault()\n\n          const groupId = handleElement.getAttribute('data-panel-group-id')\n          assert(groupId)\n\n          const handles = getResizeHandleElementsForGroup(\n            groupId,\n            _panelGroupElement,\n          )\n          const index = getResizeHandleElementIndex(\n            groupId,\n            handleId,\n            _panelGroupElement,\n          )\n\n          assert(index !== null)\n\n          const nextIndex = event.shiftKey\n            ? index > 0\n              ? index - 1\n              : handles.length - 1\n            : index + 1 < handles.length\n              ? index + 1\n              : 0\n\n          const nextHandle = handles[nextIndex] as HTMLElement\n          nextHandle.focus()\n\n          break\n        }\n      }\n    }\n\n    handleElement.addEventListener('keydown', onKeyDown)\n    onCleanup(() => {\n      handleElement.removeEventListener('keydown', onKeyDown)\n    })\n  })\n}\n"], "names": ["watchEffect", "getResizeHandleElement", "assert", "getResizeHandleElementsForGroup", "getResizeHandleElementIndex"], "mappings": ";;;;;;AAQO,SAAS,sCAAuC,CAAA;AAAA,EACrD,QAAA;AAAA,EACA,QAAA;AAAA,EACA,aAAA;AAAA,EACA;AACF,CAKS,EAAA;AACP,EAAAA,eAAA,CAAY,CAAC,SAAc,KAAA;AACzB,IAAA,MAAM,qBAAqB,iBAAkB,CAAA,KAAA;AAC7C,IAAA,IAAI,QAAS,CAAA,KAAA,IAAS,aAAc,CAAA,KAAA,KAAU,QAAQ,kBAAuB,KAAA,IAAA;AAC3E,MAAA;AAEF,IAAM,MAAA,aAAA,GAAgBC,gCAAuB,CAAA,QAAA,EAAU,kBAAkB,CAAA;AACzE,IAAA,IAAI,aAAiB,IAAA,IAAA;AACnB,MAAA;AAEF,IAAM,MAAA,SAAA,GAAY,CAAC,KAAyB,KAAA;AAC1C,MAAA,IAAI,KAAM,CAAA,gBAAA;AACR,QAAA;AAEF,MAAA,QAAQ,MAAM,GAAK;AAAA,QACjB,KAAK,WAAA;AAAA,QACL,KAAK,WAAA;AAAA,QACL,KAAK,YAAA;AAAA,QACL,KAAK,SAAA;AAAA,QACL,KAAK,KAAA;AAAA,QACL,KAAK,MAAQ,EAAA;AACX,UAAA,KAAA,CAAM,cAAe,EAAA;AAErB,UAAA,aAAA,CAAc,QAAQ,KAAK,CAAA;AAC3B,UAAA;AAAA;AACF,QACA,KAAK,IAAM,EAAA;AACT,UAAA,KAAA,CAAM,cAAe,EAAA;AAErB,UAAM,MAAA,OAAA,GAAU,aAAc,CAAA,YAAA,CAAa,qBAAqB,CAAA;AAChE,UAAAC,mBAAA,CAAO,OAAO,CAAA;AAEd,UAAA,MAAM,OAAU,GAAAC,yCAAA;AAAA,YACd,OAAA;AAAA,YACA;AAAA,WACF;AACA,UAAA,MAAM,KAAQ,GAAAC,qCAAA;AAAA,YACZ,OAAA;AAAA,YACA,QAAA;AAAA,YACA;AAAA,WACF;AAEA,UAAAF,mBAAA,CAAO,UAAU,IAAI,CAAA;AAErB,UAAA,MAAM,SAAY,GAAA,KAAA,CAAM,QACpB,GAAA,KAAA,GAAQ,IACN,KAAQ,GAAA,CAAA,GACR,OAAQ,CAAA,MAAA,GAAS,IACnB,KAAQ,GAAA,CAAA,GAAI,OAAQ,CAAA,MAAA,GAClB,QAAQ,CACR,GAAA,CAAA;AAEN,UAAM,MAAA,UAAA,GAAa,QAAQ,SAAS,CAAA;AACpC,UAAA,UAAA,CAAW,KAAM,EAAA;AAEjB,UAAA;AAAA;AACF;AACF,KACF;AAEA,IAAc,aAAA,CAAA,gBAAA,CAAiB,WAAW,SAAS,CAAA;AACnD,IAAA,SAAA,CAAU,MAAM;AACd,MAAc,aAAA,CAAA,mBAAA,CAAoB,WAAW,SAAS,CAAA;AAAA,KACvD,CAAA;AAAA,GACF,CAAA;AACH;;;;"}