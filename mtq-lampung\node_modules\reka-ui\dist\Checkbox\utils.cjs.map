{"version": 3, "file": "utils.cjs", "sources": ["../../src/Checkbox/utils.ts"], "sourcesContent": ["export type CheckedState = boolean | 'indeterminate'\n\nexport function isIndeterminate(checked?: CheckedState): checked is 'indeterminate' {\n  return checked === 'indeterminate'\n}\n\nexport function getState(checked: CheckedState) {\n  return isIndeterminate(checked) ? 'indeterminate' : checked ? 'checked' : 'unchecked'\n}\n"], "names": [], "mappings": ";;AAEO,SAAS,gBAAgB,OAAoD,EAAA;AAClF,EAAA,OAAO,OAAY,KAAA,eAAA;AACrB;AAEO,SAAS,SAAS,OAAuB,EAAA;AAC9C,EAAA,OAAO,eAAgB,CAAA,OAAO,CAAI,GAAA,eAAA,GAAkB,UAAU,SAAY,GAAA,WAAA;AAC5E;;;;;"}