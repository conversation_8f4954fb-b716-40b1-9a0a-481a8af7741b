{"version": 3, "file": "ToastClose.js", "sources": ["../../src/Toast/ToastClose.vue"], "sourcesContent": ["<script lang=\"ts\">\nimport type { PrimitiveProps } from '@/Primitive'\nimport { useForwardExpose } from '@/shared'\n\nexport interface ToastCloseProps extends PrimitiveProps {}\n</script>\n\n<script setup lang=\"ts\">\nimport { Primitive } from '@/Primitive'\nimport ToastAnnounceExclude from './ToastAnnounceExclude.vue'\nimport { injectToastRootContext } from './ToastRootImpl.vue'\n\nconst props = withDefaults(defineProps<ToastCloseProps>(), {\n  as: 'button',\n})\n\nconst rootContext = injectToastRootContext()\nconst { forwardRef } = useForwardExpose()\n</script>\n\n<template>\n  <ToastAnnounceExclude as-child>\n    <Primitive\n      v-bind=\"props\"\n      :ref=\"forwardRef\"\n      :type=\"as === 'button' ? 'button' : undefined \"\n      @click=\"rootContext.onClose\"\n    >\n      <slot />\n    </Primitive>\n  </ToastAnnounceExclude>\n</template>\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAYA,IAAA,MAAM,KAAQ,GAAA,OAAA;AAId,IAAA,MAAM,cAAc,sBAAuB,EAAA;AAC3C,IAAM,MAAA,EAAE,UAAW,EAAA,GAAI,gBAAiB,EAAA;;;;;;;;;;;;;;;;;;;;;;;"}