{"version": 3, "file": "useGraceArea.js", "sources": ["../../src/shared/useGraceArea.ts"], "sourcesContent": ["import type { Ref } from 'vue'\nimport type { Side } from '@/Popper/utils'\nimport { createEventHook, refAutoReset } from '@vueuse/shared'\nimport { ref, watchEffect } from 'vue'\n\nexport function useGraceArea(triggerElement: Ref<HTMLElement | undefined>, containerElement: Ref<HTMLElement | undefined>) {\n// Reset the inTransit state if idle/scrolled.\n  const isPointerInTransit = refAutoReset(false, 300)\n\n  const pointerGraceArea = ref<Polygon | null>(null)\n  const pointerExit = createEventHook<void>()\n\n  function handleRemoveGraceArea() {\n    pointerGraceArea.value = null\n    isPointerInTransit.value = false\n  }\n\n  function handleCreateGraceArea(event: PointerEvent, hoverTarget: HTMLElement) {\n    const currentTarget = event.currentTarget as HTMLElement\n    const exitPoint = { x: event.clientX, y: event.clientY }\n    const exitSide = getExitSideFromRect(exitPoint, currentTarget.getBoundingClientRect())\n    const paddedExitPoints = getPaddedExitPoints(exitPoint, exitSide)\n    const hoverTargetPoints = getPointsFromRect(hoverTarget.getBoundingClientRect())\n    const graceArea = getHull([...paddedExitPoints, ...hoverTargetPoints])\n    pointerGraceArea.value = graceArea\n    isPointerInTransit.value = true\n  }\n\n  watchEffect((cleanupFn) => {\n    if (triggerElement.value && containerElement.value) {\n      const handleTriggerLeave = (event: PointerEvent) => handleCreateGraceArea(event, containerElement.value!)\n      const handleContentLeave = (event: PointerEvent) => handleCreateGraceArea(event, triggerElement.value!)\n\n      triggerElement.value.addEventListener('pointerleave', handleTriggerLeave)\n      containerElement.value.addEventListener('pointerleave', handleContentLeave)\n\n      cleanupFn(() => {\n        triggerElement.value?.removeEventListener('pointerleave', handleTriggerLeave)\n        containerElement.value?.removeEventListener('pointerleave', handleContentLeave)\n      })\n    }\n  })\n\n  watchEffect((cleanupFn) => {\n    if (pointerGraceArea.value) {\n      const handleTrackPointerGrace = (event: PointerEvent) => {\n        if (!pointerGraceArea.value || !(event.target instanceof HTMLElement))\n          return\n\n        const target = event.target\n        const pointerPosition = { x: event.clientX, y: event.clientY }\n        const hasEnteredTarget = triggerElement.value?.contains(target) || containerElement.value?.contains(target)\n        const isPointerOutsideGraceArea = !isPointInPolygon(pointerPosition, pointerGraceArea.value)\n        const isAnotherGraceAreaTrigger = !!target.closest('[data-grace-area-trigger]')\n\n        if (hasEnteredTarget) {\n          handleRemoveGraceArea()\n        }\n        else if (isPointerOutsideGraceArea || isAnotherGraceAreaTrigger) {\n          handleRemoveGraceArea()\n          pointerExit.trigger()\n        }\n      }\n      triggerElement.value?.ownerDocument.addEventListener('pointermove', handleTrackPointerGrace)\n\n      cleanupFn(() => triggerElement.value?.ownerDocument.removeEventListener('pointermove', handleTrackPointerGrace))\n    }\n  })\n\n  return {\n    isPointerInTransit,\n    onPointerExit: pointerExit.on,\n  }\n}\n\ninterface Point { x: number, y: number }\n  type Polygon = Point[]\n\nfunction getExitSideFromRect(point: Point, rect: DOMRect): Side {\n  const top = Math.abs(rect.top - point.y)\n  const bottom = Math.abs(rect.bottom - point.y)\n  const right = Math.abs(rect.right - point.x)\n  const left = Math.abs(rect.left - point.x)\n\n  switch (Math.min(top, bottom, right, left)) {\n    case left:\n      return 'left'\n    case right:\n      return 'right'\n    case top:\n      return 'top'\n    case bottom:\n      return 'bottom'\n    default:\n      throw new Error('unreachable')\n  }\n}\n\nfunction getPaddedExitPoints(exitPoint: Point, exitSide: Side, padding = 5) {\n  const paddedExitPoints: Point[] = []\n  switch (exitSide) {\n    case 'top':\n      paddedExitPoints.push(\n        { x: exitPoint.x - padding, y: exitPoint.y + padding },\n        { x: exitPoint.x + padding, y: exitPoint.y + padding },\n      )\n      break\n    case 'bottom':\n      paddedExitPoints.push(\n        { x: exitPoint.x - padding, y: exitPoint.y - padding },\n        { x: exitPoint.x + padding, y: exitPoint.y - padding },\n      )\n      break\n    case 'left':\n      paddedExitPoints.push(\n        { x: exitPoint.x + padding, y: exitPoint.y - padding },\n        { x: exitPoint.x + padding, y: exitPoint.y + padding },\n      )\n      break\n    case 'right':\n      paddedExitPoints.push(\n        { x: exitPoint.x - padding, y: exitPoint.y - padding },\n        { x: exitPoint.x - padding, y: exitPoint.y + padding },\n      )\n      break\n  }\n  return paddedExitPoints\n}\n\nfunction getPointsFromRect(rect: DOMRect) {\n  const { top, right, bottom, left } = rect\n  return [\n    { x: left, y: top },\n    { x: right, y: top },\n    { x: right, y: bottom },\n    { x: left, y: bottom },\n  ]\n}\n\n// Determine if a point is inside of a polygon.\n// Based on https://github.com/substack/point-in-polygon\nfunction isPointInPolygon(point: Point, polygon: Polygon) {\n  const { x, y } = point\n  let inside = false\n  for (let i = 0, j = polygon.length - 1; i < polygon.length; j = i++) {\n    const xi = polygon[i].x\n    const yi = polygon[i].y\n    const xj = polygon[j].x\n    const yj = polygon[j].y\n\n    // prettier-ignore\n    const intersect = ((yi > y) !== (yj > y)) && (x < (xj - xi) * (y - yi) / (yj - yi) + xi)\n    if (intersect)\n      inside = !inside\n  }\n\n  return inside\n}\n\n// Returns a new array of points representing the convex hull of the given set of points.\n// https://www.nayuki.io/page/convex-hull-algorithm\nfunction getHull<P extends Point>(points: Readonly<Array<P>>): Array<P> {\n  const newPoints: Array<P> = points.slice()\n  newPoints.sort((a: Point, b: Point) => {\n    if (a.x < b.x)\n      return -1\n    else if (a.x > b.x)\n      return +1\n    else if (a.y < b.y)\n      return -1\n    else if (a.y > b.y)\n      return +1\n    else return 0\n  })\n  return getHullPresorted(newPoints)\n}\n\n// Returns the convex hull, assuming that each points[i] <= points[i + 1]. Runs in O(n) time.\nfunction getHullPresorted<P extends Point>(points: Readonly<Array<P>>): Array<P> {\n  if (points.length <= 1)\n    return points.slice()\n\n  const upperHull: Array<P> = []\n  for (let i = 0; i < points.length; i++) {\n    const p = points[i]\n    while (upperHull.length >= 2) {\n      const q = upperHull[upperHull.length - 1]\n      const r = upperHull[upperHull.length - 2]\n      if ((q.x - r.x) * (p.y - r.y) >= (q.y - r.y) * (p.x - r.x))\n        upperHull.pop()\n      else break\n    }\n    upperHull.push(p)\n  }\n  upperHull.pop()\n\n  const lowerHull: Array<P> = []\n  for (let i = points.length - 1; i >= 0; i--) {\n    const p = points[i]\n    while (lowerHull.length >= 2) {\n      const q = lowerHull[lowerHull.length - 1]\n      const r = lowerHull[lowerHull.length - 2]\n      if ((q.x - r.x) * (p.y - r.y) >= (q.y - r.y) * (p.x - r.x))\n        lowerHull.pop()\n      else break\n    }\n    lowerHull.push(p)\n  }\n  lowerHull.pop()\n\n  if (\n    upperHull.length === 1\n    && lowerHull.length === 1\n    && upperHull[0].x === lowerHull[0].x\n    && upperHull[0].y === lowerHull[0].y\n  ) {\n    return upperHull\n  }\n\n  else {\n    return upperHull.concat(lowerHull)\n  }\n}\n"], "names": [], "mappings": ";;;AAKgB,SAAA,YAAA,CAAa,gBAA8C,gBAAgD,EAAA;AAEzH,EAAM,MAAA,kBAAA,GAAqB,YAAa,CAAA,KAAA,EAAO,GAAG,CAAA;AAElD,EAAM,MAAA,gBAAA,GAAmB,IAAoB,IAAI,CAAA;AACjD,EAAA,MAAM,cAAc,eAAsB,EAAA;AAE1C,EAAA,SAAS,qBAAwB,GAAA;AAC/B,IAAA,gBAAA,CAAiB,KAAQ,GAAA,IAAA;AACzB,IAAA,kBAAA,CAAmB,KAAQ,GAAA,KAAA;AAAA;AAG7B,EAAS,SAAA,qBAAA,CAAsB,OAAqB,WAA0B,EAAA;AAC5E,IAAA,MAAM,gBAAgB,KAAM,CAAA,aAAA;AAC5B,IAAA,MAAM,YAAY,EAAE,CAAA,EAAG,MAAM,OAAS,EAAA,CAAA,EAAG,MAAM,OAAQ,EAAA;AACvD,IAAA,MAAM,QAAW,GAAA,mBAAA,CAAoB,SAAW,EAAA,aAAA,CAAc,uBAAuB,CAAA;AACrF,IAAM,MAAA,gBAAA,GAAmB,mBAAoB,CAAA,SAAA,EAAW,QAAQ,CAAA;AAChE,IAAA,MAAM,iBAAoB,GAAA,iBAAA,CAAkB,WAAY,CAAA,qBAAA,EAAuB,CAAA;AAC/E,IAAA,MAAM,YAAY,OAAQ,CAAA,CAAC,GAAG,gBAAkB,EAAA,GAAG,iBAAiB,CAAC,CAAA;AACrE,IAAA,gBAAA,CAAiB,KAAQ,GAAA,SAAA;AACzB,IAAA,kBAAA,CAAmB,KAAQ,GAAA,IAAA;AAAA;AAG7B,EAAA,WAAA,CAAY,CAAC,SAAc,KAAA;AACzB,IAAI,IAAA,cAAA,CAAe,KAAS,IAAA,gBAAA,CAAiB,KAAO,EAAA;AAClD,MAAA,MAAM,qBAAqB,CAAC,KAAA,KAAwB,qBAAsB,CAAA,KAAA,EAAO,iBAAiB,KAAM,CAAA;AACxG,MAAA,MAAM,qBAAqB,CAAC,KAAA,KAAwB,qBAAsB,CAAA,KAAA,EAAO,eAAe,KAAM,CAAA;AAEtG,MAAe,cAAA,CAAA,KAAA,CAAM,gBAAiB,CAAA,cAAA,EAAgB,kBAAkB,CAAA;AACxE,MAAiB,gBAAA,CAAA,KAAA,CAAM,gBAAiB,CAAA,cAAA,EAAgB,kBAAkB,CAAA;AAE1E,MAAA,SAAA,CAAU,MAAM;AACd,QAAe,cAAA,CAAA,KAAA,EAAO,mBAAoB,CAAA,cAAA,EAAgB,kBAAkB,CAAA;AAC5E,QAAiB,gBAAA,CAAA,KAAA,EAAO,mBAAoB,CAAA,cAAA,EAAgB,kBAAkB,CAAA;AAAA,OAC/E,CAAA;AAAA;AACH,GACD,CAAA;AAED,EAAA,WAAA,CAAY,CAAC,SAAc,KAAA;AACzB,IAAA,IAAI,iBAAiB,KAAO,EAAA;AAC1B,MAAM,MAAA,uBAAA,GAA0B,CAAC,KAAwB,KAAA;AACvD,QAAA,IAAI,CAAC,gBAAA,CAAiB,KAAS,IAAA,EAAE,MAAM,MAAkB,YAAA,WAAA,CAAA;AACvD,UAAA;AAEF,QAAA,MAAM,SAAS,KAAM,CAAA,MAAA;AACrB,QAAA,MAAM,kBAAkB,EAAE,CAAA,EAAG,MAAM,OAAS,EAAA,CAAA,EAAG,MAAM,OAAQ,EAAA;AAC7D,QAAM,MAAA,gBAAA,GAAmB,eAAe,KAAO,EAAA,QAAA,CAAS,MAAM,CAAK,IAAA,gBAAA,CAAiB,KAAO,EAAA,QAAA,CAAS,MAAM,CAAA;AAC1G,QAAA,MAAM,yBAA4B,GAAA,CAAC,gBAAiB,CAAA,eAAA,EAAiB,iBAAiB,KAAK,CAAA;AAC3F,QAAA,MAAM,yBAA4B,GAAA,CAAC,CAAC,MAAA,CAAO,QAAQ,2BAA2B,CAAA;AAE9E,QAAA,IAAI,gBAAkB,EAAA;AACpB,UAAsB,qBAAA,EAAA;AAAA,SACxB,MAAA,IACS,6BAA6B,yBAA2B,EAAA;AAC/D,UAAsB,qBAAA,EAAA;AACtB,UAAA,WAAA,CAAY,OAAQ,EAAA;AAAA;AACtB,OACF;AACA,MAAA,cAAA,CAAe,KAAO,EAAA,aAAA,CAAc,gBAAiB,CAAA,aAAA,EAAe,uBAAuB,CAAA;AAE3F,MAAA,SAAA,CAAU,MAAM,cAAe,CAAA,KAAA,EAAO,cAAc,mBAAoB,CAAA,aAAA,EAAe,uBAAuB,CAAC,CAAA;AAAA;AACjH,GACD,CAAA;AAED,EAAO,OAAA;AAAA,IACL,kBAAA;AAAA,IACA,eAAe,WAAY,CAAA;AAAA,GAC7B;AACF;AAKA,SAAS,mBAAA,CAAoB,OAAc,IAAqB,EAAA;AAC9D,EAAA,MAAM,MAAM,IAAK,CAAA,GAAA,CAAI,IAAK,CAAA,GAAA,GAAM,MAAM,CAAC,CAAA;AACvC,EAAA,MAAM,SAAS,IAAK,CAAA,GAAA,CAAI,IAAK,CAAA,MAAA,GAAS,MAAM,CAAC,CAAA;AAC7C,EAAA,MAAM,QAAQ,IAAK,CAAA,GAAA,CAAI,IAAK,CAAA,KAAA,GAAQ,MAAM,CAAC,CAAA;AAC3C,EAAA,MAAM,OAAO,IAAK,CAAA,GAAA,CAAI,IAAK,CAAA,IAAA,GAAO,MAAM,CAAC,CAAA;AAEzC,EAAA,QAAQ,KAAK,GAAI,CAAA,GAAA,EAAK,MAAQ,EAAA,KAAA,EAAO,IAAI,CAAG;AAAA,IAC1C,KAAK,IAAA;AACH,MAAO,OAAA,MAAA;AAAA,IACT,KAAK,KAAA;AACH,MAAO,OAAA,OAAA;AAAA,IACT,KAAK,GAAA;AACH,MAAO,OAAA,KAAA;AAAA,IACT,KAAK,MAAA;AACH,MAAO,OAAA,QAAA;AAAA,IACT;AACE,MAAM,MAAA,IAAI,MAAM,aAAa,CAAA;AAAA;AAEnC;AAEA,SAAS,mBAAoB,CAAA,SAAA,EAAkB,QAAgB,EAAA,OAAA,GAAU,CAAG,EAAA;AAC1E,EAAA,MAAM,mBAA4B,EAAC;AACnC,EAAA,QAAQ,QAAU;AAAA,IAChB,KAAK,KAAA;AACH,MAAiB,gBAAA,CAAA,IAAA;AAAA,QACf,EAAE,GAAG,SAAU,CAAA,CAAA,GAAI,SAAS,CAAG,EAAA,SAAA,CAAU,IAAI,OAAQ,EAAA;AAAA,QACrD,EAAE,GAAG,SAAU,CAAA,CAAA,GAAI,SAAS,CAAG,EAAA,SAAA,CAAU,IAAI,OAAQ;AAAA,OACvD;AACA,MAAA;AAAA,IACF,KAAK,QAAA;AACH,MAAiB,gBAAA,CAAA,IAAA;AAAA,QACf,EAAE,GAAG,SAAU,CAAA,CAAA,GAAI,SAAS,CAAG,EAAA,SAAA,CAAU,IAAI,OAAQ,EAAA;AAAA,QACrD,EAAE,GAAG,SAAU,CAAA,CAAA,GAAI,SAAS,CAAG,EAAA,SAAA,CAAU,IAAI,OAAQ;AAAA,OACvD;AACA,MAAA;AAAA,IACF,KAAK,MAAA;AACH,MAAiB,gBAAA,CAAA,IAAA;AAAA,QACf,EAAE,GAAG,SAAU,CAAA,CAAA,GAAI,SAAS,CAAG,EAAA,SAAA,CAAU,IAAI,OAAQ,EAAA;AAAA,QACrD,EAAE,GAAG,SAAU,CAAA,CAAA,GAAI,SAAS,CAAG,EAAA,SAAA,CAAU,IAAI,OAAQ;AAAA,OACvD;AACA,MAAA;AAAA,IACF,KAAK,OAAA;AACH,MAAiB,gBAAA,CAAA,IAAA;AAAA,QACf,EAAE,GAAG,SAAU,CAAA,CAAA,GAAI,SAAS,CAAG,EAAA,SAAA,CAAU,IAAI,OAAQ,EAAA;AAAA,QACrD,EAAE,GAAG,SAAU,CAAA,CAAA,GAAI,SAAS,CAAG,EAAA,SAAA,CAAU,IAAI,OAAQ;AAAA,OACvD;AACA,MAAA;AAAA;AAEJ,EAAO,OAAA,gBAAA;AACT;AAEA,SAAS,kBAAkB,IAAe,EAAA;AACxC,EAAA,MAAM,EAAE,GAAA,EAAK,KAAO,EAAA,MAAA,EAAQ,MAAS,GAAA,IAAA;AACrC,EAAO,OAAA;AAAA,IACL,EAAE,CAAA,EAAG,IAAM,EAAA,CAAA,EAAG,GAAI,EAAA;AAAA,IAClB,EAAE,CAAA,EAAG,KAAO,EAAA,CAAA,EAAG,GAAI,EAAA;AAAA,IACnB,EAAE,CAAA,EAAG,KAAO,EAAA,CAAA,EAAG,MAAO,EAAA;AAAA,IACtB,EAAE,CAAA,EAAG,IAAM,EAAA,CAAA,EAAG,MAAO;AAAA,GACvB;AACF;AAIA,SAAS,gBAAA,CAAiB,OAAc,OAAkB,EAAA;AACxD,EAAM,MAAA,EAAE,CAAG,EAAA,CAAA,EAAM,GAAA,KAAA;AACjB,EAAA,IAAI,MAAS,GAAA,KAAA;AACb,EAAS,KAAA,IAAA,CAAA,GAAI,CAAG,EAAA,CAAA,GAAI,OAAQ,CAAA,MAAA,GAAS,GAAG,CAAI,GAAA,OAAA,CAAQ,MAAQ,EAAA,CAAA,GAAI,CAAK,EAAA,EAAA;AACnE,IAAM,MAAA,EAAA,GAAK,OAAQ,CAAA,CAAC,CAAE,CAAA,CAAA;AACtB,IAAM,MAAA,EAAA,GAAK,OAAQ,CAAA,CAAC,CAAE,CAAA,CAAA;AACtB,IAAM,MAAA,EAAA,GAAK,OAAQ,CAAA,CAAC,CAAE,CAAA,CAAA;AACtB,IAAM,MAAA,EAAA,GAAK,OAAQ,CAAA,CAAC,CAAE,CAAA,CAAA;AAGtB,IAAM,MAAA,SAAA,GAAc,EAAK,GAAA,CAAA,KAAQ,EAAK,GAAA,CAAA,IAAQ,CAAK,GAAA,CAAA,EAAA,GAAK,EAAO,KAAA,CAAA,GAAI,EAAO,CAAA,IAAA,EAAA,GAAK,EAAM,CAAA,GAAA,EAAA;AACrF,IAAI,IAAA,SAAA;AACF,MAAA,MAAA,GAAS,CAAC,MAAA;AAAA;AAGd,EAAO,OAAA,MAAA;AACT;AAIA,SAAS,QAAyB,MAAsC,EAAA;AACtE,EAAM,MAAA,SAAA,GAAsB,OAAO,KAAM,EAAA;AACzC,EAAU,SAAA,CAAA,IAAA,CAAK,CAAC,CAAA,EAAU,CAAa,KAAA;AACrC,IAAI,IAAA,CAAA,CAAE,IAAI,CAAE,CAAA,CAAA;AACV,MAAO,OAAA,EAAA;AAAA,SACA,IAAA,CAAA,CAAE,IAAI,CAAE,CAAA,CAAA;AACf,MAAO,OAAA,CAAA;AAAA,SACA,IAAA,CAAA,CAAE,IAAI,CAAE,CAAA,CAAA;AACf,MAAO,OAAA,EAAA;AAAA,SACA,IAAA,CAAA,CAAE,IAAI,CAAE,CAAA,CAAA;AACf,MAAO,OAAA,CAAA;AAAA,SACG,OAAA,CAAA;AAAA,GACb,CAAA;AACD,EAAA,OAAO,iBAAiB,SAAS,CAAA;AACnC;AAGA,SAAS,iBAAkC,MAAsC,EAAA;AAC/E,EAAA,IAAI,OAAO,MAAU,IAAA,CAAA;AACnB,IAAA,OAAO,OAAO,KAAM,EAAA;AAEtB,EAAA,MAAM,YAAsB,EAAC;AAC7B,EAAA,KAAA,IAAS,CAAI,GAAA,CAAA,EAAG,CAAI,GAAA,MAAA,CAAO,QAAQ,CAAK,EAAA,EAAA;AACtC,IAAM,MAAA,CAAA,GAAI,OAAO,CAAC,CAAA;AAClB,IAAO,OAAA,SAAA,CAAU,UAAU,CAAG,EAAA;AAC5B,MAAA,MAAM,CAAI,GAAA,SAAA,CAAU,SAAU,CAAA,MAAA,GAAS,CAAC,CAAA;AACxC,MAAA,MAAM,CAAI,GAAA,SAAA,CAAU,SAAU,CAAA,MAAA,GAAS,CAAC,CAAA;AACxC,MAAA,IAAA,CAAK,CAAE,CAAA,CAAA,GAAI,CAAE,CAAA,CAAA,KAAM,EAAE,CAAI,GAAA,CAAA,CAAE,CAAO,CAAA,IAAA,CAAA,CAAA,CAAE,CAAI,GAAA,CAAA,CAAE,CAAM,KAAA,CAAA,CAAE,IAAI,CAAE,CAAA,CAAA,CAAA;AACtD,QAAA,SAAA,CAAU,GAAI,EAAA;AAAA,WACX;AAAA;AAEP,IAAA,SAAA,CAAU,KAAK,CAAC,CAAA;AAAA;AAElB,EAAA,SAAA,CAAU,GAAI,EAAA;AAEd,EAAA,MAAM,YAAsB,EAAC;AAC7B,EAAA,KAAA,IAAS,IAAI,MAAO,CAAA,MAAA,GAAS,CAAG,EAAA,CAAA,IAAK,GAAG,CAAK,EAAA,EAAA;AAC3C,IAAM,MAAA,CAAA,GAAI,OAAO,CAAC,CAAA;AAClB,IAAO,OAAA,SAAA,CAAU,UAAU,CAAG,EAAA;AAC5B,MAAA,MAAM,CAAI,GAAA,SAAA,CAAU,SAAU,CAAA,MAAA,GAAS,CAAC,CAAA;AACxC,MAAA,MAAM,CAAI,GAAA,SAAA,CAAU,SAAU,CAAA,MAAA,GAAS,CAAC,CAAA;AACxC,MAAA,IAAA,CAAK,CAAE,CAAA,CAAA,GAAI,CAAE,CAAA,CAAA,KAAM,EAAE,CAAI,GAAA,CAAA,CAAE,CAAO,CAAA,IAAA,CAAA,CAAA,CAAE,CAAI,GAAA,CAAA,CAAE,CAAM,KAAA,CAAA,CAAE,IAAI,CAAE,CAAA,CAAA,CAAA;AACtD,QAAA,SAAA,CAAU,GAAI,EAAA;AAAA,WACX;AAAA;AAEP,IAAA,SAAA,CAAU,KAAK,CAAC,CAAA;AAAA;AAElB,EAAA,SAAA,CAAU,GAAI,EAAA;AAEd,EACE,IAAA,SAAA,CAAU,WAAW,CAClB,IAAA,SAAA,CAAU,WAAW,CACrB,IAAA,SAAA,CAAU,CAAC,CAAE,CAAA,CAAA,KAAM,UAAU,CAAC,CAAA,CAAE,KAChC,SAAU,CAAA,CAAC,EAAE,CAAM,KAAA,SAAA,CAAU,CAAC,CAAA,CAAE,CACnC,EAAA;AACA,IAAO,OAAA,SAAA;AAAA,GAGJ,MAAA;AACH,IAAO,OAAA,SAAA,CAAU,OAAO,SAAS,CAAA;AAAA;AAErC;;;;"}