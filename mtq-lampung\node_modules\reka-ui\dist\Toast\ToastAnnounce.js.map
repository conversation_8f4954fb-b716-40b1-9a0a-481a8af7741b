{"version": 3, "file": "ToastAnnounce.js", "sources": ["../../src/Toast/ToastAnnounce.vue"], "sourcesContent": ["<script setup lang=\"ts\">\nimport { useRafFn } from '@vueuse/core'\nimport { useTimeout } from '@vueuse/shared'\nimport { ref } from 'vue'\nimport { VisuallyHidden } from '@/VisuallyHidden'\nimport { injectToastProviderContext } from './ToastProvider.vue'\n\nconst providerContext = injectToastProviderContext()\n\nconst isAnnounced = useTimeout(1000)\nconst renderAnnounceText = ref(false)\n\nuseRafFn(() => {\n  renderAnnounceText.value = true\n})\n</script>\n\n<template>\n  <VisuallyHidden v-if=\"isAnnounced || renderAnnounceText\">\n    {{ providerContext.label.value }}\n    <slot />\n  </VisuallyHidden>\n</template>\n"], "names": [], "mappings": ";;;;;;;;;AAOA,IAAA,MAAM,kBAAkB,0BAA2B,EAAA;AAEnD,IAAM,MAAA,WAAA,GAAc,WAAW,GAAI,CAAA;AACnC,IAAM,MAAA,kBAAA,GAAqB,IAAI,KAAK,CAAA;AAEpC,IAAA,QAAA,CAAS,MAAM;AACb,MAAA,kBAAA,CAAmB,KAAQ,GAAA,IAAA;AAAA,KAC5B,CAAA;;;;;;;;;;;;;;;"}