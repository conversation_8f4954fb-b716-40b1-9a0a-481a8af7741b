{"version": 3, "file": "useDateFormatter.js", "sources": ["../../src/shared/useDateFormatter.ts"], "sourcesContent": ["/*\n  * Implementation ported from https://github.com/melt-ui/melt-ui/blob/develop/src/lib/internal/helpers/date/formatter.ts\n*/\n\nimport type { DateValue, ZonedDateTime } from '@internationalized/date'\nimport { DateFormatter, getLocalTimeZone, today } from '@internationalized/date'\nimport { ref } from 'vue'\nimport { hasTime, isZonedDateTime, toDate } from '@/date'\n\nexport interface DateFormatterOptions extends Intl.DateTimeFormatOptions {\n  calendar?: string\n}\n\nexport type Formatter = {\n  getLocale: () => string\n  setLocale: (newLocale: string) => void\n  custom: (date: Date, options: DateFormatterOptions) => string\n  selectedDate: (date: DateValue, includeTime?: boolean) => string\n  dayOfWeek: (date: Date, length?: DateFormatterOptions['weekday']) => string\n  fullMonthAndYear: (date: Date, options?: DateFormatterOptions) => string\n  fullMonth: (date: Date, options?: DateFormatterOptions) => string\n  fullYear: (date: Date, options?: DateFormatterOptions) => string\n  dayPeriod: (date: Date) => string\n  part: (dateObj: DateValue, type: Intl.DateTimeFormatPartTypes, options?: DateFormatterOptions) => string\n  toParts: (date: DateValue, options?: DateFormatterOptions) => Intl.DateTimeFormatPart[]\n  getMonths: () => { label: string, value: number }[]\n}\n\n/**\n * Creates a wrapper around the `DateFormatter`, which is\n * an improved version of the {@link Intl.DateTimeFormat} API,\n * that is used internally by the various date builders to\n * easily format dates in a consistent way.\n *\n * @see [DateFormatter](https://react-spectrum.adobe.com/internationalized/date/DateFormatter.html)\n */\nexport function useDateFormatter(initialLocale: string, opts: DateFormatterOptions = {}): Formatter {\n  const locale = ref(initialLocale)\n\n  function getLocale() {\n    return locale.value\n  }\n\n  function setLocale(newLocale: string) {\n    locale.value = newLocale\n  }\n\n  function custom(date: Date, options: DateFormatterOptions) {\n    return new DateFormatter(locale.value, { ...opts, ...options }).format(date)\n  }\n\n  function selectedDate(date: DateValue, includeTime = true) {\n    if (hasTime(date) && includeTime) {\n      return custom(toDate(date), {\n        dateStyle: 'long',\n        timeStyle: 'long',\n      })\n    }\n    else {\n      return custom(toDate(date), {\n        dateStyle: 'long',\n      })\n    }\n  }\n\n  function fullMonthAndYear(date: Date, options: DateFormatterOptions = {}) {\n    return new DateFormatter(locale.value, { ...opts, month: 'long', year: 'numeric', ...options }).format(date)\n  }\n\n  function fullMonth(date: Date, options: DateFormatterOptions = {}) {\n    return new DateFormatter(locale.value, { ...opts, month: 'long', ...options }).format(date)\n  }\n\n  function getMonths() {\n    const defaultDate = today(getLocalTimeZone())\n    const months = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]\n    return months.map(item => ({ label: fullMonth(toDate(defaultDate.set({ month: item }))), value: item }))\n  }\n\n  function fullYear(date: Date, options: DateFormatterOptions = {}) {\n    return new DateFormatter(locale.value, { ...opts, year: 'numeric', ...options }).format(date)\n  }\n\n  function toParts(date: DateValue, options?: DateFormatterOptions) {\n    if (isZonedDateTime(date)) {\n      return new DateFormatter(locale.value, {\n        ...opts,\n        ...options,\n        timeZone: (date as ZonedDateTime).timeZone,\n      }).formatToParts(toDate(date))\n    }\n    else {\n      return new DateFormatter(locale.value, { ...opts, ...options }).formatToParts(toDate(date))\n    }\n  }\n\n  function dayOfWeek(date: Date, length: DateFormatterOptions['weekday'] = 'narrow') {\n    return new DateFormatter(locale.value, { ...opts, weekday: length }).format(date)\n  }\n\n  function dayPeriod(date: Date) {\n    const parts = new DateFormatter(locale.value, {\n      ...opts,\n      hour: 'numeric',\n      minute: 'numeric',\n    }).formatToParts(date)\n    const value = parts.find(p => p.type === 'dayPeriod')?.value\n    if (value === 'PM')\n      return 'PM'\n\n    return 'AM'\n  }\n\n  const defaultPartOptions: DateFormatterOptions = {\n    year: 'numeric',\n    month: 'numeric',\n    day: 'numeric',\n    hour: 'numeric',\n    minute: 'numeric',\n    second: 'numeric',\n  }\n\n  function part(\n    dateObj: DateValue,\n    type: Intl.DateTimeFormatPartTypes,\n    options: DateFormatterOptions = {},\n  ) {\n    const opts = { ...defaultPartOptions, ...options }\n    const parts = toParts(dateObj, opts)\n    const part = parts.find(p => p.type === type)\n    return part ? part.value : ''\n  }\n\n  return {\n    setLocale,\n    getLocale,\n    fullMonth,\n    fullYear,\n    fullMonthAndYear,\n    toParts,\n    custom,\n    part,\n    dayPeriod,\n    selectedDate,\n    dayOfWeek,\n    getMonths,\n  }\n}\n"], "names": ["opts", "part"], "mappings": ";;;;AAoCO,SAAS,gBAAiB,CAAA,aAAA,EAAuB,IAA6B,GAAA,EAAe,EAAA;AAClG,EAAM,MAAA,MAAA,GAAS,IAAI,aAAa,CAAA;AAEhC,EAAA,SAAS,SAAY,GAAA;AACnB,IAAA,OAAO,MAAO,CAAA,KAAA;AAAA;AAGhB,EAAA,SAAS,UAAU,SAAmB,EAAA;AACpC,IAAA,MAAA,CAAO,KAAQ,GAAA,SAAA;AAAA;AAGjB,EAAS,SAAA,MAAA,CAAO,MAAY,OAA+B,EAAA;AACzD,IAAA,OAAO,IAAI,aAAA,CAAc,MAAO,CAAA,KAAA,EAAO,EAAE,GAAG,IAAM,EAAA,GAAG,OAAQ,EAAC,CAAE,CAAA,MAAA,CAAO,IAAI,CAAA;AAAA;AAG7E,EAAS,SAAA,YAAA,CAAa,IAAiB,EAAA,WAAA,GAAc,IAAM,EAAA;AACzD,IAAI,IAAA,OAAA,CAAQ,IAAI,CAAA,IAAK,WAAa,EAAA;AAChC,MAAO,OAAA,MAAA,CAAO,MAAO,CAAA,IAAI,CAAG,EAAA;AAAA,QAC1B,SAAW,EAAA,MAAA;AAAA,QACX,SAAW,EAAA;AAAA,OACZ,CAAA;AAAA,KAEE,MAAA;AACH,MAAO,OAAA,MAAA,CAAO,MAAO,CAAA,IAAI,CAAG,EAAA;AAAA,QAC1B,SAAW,EAAA;AAAA,OACZ,CAAA;AAAA;AACH;AAGF,EAAA,SAAS,gBAAiB,CAAA,IAAA,EAAY,OAAgC,GAAA,EAAI,EAAA;AACxE,IAAA,OAAO,IAAI,aAAc,CAAA,MAAA,CAAO,KAAO,EAAA,EAAE,GAAG,IAAM,EAAA,KAAA,EAAO,MAAQ,EAAA,IAAA,EAAM,WAAW,GAAG,OAAA,EAAS,CAAA,CAAE,OAAO,IAAI,CAAA;AAAA;AAG7G,EAAA,SAAS,SAAU,CAAA,IAAA,EAAY,OAAgC,GAAA,EAAI,EAAA;AACjE,IAAA,OAAO,IAAI,aAAA,CAAc,MAAO,CAAA,KAAA,EAAO,EAAE,GAAG,IAAA,EAAM,KAAO,EAAA,MAAA,EAAQ,GAAG,OAAA,EAAS,CAAA,CAAE,OAAO,IAAI,CAAA;AAAA;AAG5F,EAAA,SAAS,SAAY,GAAA;AACnB,IAAM,MAAA,WAAA,GAAc,KAAM,CAAA,gBAAA,EAAkB,CAAA;AAC5C,IAAA,MAAM,MAAS,GAAA,CAAC,CAAG,EAAA,CAAA,EAAG,CAAG,EAAA,CAAA,EAAG,CAAG,EAAA,CAAA,EAAG,CAAG,EAAA,CAAA,EAAG,CAAG,EAAA,EAAA,EAAI,IAAI,EAAE,CAAA;AACrD,IAAA,OAAO,OAAO,GAAI,CAAA,CAAA,IAAA,MAAS,EAAE,KAAO,EAAA,SAAA,CAAU,OAAO,WAAY,CAAA,GAAA,CAAI,EAAE,KAAA,EAAO,MAAM,CAAC,CAAC,CAAG,EAAA,KAAA,EAAO,MAAO,CAAA,CAAA;AAAA;AAGzG,EAAA,SAAS,QAAS,CAAA,IAAA,EAAY,OAAgC,GAAA,EAAI,EAAA;AAChE,IAAA,OAAO,IAAI,aAAA,CAAc,MAAO,CAAA,KAAA,EAAO,EAAE,GAAG,IAAA,EAAM,IAAM,EAAA,SAAA,EAAW,GAAG,OAAA,EAAS,CAAA,CAAE,OAAO,IAAI,CAAA;AAAA;AAG9F,EAAS,SAAA,OAAA,CAAQ,MAAiB,OAAgC,EAAA;AAChE,IAAI,IAAA,eAAA,CAAgB,IAAI,CAAG,EAAA;AACzB,MAAO,OAAA,IAAI,aAAc,CAAA,MAAA,CAAO,KAAO,EAAA;AAAA,QACrC,GAAG,IAAA;AAAA,QACH,GAAG,OAAA;AAAA,QACH,UAAW,IAAuB,CAAA;AAAA,OACnC,CAAA,CAAE,aAAc,CAAA,MAAA,CAAO,IAAI,CAAC,CAAA;AAAA,KAE1B,MAAA;AACH,MAAA,OAAO,IAAI,aAAA,CAAc,MAAO,CAAA,KAAA,EAAO,EAAE,GAAG,IAAA,EAAM,GAAG,OAAA,EAAS,CAAA,CAAE,aAAc,CAAA,MAAA,CAAO,IAAI,CAAC,CAAA;AAAA;AAC5F;AAGF,EAAS,SAAA,SAAA,CAAU,IAAY,EAAA,MAAA,GAA0C,QAAU,EAAA;AACjF,IAAA,OAAO,IAAI,aAAA,CAAc,MAAO,CAAA,KAAA,EAAO,EAAE,GAAG,IAAM,EAAA,OAAA,EAAS,MAAO,EAAC,CAAE,CAAA,MAAA,CAAO,IAAI,CAAA;AAAA;AAGlF,EAAA,SAAS,UAAU,IAAY,EAAA;AAC7B,IAAA,MAAM,KAAQ,GAAA,IAAI,aAAc,CAAA,MAAA,CAAO,KAAO,EAAA;AAAA,MAC5C,GAAG,IAAA;AAAA,MACH,IAAM,EAAA,SAAA;AAAA,MACN,MAAQ,EAAA;AAAA,KACT,CAAE,CAAA,aAAA,CAAc,IAAI,CAAA;AACrB,IAAA,MAAM,QAAQ,KAAM,CAAA,IAAA,CAAK,OAAK,CAAE,CAAA,IAAA,KAAS,WAAW,CAAG,EAAA,KAAA;AACvD,IAAA,IAAI,KAAU,KAAA,IAAA;AACZ,MAAO,OAAA,IAAA;AAET,IAAO,OAAA,IAAA;AAAA;AAGT,EAAA,MAAM,kBAA2C,GAAA;AAAA,IAC/C,IAAM,EAAA,SAAA;AAAA,IACN,KAAO,EAAA,SAAA;AAAA,IACP,GAAK,EAAA,SAAA;AAAA,IACL,IAAM,EAAA,SAAA;AAAA,IACN,MAAQ,EAAA,SAAA;AAAA,IACR,MAAQ,EAAA;AAAA,GACV;AAEA,EAAA,SAAS,IACP,CAAA,OAAA,EACA,IACA,EAAA,OAAA,GAAgC,EAChC,EAAA;AACA,IAAA,MAAMA,KAAO,GAAA,EAAE,GAAG,kBAAA,EAAoB,GAAG,OAAQ,EAAA;AACjD,IAAM,MAAA,KAAA,GAAQ,OAAQ,CAAA,OAAA,EAASA,KAAI,CAAA;AACnC,IAAA,MAAMC,QAAO,KAAM,CAAA,IAAA,CAAK,CAAK,CAAA,KAAA,CAAA,CAAE,SAAS,IAAI,CAAA;AAC5C,IAAOA,OAAAA,KAAAA,GAAOA,MAAK,KAAQ,GAAA,EAAA;AAAA;AAG7B,EAAO,OAAA;AAAA,IACL,SAAA;AAAA,IACA,SAAA;AAAA,IACA,SAAA;AAAA,IACA,QAAA;AAAA,IACA,gBAAA;AAAA,IACA,OAAA;AAAA,IACA,MAAA;AAAA,IACA,IAAA;AAAA,IACA,SAAA;AAAA,IACA,YAAA;AAAA,IACA,SAAA;AAAA,IACA;AAAA,GACF;AACF;;;;"}