import{P as H,c as ye,a as I,_ as Xo,d as qo,b as Zo}from"./AppLogoIcon.vue_vue_type_script_setup_true_lang-43VByX6w.js";import{c as E,B as S,K as Qo,l as Ce,L as Jo,M as en,N as Ee,u as r,d as y,O as Fe,q as b,h as w,o as v,w as m,m as k,P as te,Q as Nt,s as ne,R as so,U as qe,n as Ae,e as O,p as $t,k as $e,V as tn,C as on,D as nn,W as ao,a as L,X as ro,j as io,I as X,J as oe,H as sn,v as an,Y as rn,Z as ln,i as de,t as me,y as Ze,_ as un,b as G,A as W,F as he,E as Bt,r as At,S as Ie,$ as dn}from"./app-BxByyVXe.js";import{b as We,a as F,i as De,o as cn,c as fn,u as lo,d as uo,e as pn,f as Kt,g as Me,h as mn,j as hn,k as co,l as gn,m as fo,r as Qe,n as vn}from"./useForwardExpose-67BWFZEI.js";import{c as re,h as ee,a as ze,j as Je,P as et,k as yn,l as po,m as bn,b as wn,d as ie,_ as _n,e as tt}from"./RovingFocusGroup-hazQWYmS.js";const Cn=["top","right","bottom","left"],xe=Math.min,Q=Math.max,Ge=Math.round,Ve=Math.floor,ue=e=>({x:e,y:e}),xn={left:"right",right:"left",bottom:"top",top:"bottom"},On={start:"end",end:"start"};function vt(e,o,t){return Q(e,xe(o,t))}function ge(e,o){return typeof e=="function"?e(o):e}function ve(e){return e.split("-")[0]}function Le(e){return e.split("-")[1]}function Dt(e){return e==="x"?"y":"x"}function Pt(e){return e==="y"?"height":"width"}function le(e){return["top","bottom"].includes(ve(e))?"y":"x"}function Et(e){return Dt(le(e))}function $n(e,o,t){t===void 0&&(t=!1);const n=Le(e),s=Et(e),a=Pt(s);let i=s==="x"?n===(t?"end":"start")?"right":"left":n==="start"?"bottom":"top";return o.reference[a]>o.floating[a]&&(i=Ye(i)),[i,Ye(i)]}function Bn(e){const o=Ye(e);return[yt(e),o,yt(o)]}function yt(e){return e.replace(/start|end/g,o=>On[o])}function An(e,o,t){const n=["left","right"],s=["right","left"],a=["top","bottom"],i=["bottom","top"];switch(e){case"top":case"bottom":return t?o?s:n:o?n:s;case"left":case"right":return o?a:i;default:return[]}}function Dn(e,o,t,n){const s=Le(e);let a=An(ve(e),t==="start",n);return s&&(a=a.map(i=>i+"-"+s),o&&(a=a.concat(a.map(yt)))),a}function Ye(e){return e.replace(/left|right|bottom|top/g,o=>xn[o])}function Pn(e){return{top:0,right:0,bottom:0,left:0,...e}}function mo(e){return typeof e!="number"?Pn(e):{top:e,right:e,bottom:e,left:e}}function Xe(e){const{x:o,y:t,width:n,height:s}=e;return{width:n,height:s,top:t,left:o,right:o+n,bottom:t+s,x:o,y:t}}function Wt(e,o,t){let{reference:n,floating:s}=e;const a=le(o),i=Et(o),l=Pt(i),d=ve(o),f=a==="y",u=n.x+n.width/2-s.width/2,c=n.y+n.height/2-s.height/2,p=n[l]/2-s[l]/2;let h;switch(d){case"top":h={x:u,y:n.y-s.height};break;case"bottom":h={x:u,y:n.y+n.height};break;case"right":h={x:n.x+n.width,y:c};break;case"left":h={x:n.x-s.width,y:c};break;default:h={x:n.x,y:n.y}}switch(Le(o)){case"start":h[i]-=p*(t&&f?-1:1);break;case"end":h[i]+=p*(t&&f?-1:1);break}return h}const En=async(e,o,t)=>{const{placement:n="bottom",strategy:s="absolute",middleware:a=[],platform:i}=t,l=a.filter(Boolean),d=await(i.isRTL==null?void 0:i.isRTL(o));let f=await i.getElementRects({reference:e,floating:o,strategy:s}),{x:u,y:c}=Wt(f,n,d),p=n,h={},g=0;for(let _=0;_<l.length;_++){const{name:C,fn:$}=l[_],{x:B,y:A,data:P,reset:R}=await $({x:u,y:c,initialPlacement:n,placement:p,strategy:s,middlewareData:h,rects:f,platform:i,elements:{reference:e,floating:o}});u=B??u,c=A??c,h={...h,[C]:{...h[C],...P}},R&&g<=50&&(g++,typeof R=="object"&&(R.placement&&(p=R.placement),R.rects&&(f=R.rects===!0?await i.getElementRects({reference:e,floating:o,strategy:s}):R.rects),{x:u,y:c}=Wt(f,p,d)),_=-1)}return{x:u,y:c,placement:p,strategy:s,middlewareData:h}};async function Ne(e,o){var t;o===void 0&&(o={});const{x:n,y:s,platform:a,rects:i,elements:l,strategy:d}=e,{boundary:f="clippingAncestors",rootBoundary:u="viewport",elementContext:c="floating",altBoundary:p=!1,padding:h=0}=ge(o,e),g=mo(h),C=l[p?c==="floating"?"reference":"floating":c],$=Xe(await a.getClippingRect({element:(t=await(a.isElement==null?void 0:a.isElement(C)))==null||t?C:C.contextElement||await(a.getDocumentElement==null?void 0:a.getDocumentElement(l.floating)),boundary:f,rootBoundary:u,strategy:d})),B=c==="floating"?{x:n,y:s,width:i.floating.width,height:i.floating.height}:i.reference,A=await(a.getOffsetParent==null?void 0:a.getOffsetParent(l.floating)),P=await(a.isElement==null?void 0:a.isElement(A))?await(a.getScale==null?void 0:a.getScale(A))||{x:1,y:1}:{x:1,y:1},R=Xe(a.convertOffsetParentRelativeRectToViewportRelativeRect?await a.convertOffsetParentRelativeRectToViewportRelativeRect({elements:l,rect:B,offsetParent:A,strategy:d}):B);return{top:($.top-R.top+g.top)/P.y,bottom:(R.bottom-$.bottom+g.bottom)/P.y,left:($.left-R.left+g.left)/P.x,right:(R.right-$.right+g.right)/P.x}}const Sn=e=>({name:"arrow",options:e,async fn(o){const{x:t,y:n,placement:s,rects:a,platform:i,elements:l,middlewareData:d}=o,{element:f,padding:u=0}=ge(e,o)||{};if(f==null)return{};const c=mo(u),p={x:t,y:n},h=Et(s),g=Pt(h),_=await i.getDimensions(f),C=h==="y",$=C?"top":"left",B=C?"bottom":"right",A=C?"clientHeight":"clientWidth",P=a.reference[g]+a.reference[h]-p[h]-a.floating[g],R=p[h]-a.reference[h],V=await(i.getOffsetParent==null?void 0:i.getOffsetParent(f));let M=V?V[A]:0;(!M||!await(i.isElement==null?void 0:i.isElement(V)))&&(M=l.floating[A]||a.floating[g]);const N=P/2-R/2,x=M/2-_[g]/2-1,T=xe(c[$],x),D=xe(c[B],x),K=T,U=M-_[g]-D,z=M/2-_[g]/2+N,Y=vt(K,z,U),q=!d.arrow&&Le(s)!=null&&z!==Y&&a.reference[g]/2-(z<K?T:D)-_[g]/2<0,j=q?z<K?z-K:z-U:0;return{[h]:p[h]+j,data:{[h]:Y,centerOffset:z-Y-j,...q&&{alignmentOffset:j}},reset:q}}}),kn=function(e){return e===void 0&&(e={}),{name:"flip",options:e,async fn(o){var t,n;const{placement:s,middlewareData:a,rects:i,initialPlacement:l,platform:d,elements:f}=o,{mainAxis:u=!0,crossAxis:c=!0,fallbackPlacements:p,fallbackStrategy:h="bestFit",fallbackAxisSideDirection:g="none",flipAlignment:_=!0,...C}=ge(e,o);if((t=a.arrow)!=null&&t.alignmentOffset)return{};const $=ve(s),B=le(l),A=ve(l)===l,P=await(d.isRTL==null?void 0:d.isRTL(f.floating)),R=p||(A||!_?[Ye(l)]:Bn(l)),V=g!=="none";!p&&V&&R.push(...Dn(l,_,g,P));const M=[l,...R],N=await Ne(o,C),x=[];let T=((n=a.flip)==null?void 0:n.overflows)||[];if(u&&x.push(N[$]),c){const z=$n(s,i,P);x.push(N[z[0]],N[z[1]])}if(T=[...T,{placement:s,overflows:x}],!x.every(z=>z<=0)){var D,K;const z=(((D=a.flip)==null?void 0:D.index)||0)+1,Y=M[z];if(Y&&(!(c==="alignment"?B!==le(Y):!1)||T.every(Z=>Z.overflows[0]>0&&le(Z.placement)===B)))return{data:{index:z,overflows:T},reset:{placement:Y}};let q=(K=T.filter(j=>j.overflows[0]<=0).sort((j,Z)=>j.overflows[1]-Z.overflows[1])[0])==null?void 0:K.placement;if(!q)switch(h){case"bestFit":{var U;const j=(U=T.filter(Z=>{if(V){const we=le(Z.placement);return we===B||we==="y"}return!0}).map(Z=>[Z.placement,Z.overflows.filter(we=>we>0).reduce((we,Yo)=>we+Yo,0)]).sort((Z,we)=>Z[1]-we[1])[0])==null?void 0:U[0];j&&(q=j);break}case"initialPlacement":q=l;break}if(s!==q)return{reset:{placement:q}}}return{}}}};function Ht(e,o){return{top:e.top-o.height,right:e.right-o.width,bottom:e.bottom-o.height,left:e.left-o.width}}function Vt(e){return Cn.some(o=>e[o]>=0)}const Tn=function(e){return e===void 0&&(e={}),{name:"hide",options:e,async fn(o){const{rects:t}=o,{strategy:n="referenceHidden",...s}=ge(e,o);switch(n){case"referenceHidden":{const a=await Ne(o,{...s,elementContext:"reference"}),i=Ht(a,t.reference);return{data:{referenceHiddenOffsets:i,referenceHidden:Vt(i)}}}case"escaped":{const a=await Ne(o,{...s,altBoundary:!0}),i=Ht(a,t.floating);return{data:{escapedOffsets:i,escaped:Vt(i)}}}default:return{}}}}};async function Mn(e,o){const{placement:t,platform:n,elements:s}=e,a=await(n.isRTL==null?void 0:n.isRTL(s.floating)),i=ve(t),l=Le(t),d=le(t)==="y",f=["left","top"].includes(i)?-1:1,u=a&&d?-1:1,c=ge(o,e);let{mainAxis:p,crossAxis:h,alignmentAxis:g}=typeof c=="number"?{mainAxis:c,crossAxis:0,alignmentAxis:null}:{mainAxis:c.mainAxis||0,crossAxis:c.crossAxis||0,alignmentAxis:c.alignmentAxis};return l&&typeof g=="number"&&(h=l==="end"?g*-1:g),d?{x:h*u,y:p*f}:{x:p*f,y:h*u}}const Rn=function(e){return e===void 0&&(e=0),{name:"offset",options:e,async fn(o){var t,n;const{x:s,y:a,placement:i,middlewareData:l}=o,d=await Mn(o,e);return i===((t=l.offset)==null?void 0:t.placement)&&(n=l.arrow)!=null&&n.alignmentOffset?{}:{x:s+d.x,y:a+d.y,data:{...d,placement:i}}}}},Fn=function(e){return e===void 0&&(e={}),{name:"shift",options:e,async fn(o){const{x:t,y:n,placement:s}=o,{mainAxis:a=!0,crossAxis:i=!1,limiter:l={fn:C=>{let{x:$,y:B}=C;return{x:$,y:B}}},...d}=ge(e,o),f={x:t,y:n},u=await Ne(o,d),c=le(ve(s)),p=Dt(c);let h=f[p],g=f[c];if(a){const C=p==="y"?"top":"left",$=p==="y"?"bottom":"right",B=h+u[C],A=h-u[$];h=vt(B,h,A)}if(i){const C=c==="y"?"top":"left",$=c==="y"?"bottom":"right",B=g+u[C],A=g-u[$];g=vt(B,g,A)}const _=l.fn({...o,[p]:h,[c]:g});return{..._,data:{x:_.x-t,y:_.y-n,enabled:{[p]:a,[c]:i}}}}}},Ln=function(e){return e===void 0&&(e={}),{options:e,fn(o){const{x:t,y:n,placement:s,rects:a,middlewareData:i}=o,{offset:l=0,mainAxis:d=!0,crossAxis:f=!0}=ge(e,o),u={x:t,y:n},c=le(s),p=Dt(c);let h=u[p],g=u[c];const _=ge(l,o),C=typeof _=="number"?{mainAxis:_,crossAxis:0}:{mainAxis:0,crossAxis:0,..._};if(d){const A=p==="y"?"height":"width",P=a.reference[p]-a.floating[A]+C.mainAxis,R=a.reference[p]+a.reference[A]-C.mainAxis;h<P?h=P:h>R&&(h=R)}if(f){var $,B;const A=p==="y"?"width":"height",P=["top","left"].includes(ve(s)),R=a.reference[c]-a.floating[A]+(P&&(($=i.offset)==null?void 0:$[c])||0)+(P?0:C.crossAxis),V=a.reference[c]+a.reference[A]+(P?0:((B=i.offset)==null?void 0:B[c])||0)-(P?C.crossAxis:0);g<R?g=R:g>V&&(g=V)}return{[p]:h,[c]:g}}}},In=function(e){return e===void 0&&(e={}),{name:"size",options:e,async fn(o){var t,n;const{placement:s,rects:a,platform:i,elements:l}=o,{apply:d=()=>{},...f}=ge(e,o),u=await Ne(o,f),c=ve(s),p=Le(s),h=le(s)==="y",{width:g,height:_}=a.floating;let C,$;c==="top"||c==="bottom"?(C=c,$=p===(await(i.isRTL==null?void 0:i.isRTL(l.floating))?"start":"end")?"left":"right"):($=c,C=p==="end"?"top":"bottom");const B=_-u.top-u.bottom,A=g-u.left-u.right,P=xe(_-u[C],B),R=xe(g-u[$],A),V=!o.middlewareData.shift;let M=P,N=R;if((t=o.middlewareData.shift)!=null&&t.enabled.x&&(N=A),(n=o.middlewareData.shift)!=null&&n.enabled.y&&(M=B),V&&!p){const T=Q(u.left,0),D=Q(u.right,0),K=Q(u.top,0),U=Q(u.bottom,0);h?N=g-2*(T!==0||D!==0?T+D:Q(u.left,u.right)):M=_-2*(K!==0||U!==0?K+U:Q(u.top,u.bottom))}await d({...o,availableWidth:N,availableHeight:M});const x=await i.getDimensions(l.floating);return g!==x.width||_!==x.height?{reset:{rects:!0}}:{}}}};function ot(){return typeof window<"u"}function Pe(e){return St(e)?(e.nodeName||"").toLowerCase():"#document"}function J(e){var o;return(e==null||(o=e.ownerDocument)==null?void 0:o.defaultView)||window}function fe(e){var o;return(o=(St(e)?e.ownerDocument:e.document)||window.document)==null?void 0:o.documentElement}function St(e){return ot()?e instanceof Node||e instanceof J(e).Node:!1}function se(e){return ot()?e instanceof Element||e instanceof J(e).Element:!1}function ce(e){return ot()?e instanceof HTMLElement||e instanceof J(e).HTMLElement:!1}function jt(e){return!ot()||typeof ShadowRoot>"u"?!1:e instanceof ShadowRoot||e instanceof J(e).ShadowRoot}function He(e){const{overflow:o,overflowX:t,overflowY:n,display:s}=ae(e);return/auto|scroll|overlay|hidden|clip/.test(o+n+t)&&!["inline","contents"].includes(s)}function zn(e){return["table","td","th"].includes(Pe(e))}function nt(e){return[":popover-open",":modal"].some(o=>{try{return e.matches(o)}catch{return!1}})}function kt(e){const o=Tt(),t=se(e)?ae(e):e;return["transform","translate","scale","rotate","perspective"].some(n=>t[n]?t[n]!=="none":!1)||(t.containerType?t.containerType!=="normal":!1)||!o&&(t.backdropFilter?t.backdropFilter!=="none":!1)||!o&&(t.filter?t.filter!=="none":!1)||["transform","translate","scale","rotate","perspective","filter"].some(n=>(t.willChange||"").includes(n))||["paint","layout","strict","content"].some(n=>(t.contain||"").includes(n))}function Nn(e){let o=Oe(e);for(;ce(o)&&!Re(o);){if(kt(o))return o;if(nt(o))return null;o=Oe(o)}return null}function Tt(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function Re(e){return["html","body","#document"].includes(Pe(e))}function ae(e){return J(e).getComputedStyle(e)}function st(e){return se(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function Oe(e){if(Pe(e)==="html")return e;const o=e.assignedSlot||e.parentNode||jt(e)&&e.host||fe(e);return jt(o)?o.host:o}function ho(e){const o=Oe(e);return Re(o)?e.ownerDocument?e.ownerDocument.body:e.body:ce(o)&&He(o)?o:ho(o)}function Ke(e,o,t){var n;o===void 0&&(o=[]),t===void 0&&(t=!0);const s=ho(e),a=s===((n=e.ownerDocument)==null?void 0:n.body),i=J(s);if(a){const l=bt(i);return o.concat(i,i.visualViewport||[],He(s)?s:[],l&&t?Ke(l):[])}return o.concat(s,Ke(s,[],t))}function bt(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function go(e){const o=ae(e);let t=parseFloat(o.width)||0,n=parseFloat(o.height)||0;const s=ce(e),a=s?e.offsetWidth:t,i=s?e.offsetHeight:n,l=Ge(t)!==a||Ge(n)!==i;return l&&(t=a,n=i),{width:t,height:n,$:l}}function Mt(e){return se(e)?e:e.contextElement}function Te(e){const o=Mt(e);if(!ce(o))return ue(1);const t=o.getBoundingClientRect(),{width:n,height:s,$:a}=go(o);let i=(a?Ge(t.width):t.width)/n,l=(a?Ge(t.height):t.height)/s;return(!i||!Number.isFinite(i))&&(i=1),(!l||!Number.isFinite(l))&&(l=1),{x:i,y:l}}const Kn=ue(0);function vo(e){const o=J(e);return!Tt()||!o.visualViewport?Kn:{x:o.visualViewport.offsetLeft,y:o.visualViewport.offsetTop}}function Wn(e,o,t){return o===void 0&&(o=!1),!t||o&&t!==J(e)?!1:o}function Be(e,o,t,n){o===void 0&&(o=!1),t===void 0&&(t=!1);const s=e.getBoundingClientRect(),a=Mt(e);let i=ue(1);o&&(n?se(n)&&(i=Te(n)):i=Te(e));const l=Wn(a,t,n)?vo(a):ue(0);let d=(s.left+l.x)/i.x,f=(s.top+l.y)/i.y,u=s.width/i.x,c=s.height/i.y;if(a){const p=J(a),h=n&&se(n)?J(n):n;let g=p,_=bt(g);for(;_&&n&&h!==g;){const C=Te(_),$=_.getBoundingClientRect(),B=ae(_),A=$.left+(_.clientLeft+parseFloat(B.paddingLeft))*C.x,P=$.top+(_.clientTop+parseFloat(B.paddingTop))*C.y;d*=C.x,f*=C.y,u*=C.x,c*=C.y,d+=A,f+=P,g=J(_),_=bt(g)}}return Xe({width:u,height:c,x:d,y:f})}function Rt(e,o){const t=st(e).scrollLeft;return o?o.left+t:Be(fe(e)).left+t}function yo(e,o,t){t===void 0&&(t=!1);const n=e.getBoundingClientRect(),s=n.left+o.scrollLeft-(t?0:Rt(e,n)),a=n.top+o.scrollTop;return{x:s,y:a}}function Hn(e){let{elements:o,rect:t,offsetParent:n,strategy:s}=e;const a=s==="fixed",i=fe(n),l=o?nt(o.floating):!1;if(n===i||l&&a)return t;let d={scrollLeft:0,scrollTop:0},f=ue(1);const u=ue(0),c=ce(n);if((c||!c&&!a)&&((Pe(n)!=="body"||He(i))&&(d=st(n)),ce(n))){const h=Be(n);f=Te(n),u.x=h.x+n.clientLeft,u.y=h.y+n.clientTop}const p=i&&!c&&!a?yo(i,d,!0):ue(0);return{width:t.width*f.x,height:t.height*f.y,x:t.x*f.x-d.scrollLeft*f.x+u.x+p.x,y:t.y*f.y-d.scrollTop*f.y+u.y+p.y}}function Vn(e){return Array.from(e.getClientRects())}function jn(e){const o=fe(e),t=st(e),n=e.ownerDocument.body,s=Q(o.scrollWidth,o.clientWidth,n.scrollWidth,n.clientWidth),a=Q(o.scrollHeight,o.clientHeight,n.scrollHeight,n.clientHeight);let i=-t.scrollLeft+Rt(e);const l=-t.scrollTop;return ae(n).direction==="rtl"&&(i+=Q(o.clientWidth,n.clientWidth)-s),{width:s,height:a,x:i,y:l}}function Un(e,o){const t=J(e),n=fe(e),s=t.visualViewport;let a=n.clientWidth,i=n.clientHeight,l=0,d=0;if(s){a=s.width,i=s.height;const f=Tt();(!f||f&&o==="fixed")&&(l=s.offsetLeft,d=s.offsetTop)}return{width:a,height:i,x:l,y:d}}function Gn(e,o){const t=Be(e,!0,o==="fixed"),n=t.top+e.clientTop,s=t.left+e.clientLeft,a=ce(e)?Te(e):ue(1),i=e.clientWidth*a.x,l=e.clientHeight*a.y,d=s*a.x,f=n*a.y;return{width:i,height:l,x:d,y:f}}function Ut(e,o,t){let n;if(o==="viewport")n=Un(e,t);else if(o==="document")n=jn(fe(e));else if(se(o))n=Gn(o,t);else{const s=vo(e);n={x:o.x-s.x,y:o.y-s.y,width:o.width,height:o.height}}return Xe(n)}function bo(e,o){const t=Oe(e);return t===o||!se(t)||Re(t)?!1:ae(t).position==="fixed"||bo(t,o)}function Yn(e,o){const t=o.get(e);if(t)return t;let n=Ke(e,[],!1).filter(l=>se(l)&&Pe(l)!=="body"),s=null;const a=ae(e).position==="fixed";let i=a?Oe(e):e;for(;se(i)&&!Re(i);){const l=ae(i),d=kt(i);!d&&l.position==="fixed"&&(s=null),(a?!d&&!s:!d&&l.position==="static"&&!!s&&["absolute","fixed"].includes(s.position)||He(i)&&!d&&bo(e,i))?n=n.filter(u=>u!==i):s=l,i=Oe(i)}return o.set(e,n),n}function Xn(e){let{element:o,boundary:t,rootBoundary:n,strategy:s}=e;const i=[...t==="clippingAncestors"?nt(o)?[]:Yn(o,this._c):[].concat(t),n],l=i[0],d=i.reduce((f,u)=>{const c=Ut(o,u,s);return f.top=Q(c.top,f.top),f.right=xe(c.right,f.right),f.bottom=xe(c.bottom,f.bottom),f.left=Q(c.left,f.left),f},Ut(o,l,s));return{width:d.right-d.left,height:d.bottom-d.top,x:d.left,y:d.top}}function qn(e){const{width:o,height:t}=go(e);return{width:o,height:t}}function Zn(e,o,t){const n=ce(o),s=fe(o),a=t==="fixed",i=Be(e,!0,a,o);let l={scrollLeft:0,scrollTop:0};const d=ue(0);function f(){d.x=Rt(s)}if(n||!n&&!a)if((Pe(o)!=="body"||He(s))&&(l=st(o)),n){const h=Be(o,!0,a,o);d.x=h.x+o.clientLeft,d.y=h.y+o.clientTop}else s&&f();a&&!n&&s&&f();const u=s&&!n&&!a?yo(s,l):ue(0),c=i.left+l.scrollLeft-d.x-u.x,p=i.top+l.scrollTop-d.y-u.y;return{x:c,y:p,width:i.width,height:i.height}}function ct(e){return ae(e).position==="static"}function Gt(e,o){if(!ce(e)||ae(e).position==="fixed")return null;if(o)return o(e);let t=e.offsetParent;return fe(e)===t&&(t=t.ownerDocument.body),t}function wo(e,o){const t=J(e);if(nt(e))return t;if(!ce(e)){let s=Oe(e);for(;s&&!Re(s);){if(se(s)&&!ct(s))return s;s=Oe(s)}return t}let n=Gt(e,o);for(;n&&zn(n)&&ct(n);)n=Gt(n,o);return n&&Re(n)&&ct(n)&&!kt(n)?t:n||Nn(e)||t}const Qn=async function(e){const o=this.getOffsetParent||wo,t=this.getDimensions,n=await t(e.floating);return{reference:Zn(e.reference,await o(e.floating),e.strategy),floating:{x:0,y:0,width:n.width,height:n.height}}};function Jn(e){return ae(e).direction==="rtl"}const es={convertOffsetParentRelativeRectToViewportRelativeRect:Hn,getDocumentElement:fe,getClippingRect:Xn,getOffsetParent:wo,getElementRects:Qn,getClientRects:Vn,getDimensions:qn,getScale:Te,isElement:se,isRTL:Jn};function _o(e,o){return e.x===o.x&&e.y===o.y&&e.width===o.width&&e.height===o.height}function ts(e,o){let t=null,n;const s=fe(e);function a(){var l;clearTimeout(n),(l=t)==null||l.disconnect(),t=null}function i(l,d){l===void 0&&(l=!1),d===void 0&&(d=1),a();const f=e.getBoundingClientRect(),{left:u,top:c,width:p,height:h}=f;if(l||o(),!p||!h)return;const g=Ve(c),_=Ve(s.clientWidth-(u+p)),C=Ve(s.clientHeight-(c+h)),$=Ve(u),A={rootMargin:-g+"px "+-_+"px "+-C+"px "+-$+"px",threshold:Q(0,xe(1,d))||1};let P=!0;function R(V){const M=V[0].intersectionRatio;if(M!==d){if(!P)return i();M?i(!1,M):n=setTimeout(()=>{i(!1,1e-7)},1e3)}M===1&&!_o(f,e.getBoundingClientRect())&&i(),P=!1}try{t=new IntersectionObserver(R,{...A,root:s.ownerDocument})}catch{t=new IntersectionObserver(R,A)}t.observe(e)}return i(!0),a}function os(e,o,t,n){n===void 0&&(n={});const{ancestorScroll:s=!0,ancestorResize:a=!0,elementResize:i=typeof ResizeObserver=="function",layoutShift:l=typeof IntersectionObserver=="function",animationFrame:d=!1}=n,f=Mt(e),u=s||a?[...f?Ke(f):[],...Ke(o)]:[];u.forEach($=>{s&&$.addEventListener("scroll",t,{passive:!0}),a&&$.addEventListener("resize",t)});const c=f&&l?ts(f,t):null;let p=-1,h=null;i&&(h=new ResizeObserver($=>{let[B]=$;B&&B.target===f&&h&&(h.unobserve(o),cancelAnimationFrame(p),p=requestAnimationFrame(()=>{var A;(A=h)==null||A.observe(o)})),t()}),f&&!d&&h.observe(f),h.observe(o));let g,_=d?Be(e):null;d&&C();function C(){const $=Be(e);_&&!_o(_,$)&&t(),_=$,g=requestAnimationFrame(C)}return t(),()=>{var $;u.forEach(B=>{s&&B.removeEventListener("scroll",t),a&&B.removeEventListener("resize",t)}),c==null||c(),($=h)==null||$.disconnect(),h=null,d&&cancelAnimationFrame(g)}}const ns=Rn,ss=Fn,Yt=kn,as=In,rs=Tn,is=Sn,ls=Ln,us=(e,o,t)=>{const n=new Map,s={platform:es,...t},a={...s.platform,_c:n};return En(e,o,{...s,platform:a})};function ds(e){return e!=null&&typeof e=="object"&&"$el"in e}function wt(e){if(ds(e)){const o=e.$el;return St(o)&&Pe(o)==="#comment"?null:o}return e}function ke(e){return typeof e=="function"?e():r(e)}function cs(e){return{name:"arrow",options:e,fn(o){const t=wt(ke(e.element));return t==null?{}:is({element:t,padding:e.padding}).fn(o)}}}function Co(e){return typeof window>"u"?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function Xt(e,o){const t=Co(e);return Math.round(o*t)/t}function fs(e,o,t){t===void 0&&(t={});const n=t.whileElementsMounted,s=E(()=>{var M;return(M=ke(t.open))!=null?M:!0}),a=E(()=>ke(t.middleware)),i=E(()=>{var M;return(M=ke(t.placement))!=null?M:"bottom"}),l=E(()=>{var M;return(M=ke(t.strategy))!=null?M:"absolute"}),d=E(()=>{var M;return(M=ke(t.transform))!=null?M:!0}),f=E(()=>wt(e.value)),u=E(()=>wt(o.value)),c=S(0),p=S(0),h=S(l.value),g=S(i.value),_=Qo({}),C=S(!1),$=E(()=>{const M={position:h.value,left:"0",top:"0"};if(!u.value)return M;const N=Xt(u.value,c.value),x=Xt(u.value,p.value);return d.value?{...M,transform:"translate("+N+"px, "+x+"px)",...Co(u.value)>=1.5&&{willChange:"transform"}}:{position:h.value,left:N+"px",top:x+"px"}});let B;function A(){if(f.value==null||u.value==null)return;const M=s.value;us(f.value,u.value,{middleware:a.value,placement:i.value,strategy:l.value}).then(N=>{c.value=N.x,p.value=N.y,h.value=N.strategy,g.value=N.placement,_.value=N.middlewareData,C.value=M!==!1})}function P(){typeof B=="function"&&(B(),B=void 0)}function R(){if(P(),n===void 0){A();return}if(f.value!=null&&u.value!=null){B=n(f.value,u.value,A);return}}function V(){s.value||(C.value=!1)}return Ce([a,i,l,s],A,{flush:"sync"}),Ce([f,u],R,{flush:"sync"}),Ce(s,V,{flush:"sync"}),Jo()&&en(P),{x:Ee(c),y:Ee(p),strategy:Ee(h),placement:Ee(g),middlewareData:Ee(_),isPositioned:Ee(C),floatingStyles:$,update:A}}const ps=["INPUT","TEXTAREA"];function ms(e,o,t,n={}){if(!o||n.enableIgnoredElement&&ps.includes(o.nodeName))return null;const{arrowKeyOptions:s="both",attributeName:a="[data-reka-collection-item]",itemsArray:i=[],loop:l=!0,dir:d="ltr",preventScroll:f=!0,focus:u=!1}=n,[c,p,h,g,_,C]=[e.key==="ArrowRight",e.key==="ArrowLeft",e.key==="ArrowUp",e.key==="ArrowDown",e.key==="Home",e.key==="End"],$=h||g,B=c||p;if(!_&&!C&&(!$&&!B||s==="vertical"&&B||s==="horizontal"&&$))return null;const A=t?Array.from(t.querySelectorAll(a)):i;if(!A.length)return null;f&&e.preventDefault();let P=null;return B||$?P=xo(A,o,{goForward:$?g:d==="ltr"?c:p,loop:l}):_?P=A.at(0)||null:C&&(P=A.at(-1)||null),u&&(P==null||P.focus()),P}function xo(e,o,t,n=e.length){if(--n===0)return null;const s=e.indexOf(o),a=t.goForward?s+1:s-1;if(!t.loop&&(a<0||a>=e.length))return null;const i=(a+e.length)%e.length,l=e[i];return l?l.hasAttribute("disabled")&&l.getAttribute("disabled")!=="false"?xo(e,l,t,n):l:null}const[be,hs]=re("DialogRoot"),gs=y({inheritAttrs:!1,__name:"DialogRoot",props:{open:{type:Boolean,default:void 0},defaultOpen:{type:Boolean,default:!1},modal:{type:Boolean,default:!0}},emits:["update:open"],setup(e,{emit:o}){const t=e,s=We(t,"open",o,{defaultValue:t.defaultOpen,passive:t.open===void 0}),a=S(),i=S(),{modal:l}=Fe(t);return hs({open:s,modal:l,openModal:()=>{s.value=!0},onOpenChange:d=>{s.value=d},onOpenToggle:()=>{s.value=!s.value},contentId:"",titleId:"",descriptionId:"",triggerElement:a,contentElement:i}),(d,f)=>b(d.$slots,"default",{open:r(s),close:()=>s.value=!1})}}),vs=y({__name:"DialogClose",props:{asChild:{type:Boolean},as:{default:"button"}},setup(e){const o=e;F();const t=be();return(n,s)=>(v(),w(r(H),k(o,{type:n.as==="button"?"button":void 0,onClick:s[0]||(s[0]=a=>r(t).onOpenChange(!1))}),{default:m(()=>[b(n.$slots,"default")]),_:3},16,["type"]))}});function Oo(e,o,t){const n=t.originalEvent.target,s=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:t});o&&n.addEventListener(e,o,{once:!0}),n.dispatchEvent(s)}const ys="dismissableLayer.pointerDownOutside",bs="dismissableLayer.focusOutside";function $o(e,o){const t=o.closest("[data-dismissable-layer]"),n=e.dataset.dismissableLayer===""?e:e.querySelector("[data-dismissable-layer]"),s=Array.from(e.ownerDocument.querySelectorAll("[data-dismissable-layer]"));return!!(t&&(n===t||s.indexOf(n)<s.indexOf(t)))}function ws(e,o,t=!0){var i;const n=((i=o==null?void 0:o.value)==null?void 0:i.ownerDocument)??(globalThis==null?void 0:globalThis.document),s=S(!1),a=S(()=>{});return te(l=>{if(!De||!Nt(t))return;const d=async u=>{const c=u.target;if(!(!(o!=null&&o.value)||!c)){if($o(o.value,c)){s.value=!1;return}if(u.target&&!s.value){let p=function(){Oo(ys,e,h)};const h={originalEvent:u};u.pointerType==="touch"?(n.removeEventListener("click",a.value),a.value=p,n.addEventListener("click",a.value,{once:!0})):p()}else n.removeEventListener("click",a.value);s.value=!1}},f=window.setTimeout(()=>{n.addEventListener("pointerdown",d)},0);l(()=>{window.clearTimeout(f),n.removeEventListener("pointerdown",d),n.removeEventListener("click",a.value)})}),{onPointerDownCapture:()=>{Nt(t)&&(s.value=!0)}}}function _s(e,o){var s;const t=((s=o==null?void 0:o.value)==null?void 0:s.ownerDocument)??(globalThis==null?void 0:globalThis.document),n=S(!1);return te(a=>{if(!De)return;const i=async l=>{if(!(o!=null&&o.value))return;await ne(),await ne();const d=l.target;!o.value||!d||$o(o.value,d)||l.target&&!n.value&&Oo(bs,e,{originalEvent:l})};t.addEventListener("focusin",i),a(()=>t.removeEventListener("focusin",i))}),{onFocusCapture:()=>n.value=!0,onBlurCapture:()=>n.value=!1}}const pe=so({layersRoot:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),Ft=y({__name:"DismissableLayer",props:{disableOutsidePointerEvents:{type:Boolean,default:!1},asChild:{type:Boolean},as:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","dismiss"],setup(e,{emit:o}){const t=e,n=o,{forwardRef:s,currentElement:a}=F(),i=E(()=>{var g;return((g=a.value)==null?void 0:g.ownerDocument)??globalThis.document}),l=E(()=>pe.layersRoot),d=E(()=>a.value?Array.from(l.value).indexOf(a.value):-1),f=E(()=>pe.layersWithOutsidePointerEventsDisabled.size>0),u=E(()=>{const g=Array.from(l.value),[_]=[...pe.layersWithOutsidePointerEventsDisabled].slice(-1),C=g.indexOf(_);return d.value>=C}),c=ws(async g=>{const _=[...pe.branches].some(C=>C==null?void 0:C.contains(g.target));!u.value||_||(n("pointerDownOutside",g),n("interactOutside",g),await ne(),g.defaultPrevented||n("dismiss"))},a),p=_s(g=>{[...pe.branches].some(C=>C==null?void 0:C.contains(g.target))||(n("focusOutside",g),n("interactOutside",g),g.defaultPrevented||n("dismiss"))},a);cn("Escape",g=>{d.value===l.value.size-1&&(n("escapeKeyDown",g),g.defaultPrevented||n("dismiss"))});let h;return te(g=>{a.value&&(t.disableOutsidePointerEvents&&(pe.layersWithOutsidePointerEventsDisabled.size===0&&(h=i.value.body.style.pointerEvents,i.value.body.style.pointerEvents="none"),pe.layersWithOutsidePointerEventsDisabled.add(a.value)),l.value.add(a.value),g(()=>{t.disableOutsidePointerEvents&&pe.layersWithOutsidePointerEventsDisabled.size===1&&(i.value.body.style.pointerEvents=h)}))}),te(g=>{g(()=>{a.value&&(l.value.delete(a.value),pe.layersWithOutsidePointerEventsDisabled.delete(a.value))})}),(g,_)=>(v(),w(r(H),{ref:r(s),"as-child":g.asChild,as:g.as,"data-dismissable-layer":"",style:qe({pointerEvents:f.value?u.value?"auto":"none":void 0}),onFocusCapture:r(p).onFocusCapture,onBlurCapture:r(p).onBlurCapture,onPointerdownCapture:r(c).onPointerDownCapture},{default:m(()=>[b(g.$slots,"default")]),_:3},8,["as-child","as","style","onFocusCapture","onBlurCapture","onPointerdownCapture"]))}}),Cs="menu.itemSelect",_t=["Enter"," "],xs=["ArrowDown","PageUp","Home"],Bo=["ArrowUp","PageDown","End"],Os=[...xs,...Bo];[..._t],[..._t];function Ao(e){return e?"open":"closed"}function $s(e){const o=ee();for(const t of e)if(t===o||(t.focus(),ee()!==o))return}function Bs(e,o){const{x:t,y:n}=e;let s=!1;for(let a=0,i=o.length-1;a<o.length;i=a++){const l=o[a].x,d=o[a].y,f=o[i].x,u=o[i].y;d>n!=u>n&&t<(f-l)*(n-d)/(u-d)+l&&(s=!s)}return s}function As(e,o){if(!o)return!1;const t={x:e.clientX,y:e.clientY};return Bs(t,o)}function Ct(e){return e.pointerType==="mouse"}const Ds=fn(()=>S([]));function Ps(){const e=Ds();return{add(o){const t=e.value[0];o!==t&&(t==null||t.pause()),e.value=qt(e.value,o),e.value.unshift(o)},remove(o){var t;e.value=qt(e.value,o),(t=e.value[0])==null||t.resume()}}}function qt(e,o){const t=[...e],n=t.indexOf(o);return n!==-1&&t.splice(n,1),t}function Es(e){return e.filter(o=>o.tagName!=="A")}const ft="focusScope.autoFocusOnMount",pt="focusScope.autoFocusOnUnmount",Zt={bubbles:!1,cancelable:!0};function Ss(e,{select:o=!1}={}){const t=ee();for(const n of e)if(_e(n,{select:o}),ee()!==t)return!0}function ks(e){const o=Do(e),t=Qt(o,e),n=Qt(o.reverse(),e);return[t,n]}function Do(e){const o=[],t=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:n=>{const s=n.tagName==="INPUT"&&n.type==="hidden";return n.disabled||n.hidden||s?NodeFilter.FILTER_SKIP:n.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;t.nextNode();)o.push(t.currentNode);return o}function Qt(e,o){for(const t of e)if(!Ts(t,{upTo:o}))return t}function Ts(e,{upTo:o}){if(getComputedStyle(e).visibility==="hidden")return!0;for(;e;){if(o!==void 0&&e===o)return!1;if(getComputedStyle(e).display==="none")return!0;e=e.parentElement}return!1}function Ms(e){return e instanceof HTMLInputElement&&"select"in e}function _e(e,{select:o=!1}={}){if(e&&e.focus){const t=ee();e.focus({preventScroll:!0}),e!==t&&Ms(e)&&o&&e.select()}}const Po=y({__name:"FocusScope",props:{loop:{type:Boolean,default:!1},trapped:{type:Boolean,default:!1},asChild:{type:Boolean},as:{}},emits:["mountAutoFocus","unmountAutoFocus"],setup(e,{emit:o}){const t=e,n=o,{currentRef:s,currentElement:a}=F(),i=S(null),l=Ps(),d=so({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}});te(u=>{if(!De)return;const c=a.value;if(!t.trapped)return;function p(C){if(d.paused||!c)return;const $=C.target;c.contains($)?i.value=$:_e(i.value,{select:!0})}function h(C){if(d.paused||!c)return;const $=C.relatedTarget;$!==null&&(c.contains($)||_e(i.value,{select:!0}))}function g(C){c.contains(i.value)||_e(c)}document.addEventListener("focusin",p),document.addEventListener("focusout",h);const _=new MutationObserver(g);c&&_.observe(c,{childList:!0,subtree:!0}),u(()=>{document.removeEventListener("focusin",p),document.removeEventListener("focusout",h),_.disconnect()})}),te(async u=>{const c=a.value;if(await ne(),!c)return;l.add(d);const p=ee();if(!c.contains(p)){const g=new CustomEvent(ft,Zt);c.addEventListener(ft,_=>n("mountAutoFocus",_)),c.dispatchEvent(g),g.defaultPrevented||(Ss(Es(Do(c)),{select:!0}),ee()===p&&_e(c))}u(()=>{c.removeEventListener(ft,C=>n("mountAutoFocus",C));const g=new CustomEvent(pt,Zt),_=C=>{n("unmountAutoFocus",C)};c.addEventListener(pt,_),c.dispatchEvent(g),setTimeout(()=>{g.defaultPrevented||_e(p??document.body,{select:!0}),c.removeEventListener(pt,_),l.remove(d)},0)})});function f(u){if(!t.loop&&!t.trapped||d.paused)return;const c=u.key==="Tab"&&!u.altKey&&!u.ctrlKey&&!u.metaKey,p=ee();if(c&&p){const h=u.currentTarget,[g,_]=ks(h);g&&_?!u.shiftKey&&p===_?(u.preventDefault(),t.loop&&_e(g,{select:!0})):u.shiftKey&&p===g&&(u.preventDefault(),t.loop&&_e(_,{select:!0})):p===h&&u.preventDefault()}}return(u,c)=>(v(),w(r(H),{ref_key:"currentRef",ref:s,tabindex:"-1","as-child":u.asChild,as:u.as,onKeydown:f},{default:m(()=>[b(u.$slots,"default")]),_:3},8,["as-child","as"]))}}),Eo=y({__name:"DialogContentImpl",props:{forceMount:{type:Boolean},trapFocus:{type:Boolean},disableOutsidePointerEvents:{type:Boolean},asChild:{type:Boolean},as:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","openAutoFocus","closeAutoFocus"],setup(e,{emit:o}){const t=e,n=o,s=be(),{forwardRef:a,currentElement:i}=F();return s.titleId||(s.titleId=ze(void 0,"reka-dialog-title")),s.descriptionId||(s.descriptionId=ze(void 0,"reka-dialog-description")),Ae(()=>{s.contentElement=i,ee()!==document.body&&(s.triggerElement.value=ee())}),(l,d)=>(v(),w(r(Po),{"as-child":"",loop:"",trapped:t.trapFocus,onMountAutoFocus:d[5]||(d[5]=f=>n("openAutoFocus",f)),onUnmountAutoFocus:d[6]||(d[6]=f=>n("closeAutoFocus",f))},{default:m(()=>[O(r(Ft),k({id:r(s).contentId,ref:r(a),as:l.as,"as-child":l.asChild,"disable-outside-pointer-events":l.disableOutsidePointerEvents,role:"dialog","aria-describedby":r(s).descriptionId,"aria-labelledby":r(s).titleId,"data-state":r(Ao)(r(s).open.value)},l.$attrs,{onDismiss:d[0]||(d[0]=f=>r(s).onOpenChange(!1)),onEscapeKeyDown:d[1]||(d[1]=f=>n("escapeKeyDown",f)),onFocusOutside:d[2]||(d[2]=f=>n("focusOutside",f)),onInteractOutside:d[3]||(d[3]=f=>n("interactOutside",f)),onPointerDownOutside:d[4]||(d[4]=f=>n("pointerDownOutside",f))}),{default:m(()=>[b(l.$slots,"default")]),_:3},16,["id","as","as-child","disable-outside-pointer-events","aria-describedby","aria-labelledby","data-state"])]),_:3},8,["trapped"]))}});var Rs=function(e){if(typeof document>"u")return null;var o=Array.isArray(e)?e[0]:e;return o.ownerDocument.body},Se=new WeakMap,je=new WeakMap,Ue={},mt=0,So=function(e){return e&&(e.host||So(e.parentNode))},Fs=function(e,o){return o.map(function(t){if(e.contains(t))return t;var n=So(t);return n&&e.contains(n)?n:(console.error("aria-hidden",t,"in not contained inside",e,". Doing nothing"),null)}).filter(function(t){return!!t})},Ls=function(e,o,t,n){var s=Fs(o,Array.isArray(e)?e:[e]);Ue[t]||(Ue[t]=new WeakMap);var a=Ue[t],i=[],l=new Set,d=new Set(s),f=function(c){!c||l.has(c)||(l.add(c),f(c.parentNode))};s.forEach(f);var u=function(c){!c||d.has(c)||Array.prototype.forEach.call(c.children,function(p){if(l.has(p))u(p);else try{var h=p.getAttribute(n),g=h!==null&&h!=="false",_=(Se.get(p)||0)+1,C=(a.get(p)||0)+1;Se.set(p,_),a.set(p,C),i.push(p),_===1&&g&&je.set(p,!0),C===1&&p.setAttribute(t,"true"),g||p.setAttribute(n,"true")}catch($){console.error("aria-hidden: cannot operate on ",p,$)}})};return u(o),l.clear(),mt++,function(){i.forEach(function(c){var p=Se.get(c)-1,h=a.get(c)-1;Se.set(c,p),a.set(c,h),p||(je.has(c)||c.removeAttribute(n),je.delete(c)),h||c.removeAttribute(t)}),mt--,mt||(Se=new WeakMap,Se=new WeakMap,je=new WeakMap,Ue={})}},Is=function(e,o,t){t===void 0&&(t="data-aria-hidden");var n=Array.from(Array.isArray(e)?e:[e]),s=Rs(e);return s?(n.push.apply(n,Array.from(s.querySelectorAll("[aria-live], script"))),Ls(n,s,t,"aria-hidden")):function(){return null}};function ko(e){let o;Ce(()=>lo(e),t=>{t?o=Is(t):o&&o()}),$t(()=>{o&&o()})}const zs=y({__name:"DialogContentModal",props:{forceMount:{type:Boolean},trapFocus:{type:Boolean},disableOutsidePointerEvents:{type:Boolean},asChild:{type:Boolean},as:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","openAutoFocus","closeAutoFocus"],setup(e,{emit:o}){const t=e,n=o,s=be(),a=Je(n),{forwardRef:i,currentElement:l}=F();return ko(l),(d,f)=>(v(),w(Eo,k({...t,...r(a)},{ref:r(i),"trap-focus":r(s).open.value,"disable-outside-pointer-events":!0,onCloseAutoFocus:f[0]||(f[0]=u=>{var c;u.defaultPrevented||(u.preventDefault(),(c=r(s).triggerElement.value)==null||c.focus())}),onPointerDownOutside:f[1]||(f[1]=u=>{const c=u.detail.originalEvent,p=c.button===0&&c.ctrlKey===!0;(c.button===2||p)&&u.preventDefault()}),onFocusOutside:f[2]||(f[2]=u=>{u.preventDefault()})}),{default:m(()=>[b(d.$slots,"default")]),_:3},16,["trap-focus"]))}}),Ns=y({__name:"DialogContentNonModal",props:{forceMount:{type:Boolean},trapFocus:{type:Boolean},disableOutsidePointerEvents:{type:Boolean},asChild:{type:Boolean},as:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","openAutoFocus","closeAutoFocus"],setup(e,{emit:o}){const t=e,s=Je(o);F();const a=be(),i=S(!1),l=S(!1);return(d,f)=>(v(),w(Eo,k({...t,...r(s)},{"trap-focus":!1,"disable-outside-pointer-events":!1,onCloseAutoFocus:f[0]||(f[0]=u=>{var c;u.defaultPrevented||(i.value||(c=r(a).triggerElement.value)==null||c.focus(),u.preventDefault()),i.value=!1,l.value=!1}),onInteractOutside:f[1]||(f[1]=u=>{var h;u.defaultPrevented||(i.value=!0,u.detail.originalEvent.type==="pointerdown"&&(l.value=!0));const c=u.target;((h=r(a).triggerElement.value)==null?void 0:h.contains(c))&&u.preventDefault(),u.detail.originalEvent.type==="focusin"&&l.value&&u.preventDefault()})}),{default:m(()=>[b(d.$slots,"default")]),_:3},16))}}),Ks=y({__name:"DialogContent",props:{forceMount:{type:Boolean},trapFocus:{type:Boolean},disableOutsidePointerEvents:{type:Boolean},asChild:{type:Boolean},as:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","openAutoFocus","closeAutoFocus"],setup(e,{emit:o}){const t=e,n=o,s=be(),a=Je(n),{forwardRef:i}=F();return(l,d)=>(v(),w(r(et),{present:l.forceMount||r(s).open.value},{default:m(()=>[r(s).modal.value?(v(),w(zs,k({key:0,ref:r(i)},{...t,...r(a),...l.$attrs}),{default:m(()=>[b(l.$slots,"default")]),_:3},16)):(v(),w(Ns,k({key:1,ref:r(i)},{...t,...r(a),...l.$attrs}),{default:m(()=>[b(l.$slots,"default")]),_:3},16))]),_:3},8,["present"]))}}),Ws=y({__name:"DialogDescription",props:{asChild:{type:Boolean},as:{default:"p"}},setup(e){const o=e;F();const t=be();return(n,s)=>(v(),w(r(H),k(o,{id:r(t).descriptionId}),{default:m(()=>[b(n.$slots,"default")]),_:3},16,["id"]))}});function ht(e){if(e===null||typeof e!="object")return!1;const o=Object.getPrototypeOf(e);return o!==null&&o!==Object.prototype&&Object.getPrototypeOf(o)!==null||Symbol.iterator in e?!1:Symbol.toStringTag in e?Object.prototype.toString.call(e)==="[object Module]":!0}function xt(e,o,t=".",n){if(!ht(o))return xt(e,{},t,n);const s=Object.assign({},o);for(const a in e){if(a==="__proto__"||a==="constructor")continue;const i=e[a];i!=null&&(n&&n(s,a,i,t)||(Array.isArray(i)&&Array.isArray(s[a])?s[a]=[...i,...s[a]]:ht(i)&&ht(s[a])?s[a]=xt(i,s[a],(t?`${t}.`:"")+a.toString(),n):s[a]=i))}return s}function Hs(e){return(...o)=>o.reduce((t,n)=>xt(t,n,"",e),{})}const Vs=Hs(),js=uo(()=>{const e=S(new Map),o=S(),t=E(()=>{for(const i of e.value.values())if(i)return!0;return!1}),n=yn({scrollBody:S(!0)});let s=null;const a=()=>{document.body.style.paddingRight="",document.body.style.marginRight="",document.body.style.pointerEvents="",document.documentElement.style.removeProperty("--scrollbar-width"),document.body.style.overflow=o.value??"",Kt&&(s==null||s()),o.value=void 0};return Ce(t,(i,l)=>{var c;if(!De)return;if(!i){l&&a();return}o.value===void 0&&(o.value=document.body.style.overflow);const d=window.innerWidth-document.documentElement.clientWidth,f={padding:d,margin:0},u=(c=n.scrollBody)!=null&&c.value?typeof n.scrollBody.value=="object"?Vs({padding:n.scrollBody.value.padding===!0?d:n.scrollBody.value.padding,margin:n.scrollBody.value.margin===!0?d:n.scrollBody.value.margin},f):f:{padding:0,margin:0};d>0&&(document.body.style.paddingRight=typeof u.padding=="number"?`${u.padding}px`:String(u.padding),document.body.style.marginRight=typeof u.margin=="number"?`${u.margin}px`:String(u.margin),document.documentElement.style.setProperty("--scrollbar-width",`${d}px`),document.body.style.overflow="hidden"),Kt&&(s=Me(document,"touchmove",p=>Us(p),{passive:!1})),ne(()=>{document.body.style.pointerEvents="none",document.body.style.overflow="hidden"})},{immediate:!0,flush:"sync"}),e});function To(e){const o=Math.random().toString(36).substring(2,7),t=js();t.value.set(o,e??!1);const n=E({get:()=>t.value.get(o)??!1,set:s=>t.value.set(o,s)});return pn(()=>{t.value.delete(o)}),n}function Mo(e){const o=window.getComputedStyle(e);if(o.overflowX==="scroll"||o.overflowY==="scroll"||o.overflowX==="auto"&&e.clientWidth<e.scrollWidth||o.overflowY==="auto"&&e.clientHeight<e.scrollHeight)return!0;{const t=e.parentNode;return!(t instanceof Element)||t.tagName==="BODY"?!1:Mo(t)}}function Us(e){const o=e||window.event,t=o.target;return t instanceof Element&&Mo(t)?!1:o.touches.length>1?!0:(o.preventDefault&&o.cancelable&&o.preventDefault(),!1)}const Gs=y({__name:"DialogOverlayImpl",props:{asChild:{type:Boolean},as:{}},setup(e){const o=be();return To(!0),F(),(t,n)=>(v(),w(r(H),{as:t.as,"as-child":t.asChild,"data-state":r(o).open.value?"open":"closed",style:{"pointer-events":"auto"}},{default:m(()=>[b(t.$slots,"default")]),_:3},8,["as","as-child","data-state"]))}}),Ys=y({__name:"DialogOverlay",props:{forceMount:{type:Boolean},asChild:{type:Boolean},as:{}},setup(e){const o=be(),{forwardRef:t}=F();return(n,s)=>{var a;return(a=r(o))!=null&&a.modal.value?(v(),w(r(et),{key:0,present:n.forceMount||r(o).open.value},{default:m(()=>[O(Gs,k(n.$attrs,{ref:r(t),as:n.as,"as-child":n.asChild}),{default:m(()=>[b(n.$slots,"default")]),_:3},16,["as","as-child"])]),_:3},8,["present"])):$e("",!0)}}}),Lt=y({__name:"Teleport",props:{to:{default:"body"},disabled:{type:Boolean},defer:{type:Boolean},forceMount:{type:Boolean}},setup(e){const o=mn();return(t,n)=>r(o)||t.forceMount?(v(),w(tn,{key:0,to:t.to,disabled:t.disabled,defer:t.defer},[b(t.$slots,"default")],8,["to","disabled","defer"])):$e("",!0)}}),Xs=y({__name:"DialogTitle",props:{asChild:{type:Boolean},as:{default:"h2"}},setup(e){const o=e,t=be();return F(),(n,s)=>(v(),w(r(H),k(o,{id:r(t).titleId}),{default:m(()=>[b(n.$slots,"default")]),_:3},16,["id"]))}}),[Ro,qs]=re("AvatarRoot"),Zs=y({__name:"AvatarRoot",props:{asChild:{type:Boolean},as:{default:"span"}},setup(e){return F(),qs({imageLoadingStatus:S("idle")}),(o,t)=>(v(),w(r(H),{"as-child":o.asChild,as:o.as},{default:m(()=>[b(o.$slots,"default")]),_:3},8,["as-child","as"]))}}),Qs=y({__name:"AvatarFallback",props:{delayMs:{},asChild:{type:Boolean},as:{default:"span"}},setup(e){const o=e,t=Ro();F();const n=S(o.delayMs===void 0);return te(s=>{if(o.delayMs&&De){const a=window.setTimeout(()=>{n.value=!0},o.delayMs);s(()=>{window.clearTimeout(a)})}}),(s,a)=>n.value&&r(t).imageLoadingStatus.value!=="loaded"?(v(),w(r(H),{key:0,"as-child":s.asChild,as:s.as},{default:m(()=>[b(s.$slots,"default")]),_:3},8,["as-child","as"])):$e("",!0)}});function Jt(e,o){return e?o?(e.src!==o&&(e.src=o),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}function Js(e,{referrerPolicy:o,crossOrigin:t}={}){const n=S(!1),s=S(null),a=E(()=>n.value?(!s.value&&De&&(s.value=new window.Image),s.value):null),i=S(Jt(a.value,e.value)),l=d=>()=>{n.value&&(i.value=d)};return Ae(()=>{n.value=!0,te(d=>{const f=a.value;if(!f)return;i.value=Jt(f,e.value);const u=l("loaded"),c=l("error");f.addEventListener("load",u),f.addEventListener("error",c),o!=null&&o.value&&(f.referrerPolicy=o.value),typeof(t==null?void 0:t.value)=="string"&&(f.crossOrigin=t.value),d(()=>{f.removeEventListener("load",u),f.removeEventListener("error",c)})})}),$t(()=>{n.value=!1}),i}const ea=y({__name:"AvatarImage",props:{src:{},referrerPolicy:{},crossOrigin:{},asChild:{type:Boolean},as:{default:"img"}},emits:["loadingStatusChange"],setup(e,{emit:o}){const t=e,n=o,{src:s,referrerPolicy:a,crossOrigin:i}=Fe(t);F();const l=Ro(),d=Js(s,{referrerPolicy:a,crossOrigin:i});return Ce(d,f=>{n("loadingStatusChange",f),f!=="idle"&&(l.imageLoadingStatus.value=f)},{immediate:!0}),(f,u)=>on((v(),w(r(H),{role:"img","as-child":f.asChild,as:f.as,src:r(s),"referrer-policy":r(a)},{default:m(()=>[b(f.$slots,"default")]),_:3},8,["as-child","as","src","referrer-policy"])),[[nn,r(d)==="loaded"]])}}),[Fo,ta]=re("PopperRoot"),Lo=y({inheritAttrs:!1,__name:"PopperRoot",setup(e){const o=S();return ta({anchor:o,onAnchorChange:t=>o.value=t}),(t,n)=>b(t.$slots,"default")}}),Io=y({__name:"PopperAnchor",props:{reference:{},asChild:{type:Boolean},as:{}},setup(e){const o=e,{forwardRef:t,currentElement:n}=F(),s=Fo();return ao(()=>{s.onAnchorChange(o.reference??n.value)}),(a,i)=>(v(),w(r(H),{ref:r(t),as:a.as,"as-child":a.asChild},{default:m(()=>[b(a.$slots,"default")]),_:3},8,["as","as-child"]))}}),oa={key:0,d:"M0 0L6 6L12 0"},na={key:1,d:"M0 0L4.58579 4.58579C5.36683 5.36683 6.63316 5.36684 7.41421 4.58579L12 0"},sa=y({__name:"Arrow",props:{width:{default:10},height:{default:5},rounded:{type:Boolean},asChild:{type:Boolean},as:{default:"svg"}},setup(e){const o=e;return F(),(t,n)=>(v(),w(r(H),k(o,{width:t.width,height:t.height,viewBox:t.asChild?void 0:"0 0 12 6",preserveAspectRatio:t.asChild?void 0:"none"}),{default:m(()=>[b(t.$slots,"default",{},()=>[t.rounded?(v(),L("path",na)):(v(),L("path",oa))])]),_:3},16,["width","height","viewBox","preserveAspectRatio"]))}});function aa(e){return e!==null}function ra(e){return{name:"transformOrigin",options:e,fn(o){var C,$,B;const{placement:t,rects:n,middlewareData:s}=o,i=((C=s.arrow)==null?void 0:C.centerOffset)!==0,l=i?0:e.arrowWidth,d=i?0:e.arrowHeight,[f,u]=Ot(t),c={start:"0%",center:"50%",end:"100%"}[u],p=((($=s.arrow)==null?void 0:$.x)??0)+l/2,h=(((B=s.arrow)==null?void 0:B.y)??0)+d/2;let g="",_="";return f==="bottom"?(g=i?c:`${p}px`,_=`${-d}px`):f==="top"?(g=i?c:`${p}px`,_=`${n.floating.height+d}px`):f==="right"?(g=`${-d}px`,_=i?c:`${h}px`):f==="left"&&(g=`${n.floating.width+d}px`,_=i?c:`${h}px`),{data:{x:g,y:_}}}}}function Ot(e){const[o,t="center"]=e.split("-");return[o,t]}function ia(e){const o=S(),t=E(()=>{var s;return((s=o.value)==null?void 0:s.width)??0}),n=E(()=>{var s;return((s=o.value)==null?void 0:s.height)??0});return Ae(()=>{const s=lo(e);if(s){o.value={width:s.offsetWidth,height:s.offsetHeight};const a=new ResizeObserver(i=>{if(!Array.isArray(i)||!i.length)return;const l=i[0];let d,f;if("borderBoxSize"in l){const u=l.borderBoxSize,c=Array.isArray(u)?u[0]:u;d=c.inlineSize,f=c.blockSize}else d=s.offsetWidth,f=s.offsetHeight;o.value={width:d,height:f}});return a.observe(s,{box:"border-box"}),()=>a.unobserve(s)}else o.value=void 0}),{width:t,height:n}}const zo={side:"bottom",sideOffset:0,align:"center",alignOffset:0,arrowPadding:0,avoidCollisions:!0,collisionBoundary:()=>[],collisionPadding:0,sticky:"partial",hideWhenDetached:!1,positionStrategy:"fixed",updatePositionStrategy:"optimized",prioritizePosition:!1},[la,ua]=re("PopperContent"),No=y({inheritAttrs:!1,__name:"PopperContent",props:ro({side:{},sideOffset:{},align:{},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},arrowPadding:{},sticky:{},hideWhenDetached:{type:Boolean},positionStrategy:{},updatePositionStrategy:{},disableUpdateOnLayoutShift:{type:Boolean},prioritizePosition:{type:Boolean},reference:{},asChild:{type:Boolean},as:{}},{...zo}),emits:["placed"],setup(e,{emit:o}){const t=e,n=o,s=Fo(),{forwardRef:a,currentElement:i}=F(),l=S(),d=S(),{width:f,height:u}=ia(d),c=E(()=>t.side+(t.align!=="center"?`-${t.align}`:"")),p=E(()=>typeof t.collisionPadding=="number"?t.collisionPadding:{top:0,right:0,bottom:0,left:0,...t.collisionPadding}),h=E(()=>Array.isArray(t.collisionBoundary)?t.collisionBoundary:[t.collisionBoundary]),g=E(()=>({padding:p.value,boundary:h.value.filter(aa),altBoundary:h.value.length>0})),_=hn(()=>[ns({mainAxis:t.sideOffset+u.value,alignmentAxis:t.alignOffset}),t.prioritizePosition&&t.avoidCollisions&&Yt({...g.value}),t.avoidCollisions&&ss({mainAxis:!0,crossAxis:!!t.prioritizePosition,limiter:t.sticky==="partial"?ls():void 0,...g.value}),!t.prioritizePosition&&t.avoidCollisions&&Yt({...g.value}),as({...g.value,apply:({elements:D,rects:K,availableWidth:U,availableHeight:z})=>{const{width:Y,height:q}=K.reference,j=D.floating.style;j.setProperty("--reka-popper-available-width",`${U}px`),j.setProperty("--reka-popper-available-height",`${z}px`),j.setProperty("--reka-popper-anchor-width",`${Y}px`),j.setProperty("--reka-popper-anchor-height",`${q}px`)}}),d.value&&cs({element:d.value,padding:t.arrowPadding}),ra({arrowWidth:f.value,arrowHeight:u.value}),t.hideWhenDetached&&rs({strategy:"referenceHidden",...g.value})]),C=E(()=>t.reference??s.anchor.value),{floatingStyles:$,placement:B,isPositioned:A,middlewareData:P}=fs(C,l,{strategy:t.positionStrategy,placement:c,whileElementsMounted:(...D)=>os(...D,{layoutShift:!t.disableUpdateOnLayoutShift,animationFrame:t.updatePositionStrategy==="always"}),middleware:_}),R=E(()=>Ot(B.value)[0]),V=E(()=>Ot(B.value)[1]);ao(()=>{A.value&&n("placed")});const M=E(()=>{var D;return((D=P.value.arrow)==null?void 0:D.centerOffset)!==0}),N=S("");te(()=>{i.value&&(N.value=window.getComputedStyle(i.value).zIndex)});const x=E(()=>{var D;return((D=P.value.arrow)==null?void 0:D.x)??0}),T=E(()=>{var D;return((D=P.value.arrow)==null?void 0:D.y)??0});return ua({placedSide:R,onArrowChange:D=>d.value=D,arrowX:x,arrowY:T,shouldHideArrow:M}),(D,K)=>{var U,z,Y;return v(),L("div",{ref_key:"floatingRef",ref:l,"data-reka-popper-content-wrapper":"",style:qe({...r($),transform:r(A)?r($).transform:"translate(0, -200%)",minWidth:"max-content",zIndex:N.value,"--reka-popper-transform-origin":[(U=r(P).transformOrigin)==null?void 0:U.x,(z=r(P).transformOrigin)==null?void 0:z.y].join(" "),...((Y=r(P).hide)==null?void 0:Y.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}})},[O(r(H),k({ref:r(a)},D.$attrs,{"as-child":t.asChild,as:D.as,"data-side":R.value,"data-align":V.value,style:{animation:r(A)?void 0:"none"}}),{default:m(()=>[b(D.$slots,"default")]),_:3},16,["as-child","as","data-side","data-align","style"])],4)}}}),da={top:"bottom",right:"left",bottom:"top",left:"right"},ca=y({inheritAttrs:!1,__name:"PopperArrow",props:{width:{},height:{},rounded:{type:Boolean},asChild:{type:Boolean},as:{default:"svg"}},setup(e){const{forwardRef:o}=F(),t=la(),n=E(()=>da[t.placedSide.value]);return(s,a)=>{var i,l,d,f;return v(),L("span",{ref:u=>{r(t).onArrowChange(u)},style:qe({position:"absolute",left:(i=r(t).arrowX)!=null&&i.value?`${(l=r(t).arrowX)==null?void 0:l.value}px`:void 0,top:(d=r(t).arrowY)!=null&&d.value?`${(f=r(t).arrowY)==null?void 0:f.value}px`:void 0,[n.value]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[r(t).placedSide.value],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[r(t).placedSide.value],visibility:r(t).shouldHideArrow.value?"hidden":void 0})},[O(sa,k(s.$attrs,{ref:r(o),style:{display:"block"},as:s.as,"as-child":s.asChild,rounded:s.rounded,width:s.width,height:s.height}),{default:m(()=>[b(s.$slots,"default")]),_:3},16,["as","as-child","rounded","width","height"])],4)}}});function fa(e){const o=co("",1e3);return{search:o,handleTypeaheadSearch:(s,a)=>{o.value=o.value+s;{const i=ee(),l=a.map(p=>{var h,g;return{...p,textValue:((h=p.value)==null?void 0:h.textValue)??((g=p.ref.textContent)==null?void 0:g.trim())??""}}),d=l.find(p=>p.ref===i),f=l.map(p=>p.textValue),u=ma(f,o.value,d==null?void 0:d.textValue),c=l.find(p=>p.textValue===u);return c&&c.ref.focus(),c==null?void 0:c.ref}},resetTypeahead:()=>{o.value=""}}}function pa(e,o){return e.map((t,n)=>e[(o+n)%e.length])}function ma(e,o,t){const s=o.length>1&&Array.from(o).every(f=>f===o[0])?o[0]:o,a=t?e.indexOf(t):-1;let i=pa(e,Math.max(a,0));s.length===1&&(i=i.filter(f=>f!==t));const d=i.find(f=>f.toLowerCase().startsWith(s.toLowerCase()));return d!==t?d:void 0}function ha(){const e=S(!1);return Ae(()=>{Me("keydown",()=>{e.value=!0},{capture:!0,passive:!0}),Me(["pointerdown","pointermove"],()=>{e.value=!1},{capture:!0,passive:!0})}),e}const ga=uo(ha),[at,va]=re(["MenuRoot","MenuSub"],"MenuContext"),[It,ya]=re("MenuRoot"),ba=y({__name:"MenuRoot",props:{open:{type:Boolean,default:!1},dir:{},modal:{type:Boolean,default:!0}},emits:["update:open"],setup(e,{emit:o}){const t=e,n=o,{modal:s,dir:a}=Fe(t),i=po(a),l=We(t,"open",n),d=S(),f=ga();return va({open:l,onOpenChange:u=>{l.value=u},content:d,onContentChange:u=>{d.value=u}}),ya({onClose:()=>{l.value=!1},isUsingKeyboardRef:f,dir:i,modal:s}),(u,c)=>(v(),w(r(Lo),null,{default:m(()=>[b(u.$slots,"default")]),_:3}))}});let gt=0;function wa(){te(e=>{if(!De)return;const o=document.querySelectorAll("[data-reka-focus-guard]");document.body.insertAdjacentElement("afterbegin",o[0]??eo()),document.body.insertAdjacentElement("beforeend",o[1]??eo()),gt++,e(()=>{gt===1&&document.querySelectorAll("[data-reka-focus-guard]").forEach(t=>t.remove()),gt--})})}function eo(){const e=document.createElement("span");return e.setAttribute("data-reka-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}const[Ko,_a]=re("MenuContent"),Wo=y({__name:"MenuContentImpl",props:ro({loop:{type:Boolean},disableOutsidePointerEvents:{type:Boolean},disableOutsideScroll:{type:Boolean},trapFocus:{type:Boolean},side:{},sideOffset:{},align:{},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},arrowPadding:{},sticky:{},hideWhenDetached:{type:Boolean},positionStrategy:{},updatePositionStrategy:{},disableUpdateOnLayoutShift:{type:Boolean},prioritizePosition:{type:Boolean},reference:{},asChild:{type:Boolean},as:{}},{...zo}),emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","entryFocus","openAutoFocus","closeAutoFocus","dismiss"],setup(e,{emit:o}){const t=e,n=o,s=at(),a=It(),{trapFocus:i,disableOutsidePointerEvents:l,loop:d}=Fe(t);wa(),To(l.value);const f=S(""),u=S(0),c=S(0),p=S(null),h=S("right"),g=S(0),_=S(null),C=S(),{forwardRef:$,currentElement:B}=F(),{handleTypeaheadSearch:A}=fa();Ce(B,x=>{s.onContentChange(x)}),$t(()=>{window.clearTimeout(u.value)});function P(x){var D,K;return h.value===((D=p.value)==null?void 0:D.side)&&As(x,(K=p.value)==null?void 0:K.area)}async function R(x){var T;n("openAutoFocus",x),!x.defaultPrevented&&(x.preventDefault(),(T=B.value)==null||T.focus({preventScroll:!0}))}function V(x){var j;if(x.defaultPrevented)return;const D=x.target.closest("[data-reka-menu-content]")===x.currentTarget,K=x.ctrlKey||x.altKey||x.metaKey,U=x.key.length===1,z=ms(x,ee(),B.value,{loop:d.value,arrowKeyOptions:"vertical",dir:a==null?void 0:a.dir.value,focus:!0,attributeName:"[data-reka-collection-item]:not([data-disabled])"});if(z)return z==null?void 0:z.focus();if(x.code==="Space")return;const Y=((j=C.value)==null?void 0:j.getItems())??[];if(D&&(x.key==="Tab"&&x.preventDefault(),!K&&U&&A(x.key,Y)),x.target!==B.value||!Os.includes(x.key))return;x.preventDefault();const q=[...Y.map(Z=>Z.ref)];Bo.includes(x.key)&&q.reverse(),$s(q)}function M(x){var T,D;(D=(T=x==null?void 0:x.currentTarget)==null?void 0:T.contains)!=null&&D.call(T,x.target)||(window.clearTimeout(u.value),f.value="")}function N(x){var K;if(!Ct(x))return;const T=x.target,D=g.value!==x.clientX;if((K=x==null?void 0:x.currentTarget)!=null&&K.contains(T)&&D){const U=x.clientX>g.value?"right":"left";h.value=U,g.value=x.clientX}}return _a({onItemEnter:x=>!!P(x),onItemLeave:x=>{var T;P(x)||((T=B.value)==null||T.focus(),_.value=null)},onTriggerLeave:x=>!!P(x),searchRef:f,pointerGraceTimerRef:c,onPointerGraceIntentChange:x=>{p.value=x}}),(x,T)=>(v(),w(r(Po),{"as-child":"",trapped:r(i),onMountAutoFocus:R,onUnmountAutoFocus:T[7]||(T[7]=D=>n("closeAutoFocus",D))},{default:m(()=>[O(r(Ft),{"as-child":"","disable-outside-pointer-events":r(l),onEscapeKeyDown:T[2]||(T[2]=D=>n("escapeKeyDown",D)),onPointerDownOutside:T[3]||(T[3]=D=>n("pointerDownOutside",D)),onFocusOutside:T[4]||(T[4]=D=>n("focusOutside",D)),onInteractOutside:T[5]||(T[5]=D=>n("interactOutside",D)),onDismiss:T[6]||(T[6]=D=>n("dismiss"))},{default:m(()=>[O(r(bn),{ref_key:"rovingFocusGroupRef",ref:C,"current-tab-stop-id":_.value,"onUpdate:currentTabStopId":T[0]||(T[0]=D=>_.value=D),"as-child":"",orientation:"vertical",dir:r(a).dir.value,loop:r(d),onEntryFocus:T[1]||(T[1]=D=>{n("entryFocus",D),r(a).isUsingKeyboardRef.value||D.preventDefault()})},{default:m(()=>[O(r(No),{ref:r($),role:"menu",as:x.as,"as-child":x.asChild,"aria-orientation":"vertical","data-reka-menu-content":"","data-state":r(Ao)(r(s).open.value),dir:r(a).dir.value,side:x.side,"side-offset":x.sideOffset,align:x.align,"align-offset":x.alignOffset,"avoid-collisions":x.avoidCollisions,"collision-boundary":x.collisionBoundary,"collision-padding":x.collisionPadding,"arrow-padding":x.arrowPadding,"prioritize-position":x.prioritizePosition,"position-strategy":x.positionStrategy,"update-position-strategy":x.updatePositionStrategy,sticky:x.sticky,"hide-when-detached":x.hideWhenDetached,reference:x.reference,onKeydown:V,onBlur:M,onPointermove:N},{default:m(()=>[b(x.$slots,"default")]),_:3},8,["as","as-child","data-state","dir","side","side-offset","align","align-offset","avoid-collisions","collision-boundary","collision-padding","arrow-padding","prioritize-position","position-strategy","update-position-strategy","sticky","hide-when-detached","reference"])]),_:3},8,["current-tab-stop-id","dir","loop"])]),_:3},8,["disable-outside-pointer-events"])]),_:3},8,["trapped"]))}}),Ca=y({inheritAttrs:!1,__name:"MenuItemImpl",props:{disabled:{type:Boolean},textValue:{},asChild:{type:Boolean},as:{}},setup(e){const o=e,t=Ko(),{forwardRef:n}=F(),{CollectionItem:s}=wn(),a=S(!1);async function i(d){if(!d.defaultPrevented&&Ct(d)){if(o.disabled)t.onItemLeave(d);else if(!t.onItemEnter(d)){const u=d.currentTarget;u==null||u.focus({preventScroll:!0})}}}async function l(d){await ne(),!d.defaultPrevented&&Ct(d)&&t.onItemLeave(d)}return(d,f)=>(v(),w(r(s),{value:{textValue:d.textValue}},{default:m(()=>[O(r(H),k({ref:r(n),role:"menuitem",tabindex:"-1"},d.$attrs,{as:d.as,"as-child":d.asChild,"aria-disabled":d.disabled||void 0,"data-disabled":d.disabled?"":void 0,"data-highlighted":a.value?"":void 0,onPointermove:i,onPointerleave:l,onFocus:f[0]||(f[0]=async u=>{await ne(),!(u.defaultPrevented||d.disabled)&&(a.value=!0)}),onBlur:f[1]||(f[1]=async u=>{await ne(),!u.defaultPrevented&&(a.value=!1)})}),{default:m(()=>[b(d.$slots,"default")]),_:3},16,["as","as-child","aria-disabled","data-disabled","data-highlighted"])]),_:3},8,["value"]))}}),xa=y({__name:"MenuItem",props:{disabled:{type:Boolean},textValue:{},asChild:{type:Boolean},as:{}},emits:["select"],setup(e,{emit:o}){const t=e,n=o,{forwardRef:s,currentElement:a}=F(),i=It(),l=Ko(),d=S(!1);async function f(){const u=a.value;if(!t.disabled&&u){const c=new CustomEvent(Cs,{bubbles:!0,cancelable:!0});n("select",c),await ne(),c.defaultPrevented?d.value=!1:i.onClose()}}return(u,c)=>(v(),w(Ca,k(t,{ref:r(s),onClick:f,onPointerdown:c[0]||(c[0]=()=>{d.value=!0}),onPointerup:c[1]||(c[1]=async p=>{var h;await ne(),!p.defaultPrevented&&(d.value||(h=p.currentTarget)==null||h.click())}),onKeydown:c[2]||(c[2]=async p=>{const h=r(l).searchRef.value!=="";u.disabled||h&&p.key===" "||r(_t).includes(p.key)&&(p.currentTarget.click(),p.preventDefault())})}),{default:m(()=>[b(u.$slots,"default")]),_:3},16))}}),Oa=y({__name:"MenuRootContentModal",props:{loop:{type:Boolean},side:{},sideOffset:{},align:{},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},arrowPadding:{},sticky:{},hideWhenDetached:{type:Boolean},positionStrategy:{},updatePositionStrategy:{},disableUpdateOnLayoutShift:{type:Boolean},prioritizePosition:{type:Boolean},reference:{},asChild:{type:Boolean},as:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","entryFocus","openAutoFocus","closeAutoFocus"],setup(e,{emit:o}){const t=e,n=o,s=ie(t,n),a=at(),{forwardRef:i,currentElement:l}=F();return ko(l),(d,f)=>(v(),w(Wo,k(r(s),{ref:r(i),"trap-focus":r(a).open.value,"disable-outside-pointer-events":r(a).open.value,"disable-outside-scroll":!0,onDismiss:f[0]||(f[0]=u=>r(a).onOpenChange(!1)),onFocusOutside:f[1]||(f[1]=io(u=>n("focusOutside",u),["prevent"]))}),{default:m(()=>[b(d.$slots,"default")]),_:3},16,["trap-focus","disable-outside-pointer-events"]))}}),$a=y({__name:"MenuRootContentNonModal",props:{loop:{type:Boolean},side:{},sideOffset:{},align:{},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},arrowPadding:{},sticky:{},hideWhenDetached:{type:Boolean},positionStrategy:{},updatePositionStrategy:{},disableUpdateOnLayoutShift:{type:Boolean},prioritizePosition:{type:Boolean},reference:{},asChild:{type:Boolean},as:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","entryFocus","openAutoFocus","closeAutoFocus"],setup(e,{emit:o}){const s=ie(e,o),a=at();return(i,l)=>(v(),w(Wo,k(r(s),{"trap-focus":!1,"disable-outside-pointer-events":!1,"disable-outside-scroll":!1,onDismiss:l[0]||(l[0]=d=>r(a).onOpenChange(!1))}),{default:m(()=>[b(i.$slots,"default")]),_:3},16))}}),Ba=y({__name:"MenuContent",props:{forceMount:{type:Boolean},loop:{type:Boolean},side:{},sideOffset:{},align:{},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},arrowPadding:{},sticky:{},hideWhenDetached:{type:Boolean},positionStrategy:{},updatePositionStrategy:{},disableUpdateOnLayoutShift:{type:Boolean},prioritizePosition:{type:Boolean},reference:{},asChild:{type:Boolean},as:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","entryFocus","openAutoFocus","closeAutoFocus"],setup(e,{emit:o}){const s=ie(e,o),a=at(),i=It();return(l,d)=>(v(),w(r(et),{present:l.forceMount||r(a).open.value},{default:m(()=>[r(i).modal.value?(v(),w(Oa,X(k({key:0},{...l.$attrs,...r(s)})),{default:m(()=>[b(l.$slots,"default")]),_:3},16)):(v(),w($a,X(k({key:1},{...l.$attrs,...r(s)})),{default:m(()=>[b(l.$slots,"default")]),_:3},16))]),_:3},8,["present"]))}}),Aa=y({__name:"MenuGroup",props:{asChild:{type:Boolean},as:{}},setup(e){const o=e;return(t,n)=>(v(),w(r(H),k({role:"group"},o),{default:m(()=>[b(t.$slots,"default")]),_:3},16))}}),Da=y({__name:"MenuLabel",props:{asChild:{type:Boolean},as:{default:"div"}},setup(e){const o=e;return(t,n)=>(v(),w(r(H),X(oe(o)),{default:m(()=>[b(t.$slots,"default")]),_:3},16))}}),Pa=y({__name:"MenuPortal",props:{to:{},disabled:{type:Boolean},defer:{type:Boolean},forceMount:{type:Boolean}},setup(e){const o=e;return(t,n)=>(v(),w(r(Lt),X(oe(o)),{default:m(()=>[b(t.$slots,"default")]),_:3},16))}}),Ea=y({__name:"MenuSeparator",props:{asChild:{type:Boolean},as:{}},setup(e){const o=e;return(t,n)=>(v(),w(r(H),k(o,{role:"separator","aria-orientation":"horizontal"}),{default:m(()=>[b(t.$slots,"default")]),_:3},16))}}),Sa=y({__name:"MenuAnchor",props:{reference:{},asChild:{type:Boolean},as:{}},setup(e){const o=e;return(t,n)=>(v(),w(r(Io),X(oe(o)),{default:m(()=>[b(t.$slots,"default")]),_:3},16))}}),ka=y({__name:"DialogPortal",props:{to:{},disabled:{type:Boolean},defer:{type:Boolean},forceMount:{type:Boolean}},setup(e){const o=e;return(t,n)=>(v(),w(r(Lt),X(oe(o)),{default:m(()=>[b(t.$slots,"default")]),_:3},16))}}),[Ho,Ta]=re("DropdownMenuRoot"),Ma=y({__name:"DropdownMenuRoot",props:{defaultOpen:{type:Boolean},open:{type:Boolean,default:void 0},dir:{},modal:{type:Boolean,default:!0}},emits:["update:open"],setup(e,{emit:o}){const t=e,n=o;F();const s=We(t,"open",n,{defaultValue:t.defaultOpen,passive:t.open===void 0}),a=S(),{modal:i,dir:l}=Fe(t),d=po(l);return Ta({open:s,onOpenChange:f=>{s.value=f},onOpenToggle:()=>{s.value=!s.value},triggerId:"",triggerElement:a,contentId:"",modal:i,dir:d}),(f,u)=>(v(),w(r(ba),{open:r(s),"onUpdate:open":u[0]||(u[0]=c=>sn(s)?s.value=c:null),dir:r(d),modal:r(i)},{default:m(()=>[b(f.$slots,"default",{open:r(s)})]),_:3},8,["open","dir","modal"]))}}),Ra=y({__name:"DropdownMenuContent",props:{forceMount:{type:Boolean},loop:{type:Boolean},side:{},sideOffset:{},align:{},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},arrowPadding:{},sticky:{},hideWhenDetached:{type:Boolean},positionStrategy:{},updatePositionStrategy:{},disableUpdateOnLayoutShift:{type:Boolean},prioritizePosition:{type:Boolean},reference:{},asChild:{type:Boolean},as:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","closeAutoFocus"],setup(e,{emit:o}){const s=ie(e,o);F();const a=Ho(),i=S(!1);function l(d){d.defaultPrevented||(i.value||setTimeout(()=>{var f;(f=a.triggerElement.value)==null||f.focus()},0),i.value=!1,d.preventDefault())}return a.contentId||(a.contentId=ze(void 0,"reka-dropdown-menu-content")),(d,f)=>{var u;return v(),w(r(Ba),k(r(s),{id:r(a).contentId,"aria-labelledby":(u=r(a))==null?void 0:u.triggerId,style:{"--reka-dropdown-menu-content-transform-origin":"var(--reka-popper-transform-origin)","--reka-dropdown-menu-content-available-width":"var(--reka-popper-available-width)","--reka-dropdown-menu-content-available-height":"var(--reka-popper-available-height)","--reka-dropdown-menu-trigger-width":"var(--reka-popper-anchor-width)","--reka-dropdown-menu-trigger-height":"var(--reka-popper-anchor-height)"},onCloseAutoFocus:l,onInteractOutside:f[0]||(f[0]=c=>{var _;if(c.defaultPrevented)return;const p=c.detail.originalEvent,h=p.button===0&&p.ctrlKey===!0,g=p.button===2||h;(!r(a).modal.value||g)&&(i.value=!0),(_=r(a).triggerElement.value)!=null&&_.contains(c.target)&&c.preventDefault()})}),{default:m(()=>[b(d.$slots,"default")]),_:3},16,["id","aria-labelledby"])}}}),Fa=y({__name:"DropdownMenuGroup",props:{asChild:{type:Boolean},as:{}},setup(e){const o=e;return F(),(t,n)=>(v(),w(r(Aa),X(oe(o)),{default:m(()=>[b(t.$slots,"default")]),_:3},16))}}),La=y({__name:"DropdownMenuItem",props:{disabled:{type:Boolean},textValue:{},asChild:{type:Boolean},as:{}},emits:["select"],setup(e,{emit:o}){const t=e,s=Je(o);return F(),(a,i)=>(v(),w(r(xa),X(oe({...t,...r(s)})),{default:m(()=>[b(a.$slots,"default")]),_:3},16))}}),Ia=y({__name:"DropdownMenuLabel",props:{asChild:{type:Boolean},as:{}},setup(e){const o=e;return F(),(t,n)=>(v(),w(r(Da),X(oe(o)),{default:m(()=>[b(t.$slots,"default")]),_:3},16))}}),za=y({__name:"DropdownMenuPortal",props:{to:{},disabled:{type:Boolean},defer:{type:Boolean},forceMount:{type:Boolean}},setup(e){const o=e;return(t,n)=>(v(),w(r(Pa),X(oe(o)),{default:m(()=>[b(t.$slots,"default")]),_:3},16))}}),Na=y({__name:"DropdownMenuSeparator",props:{asChild:{type:Boolean},as:{}},setup(e){const o=e;return F(),(t,n)=>(v(),w(r(Ea),X(oe(o)),{default:m(()=>[b(t.$slots,"default")]),_:3},16))}}),Ka=y({__name:"DropdownMenuTrigger",props:{disabled:{type:Boolean},asChild:{type:Boolean},as:{default:"button"}},setup(e){const o=e,t=Ho(),{forwardRef:n,currentElement:s}=F();return Ae(()=>{t.triggerElement=s}),t.triggerId||(t.triggerId=ze(void 0,"reka-dropdown-menu-trigger")),(a,i)=>(v(),w(r(Sa),{"as-child":""},{default:m(()=>[O(r(H),{id:r(t).triggerId,ref:r(n),type:a.as==="button"?"button":void 0,"as-child":o.asChild,as:a.as,"aria-haspopup":"menu","aria-expanded":r(t).open.value,"aria-controls":r(t).open.value?r(t).contentId:void 0,"data-disabled":a.disabled?"":void 0,disabled:a.disabled,"data-state":r(t).open.value?"open":"closed",onClick:i[0]||(i[0]=async l=>{var d;!a.disabled&&l.button===0&&l.ctrlKey===!1&&((d=r(t))==null||d.onOpenToggle(),await ne(),r(t).open.value&&l.preventDefault())}),onKeydown:i[1]||(i[1]=an(l=>{a.disabled||(["Enter"," "].includes(l.key)&&r(t).onOpenToggle(),l.key==="ArrowDown"&&r(t).onOpenChange(!0),["Enter"," ","ArrowDown"].includes(l.key)&&l.preventDefault())},["enter","space","arrow-down"]))},{default:m(()=>[b(a.$slots,"default")]),_:3},8,["id","type","as-child","as","aria-expanded","aria-controls","data-disabled","disabled","data-state"])]),_:3}))}});function Wa(e,o){const t=co(!1,300),n=S(null),s=gn();function a(){n.value=null,t.value=!1}function i(l,d){const f=l.currentTarget,u={x:l.clientX,y:l.clientY},c=Ha(u,f.getBoundingClientRect()),p=Va(u,c),h=ja(d.getBoundingClientRect()),g=Ga([...p,...h]);n.value=g,t.value=!0}return te(l=>{if(e.value&&o.value){const d=u=>i(u,o.value),f=u=>i(u,e.value);e.value.addEventListener("pointerleave",d),o.value.addEventListener("pointerleave",f),l(()=>{var u,c;(u=e.value)==null||u.removeEventListener("pointerleave",d),(c=o.value)==null||c.removeEventListener("pointerleave",f)})}}),te(l=>{var d;if(n.value){const f=u=>{var C,$;if(!n.value||!(u.target instanceof HTMLElement))return;const c=u.target,p={x:u.clientX,y:u.clientY},h=((C=e.value)==null?void 0:C.contains(c))||(($=o.value)==null?void 0:$.contains(c)),g=!Ua(p,n.value),_=!!c.closest("[data-grace-area-trigger]");h?a():(g||_)&&(a(),s.trigger())};(d=e.value)==null||d.ownerDocument.addEventListener("pointermove",f),l(()=>{var u;return(u=e.value)==null?void 0:u.ownerDocument.removeEventListener("pointermove",f)})}}),{isPointerInTransit:t,onPointerExit:s.on}}function Ha(e,o){const t=Math.abs(o.top-e.y),n=Math.abs(o.bottom-e.y),s=Math.abs(o.right-e.x),a=Math.abs(o.left-e.x);switch(Math.min(t,n,s,a)){case a:return"left";case s:return"right";case t:return"top";case n:return"bottom";default:throw new Error("unreachable")}}function Va(e,o,t=5){const n=[];switch(o){case"top":n.push({x:e.x-t,y:e.y+t},{x:e.x+t,y:e.y+t});break;case"bottom":n.push({x:e.x-t,y:e.y-t},{x:e.x+t,y:e.y-t});break;case"left":n.push({x:e.x+t,y:e.y-t},{x:e.x+t,y:e.y+t});break;case"right":n.push({x:e.x-t,y:e.y-t},{x:e.x-t,y:e.y+t});break}return n}function ja(e){const{top:o,right:t,bottom:n,left:s}=e;return[{x:s,y:o},{x:t,y:o},{x:t,y:n},{x:s,y:n}]}function Ua(e,o){const{x:t,y:n}=e;let s=!1;for(let a=0,i=o.length-1;a<o.length;i=a++){const l=o[a].x,d=o[a].y,f=o[i].x,u=o[i].y;d>n!=u>n&&t<(f-l)*(n-d)/(u-d)+l&&(s=!s)}return s}function Ga(e){const o=e.slice();return o.sort((t,n)=>t.x<n.x?-1:t.x>n.x?1:t.y<n.y?-1:t.y>n.y?1:0),Ya(o)}function Ya(e){if(e.length<=1)return e.slice();const o=[];for(let n=0;n<e.length;n++){const s=e[n];for(;o.length>=2;){const a=o[o.length-1],i=o[o.length-2];if((a.x-i.x)*(s.y-i.y)>=(a.y-i.y)*(s.x-i.x))o.pop();else break}o.push(s)}o.pop();const t=[];for(let n=e.length-1;n>=0;n--){const s=e[n];for(;t.length>=2;){const a=t[t.length-1],i=t[t.length-2];if((a.x-i.x)*(s.y-i.y)>=(a.y-i.y)*(s.x-i.x))t.pop();else break}t.push(s)}return t.pop(),o.length===1&&t.length===1&&o[0].x===t[0].x&&o[0].y===t[0].y?o:o.concat(t)}const Xa=y({__name:"TooltipArrow",props:{width:{default:10},height:{default:5},asChild:{type:Boolean},as:{default:"svg"}},setup(e){const o=e;return F(),(t,n)=>(v(),w(r(ca),X(oe(o)),{default:m(()=>[b(t.$slots,"default")]),_:3},16))}}),Vo="tooltip.open",[zt,qa]=re("TooltipProvider"),Za=y({inheritAttrs:!1,__name:"TooltipProvider",props:{delayDuration:{default:700},skipDelayDuration:{default:300},disableHoverableContent:{type:Boolean,default:!1},disableClosingTrigger:{type:Boolean},disabled:{type:Boolean},ignoreNonKeyboardFocus:{type:Boolean,default:!1}},setup(e){const o=e,{delayDuration:t,skipDelayDuration:n,disableHoverableContent:s,disableClosingTrigger:a,ignoreNonKeyboardFocus:i,disabled:l}=Fe(o);F();const d=S(!0),f=S(!1),{start:u,stop:c}=fo(()=>{d.value=!0},n,{immediate:!1});return qa({isOpenDelayed:d,delayDuration:t,onOpen(){c(),d.value=!1},onClose(){u()},isPointerInTransitRef:f,disableHoverableContent:s,disableClosingTrigger:a,disabled:l,ignoreNonKeyboardFocus:i}),(p,h)=>b(p.$slots,"default")}}),[rt,Qa]=re("TooltipRoot"),Ja=y({__name:"TooltipRoot",props:{defaultOpen:{type:Boolean,default:!1},open:{type:Boolean,default:void 0},delayDuration:{default:void 0},disableHoverableContent:{type:Boolean,default:void 0},disableClosingTrigger:{type:Boolean,default:void 0},disabled:{type:Boolean,default:void 0},ignoreNonKeyboardFocus:{type:Boolean,default:void 0}},emits:["update:open"],setup(e,{emit:o}){const t=e,n=o;F();const s=zt(),a=E(()=>t.disableHoverableContent??s.disableHoverableContent.value),i=E(()=>t.disableClosingTrigger??s.disableClosingTrigger.value),l=E(()=>t.disabled??s.disabled.value),d=E(()=>t.delayDuration??s.delayDuration.value),f=E(()=>t.ignoreNonKeyboardFocus??s.ignoreNonKeyboardFocus.value),u=We(t,"open",n,{defaultValue:t.defaultOpen,passive:t.open===void 0});Ce(u,A=>{s.onClose&&(A?(s.onOpen(),document.dispatchEvent(new CustomEvent(Vo))):s.onClose())});const c=S(!1),p=S(),h=E(()=>u.value?c.value?"delayed-open":"instant-open":"closed"),{start:g,stop:_}=fo(()=>{c.value=!0,u.value=!0},d,{immediate:!1});function C(){_(),c.value=!1,u.value=!0}function $(){_(),u.value=!1}function B(){g()}return Qa({contentId:"",open:u,stateAttribute:h,trigger:p,onTriggerChange(A){p.value=A},onTriggerEnter(){s.isOpenDelayed.value?B():C()},onTriggerLeave(){a.value?$():_()},onOpen:C,onClose:$,disableHoverableContent:a,disableClosingTrigger:i,disabled:l,ignoreNonKeyboardFocus:f}),(A,P)=>(v(),w(r(Lo),null,{default:m(()=>[b(A.$slots,"default",{open:r(u)})]),_:3}))}}),jo=y({__name:"TooltipContentImpl",props:{ariaLabel:{},asChild:{type:Boolean},as:{},side:{default:"top"},sideOffset:{default:0},align:{default:"center"},alignOffset:{},avoidCollisions:{type:Boolean,default:!0},collisionBoundary:{default:()=>[]},collisionPadding:{default:0},arrowPadding:{default:0},sticky:{default:"partial"},hideWhenDetached:{type:Boolean,default:!1},positionStrategy:{},updatePositionStrategy:{}},emits:["escapeKeyDown","pointerDownOutside"],setup(e,{emit:o}){const t=e,n=o,s=rt(),{forwardRef:a}=F(),i=rn(),l=E(()=>{var u;return(u=i.default)==null?void 0:u.call(i,{})}),d=E(()=>{var p;if(t.ariaLabel)return t.ariaLabel;let u="";function c(h){typeof h.children=="string"&&h.type!==ln?u+=h.children:Array.isArray(h.children)&&h.children.forEach(g=>c(g))}return(p=l.value)==null||p.forEach(h=>c(h)),u}),f=E(()=>{const{ariaLabel:u,...c}=t;return c});return Ae(()=>{Me(window,"scroll",u=>{const c=u.target;c!=null&&c.contains(s.trigger.value)&&s.onClose()}),Me(window,Vo,s.onClose)}),(u,c)=>(v(),w(r(Ft),{"as-child":"","disable-outside-pointer-events":!1,onEscapeKeyDown:c[0]||(c[0]=p=>n("escapeKeyDown",p)),onPointerDownOutside:c[1]||(c[1]=p=>{var h;r(s).disableClosingTrigger.value&&((h=r(s).trigger.value)!=null&&h.contains(p.target))&&p.preventDefault(),n("pointerDownOutside",p)}),onFocusOutside:c[2]||(c[2]=io(()=>{},["prevent"])),onDismiss:c[3]||(c[3]=p=>r(s).onClose())},{default:m(()=>[O(r(No),k({ref:r(a),"data-state":r(s).stateAttribute.value},{...u.$attrs,...f.value},{style:{"--reka-tooltip-content-transform-origin":"var(--reka-popper-transform-origin)","--reka-tooltip-content-available-width":"var(--reka-popper-available-width)","--reka-tooltip-content-available-height":"var(--reka-popper-available-height)","--reka-tooltip-trigger-width":"var(--reka-popper-anchor-width)","--reka-tooltip-trigger-height":"var(--reka-popper-anchor-height)"}}),{default:m(()=>[b(u.$slots,"default"),O(r(_n),{id:r(s).contentId,role:"tooltip"},{default:m(()=>[de(me(d.value),1)]),_:1},8,["id"])]),_:3},16,["data-state"])]),_:3}))}}),er=y({__name:"TooltipContentHoverable",props:{ariaLabel:{},asChild:{type:Boolean},as:{},side:{},sideOffset:{},align:{},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},arrowPadding:{},sticky:{},hideWhenDetached:{type:Boolean},positionStrategy:{},updatePositionStrategy:{}},setup(e){const t=tt(e),{forwardRef:n,currentElement:s}=F(),{trigger:a,onClose:i}=rt(),l=zt(),{isPointerInTransit:d,onPointerExit:f}=Wa(a,s);return l.isPointerInTransitRef=d,f(()=>{i()}),(u,c)=>(v(),w(jo,k({ref:r(n)},r(t)),{default:m(()=>[b(u.$slots,"default")]),_:3},16))}}),tr=y({__name:"TooltipContent",props:{forceMount:{type:Boolean},ariaLabel:{},asChild:{type:Boolean},as:{},side:{default:"top"},sideOffset:{},align:{},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},arrowPadding:{},sticky:{},hideWhenDetached:{type:Boolean},positionStrategy:{},updatePositionStrategy:{}},emits:["escapeKeyDown","pointerDownOutside"],setup(e,{emit:o}){const t=e,n=o,s=rt(),a=ie(t,n),{forwardRef:i}=F();return(l,d)=>(v(),w(r(et),{present:l.forceMount||r(s).open.value},{default:m(()=>[(v(),w(Ze(r(s).disableHoverableContent.value?jo:er),k({ref:r(i)},r(a)),{default:m(()=>[b(l.$slots,"default")]),_:3},16))]),_:3},8,["present"]))}}),or=y({__name:"TooltipPortal",props:{to:{},disabled:{type:Boolean},defer:{type:Boolean},forceMount:{type:Boolean}},setup(e){const o=e;return(t,n)=>(v(),w(r(Lt),X(oe(o)),{default:m(()=>[b(t.$slots,"default")]),_:3},16))}}),nr=y({__name:"TooltipTrigger",props:{reference:{},asChild:{type:Boolean},as:{default:"button"}},setup(e){const o=e,t=rt(),n=zt();t.contentId||(t.contentId=ze(void 0,"reka-tooltip-content"));const{forwardRef:s,currentElement:a}=F(),i=S(!1),l=S(!1),d=E(()=>t.disabled.value?{}:{click:_,focus:h,pointermove:c,pointerleave:p,pointerdown:u,blur:g});Ae(()=>{t.onTriggerChange(a.value)});function f(){setTimeout(()=>{i.value=!1},1)}function u(){t.open&&!t.disableClosingTrigger.value&&t.onClose(),i.value=!0,document.addEventListener("pointerup",f,{once:!0})}function c(C){C.pointerType!=="touch"&&!l.value&&!n.isPointerInTransitRef.value&&(t.onTriggerEnter(),l.value=!0)}function p(){t.onTriggerLeave(),l.value=!1}function h(C){var $,B;i.value||t.ignoreNonKeyboardFocus.value&&!((B=($=C.target).matches)!=null&&B.call($,":focus-visible"))||t.onOpen()}function g(){t.onClose()}function _(){t.disableClosingTrigger.value||t.onClose()}return(C,$)=>(v(),w(r(Io),{"as-child":"",reference:C.reference},{default:m(()=>[O(r(H),k({ref:r(s),"aria-describedby":r(t).open.value?r(t).contentId:void 0,"data-state":r(t).stateAttribute.value,as:C.as,"as-child":o.asChild,"data-grace-area-trigger":""},un(d.value)),{default:m(()=>[b(C.$slots,"default")]),_:3},16,["aria-describedby","data-state","as","as-child"])]),_:3},8,["reference"]))}}),sr=y({__name:"Sheet",props:{open:{type:Boolean},defaultOpen:{type:Boolean},modal:{type:Boolean}},emits:["update:open"],setup(e,{emit:o}){const s=ie(e,o);return(a,i)=>(v(),w(r(gs),k({"data-slot":"sheet"},r(s)),{default:m(()=>[b(a.$slots,"default")]),_:3},16))}});/**
 * @license lucide-vue-next v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ar=ye("BookOpenIcon",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]]);/**
 * @license lucide-vue-next v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const rr=ye("ChevronRightIcon",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]);/**
 * @license lucide-vue-next v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ir=ye("ChevronsUpDownIcon",[["path",{d:"m7 15 5 5 5-5",key:"1hf1tw"}],["path",{d:"m7 9 5-5 5 5",key:"sgt6xg"}]]);/**
 * @license lucide-vue-next v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const lr=ye("FolderIcon",[["path",{d:"M20 20a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.9a2 2 0 0 1-1.69-.9L9.6 3.9A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13a2 2 0 0 0 2 2Z",key:"1kt360"}]]);/**
 * @license lucide-vue-next v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ur=ye("LayoutGridIcon",[["rect",{width:"7",height:"7",x:"3",y:"3",rx:"1",key:"1g98yp"}],["rect",{width:"7",height:"7",x:"14",y:"3",rx:"1",key:"6d4xhi"}],["rect",{width:"7",height:"7",x:"14",y:"14",rx:"1",key:"nxv5o0"}],["rect",{width:"7",height:"7",x:"3",y:"14",rx:"1",key:"1bb6yr"}]]);/**
 * @license lucide-vue-next v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const dr=ye("LogOutIcon",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]]);/**
 * @license lucide-vue-next v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const cr=ye("PanelLeftIcon",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M9 3v18",key:"fh3hqa"}]]);/**
 * @license lucide-vue-next v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const fr=ye("SettingsIcon",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);/**
 * @license lucide-vue-next v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const pr=ye("XIcon",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),mr=y({__name:"SheetOverlay",props:{forceMount:{type:Boolean},asChild:{type:Boolean},as:{type:[String,Object,Function]},class:{}},setup(e){const o=e,t=E(()=>{const{class:n,...s}=o;return s});return(n,s)=>(v(),w(r(Ys),k({"data-slot":"sheet-overlay",class:r(I)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/80",o.class)},t.value),{default:m(()=>[b(n.$slots,"default")]),_:3},16,["class"]))}}),hr=y({inheritAttrs:!1,__name:"SheetContent",props:{class:{},side:{default:"right"},forceMount:{type:Boolean},trapFocus:{type:Boolean},disableOutsidePointerEvents:{type:Boolean},asChild:{type:Boolean},as:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","openAutoFocus","closeAutoFocus"],setup(e,{emit:o}){const t=e,n=o,s=Qe(t,"class","side"),a=ie(s,n);return(i,l)=>(v(),w(r(ka),null,{default:m(()=>[O(mr),O(r(Ks),k({"data-slot":"sheet-content",class:r(I)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500",i.side==="right"&&"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm",i.side==="left"&&"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm",i.side==="top"&&"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b",i.side==="bottom"&&"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t",t.class)},{...r(a),...i.$attrs}),{default:m(()=>[b(i.$slots,"default"),O(r(vs),{class:"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none"},{default:m(()=>[O(r(pr),{class:"size-4"}),l[0]||(l[0]=G("span",{class:"sr-only"},"Close",-1))]),_:1,__:[0]})]),_:3},16,["class"])]),_:3}))}}),gr=y({__name:"SheetDescription",props:{asChild:{type:Boolean},as:{type:[String,Object,Function]},class:{}},setup(e){const o=e,t=E(()=>{const{class:n,...s}=o;return s});return(n,s)=>(v(),w(r(Ws),k({"data-slot":"sheet-description",class:r(I)("text-muted-foreground text-sm",o.class)},t.value),{default:m(()=>[b(n.$slots,"default")]),_:3},16,["class"]))}}),vr=y({__name:"SheetHeader",props:{class:{}},setup(e){const o=e;return(t,n)=>(v(),L("div",{"data-slot":"sheet-header",class:W(r(I)("flex flex-col gap-1.5 p-4",o.class))},[b(t.$slots,"default")],2))}}),yr=y({__name:"SheetTitle",props:{asChild:{type:Boolean},as:{type:[String,Object,Function]},class:{}},setup(e){const o=e,t=E(()=>{const{class:n,...s}=o;return s});return(n,s)=>(v(),w(r(Xs),k({"data-slot":"sheet-title",class:r(I)("text-foreground font-semibold",o.class)},t.value),{default:m(()=>[b(n.$slots,"default")]),_:3},16,["class"]))}}),br="sidebar_state",wr=60*60*24*7,_r="16rem",Cr="18rem",xr="3rem",Or="b",[it,$r]=re("Sidebar"),Br={class:"flex h-full w-full flex-col"},Ar=["data-state","data-collapsible","data-variant","data-side"],Dr={"data-sidebar":"sidebar",class:"bg-sidebar group-data-[variant=floating]:border-sidebar-border flex h-full w-full flex-col group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:shadow-sm"},Pr=y({inheritAttrs:!1,__name:"Sidebar",props:{side:{default:"left"},variant:{default:"sidebar"},collapsible:{default:"offcanvas"},class:{}},setup(e){const o=e,{isMobile:t,state:n,openMobile:s,setOpenMobile:a}=it();return(i,l)=>i.collapsible==="none"?(v(),L("div",k({key:0,"data-slot":"sidebar",class:r(I)("bg-sidebar text-sidebar-foreground flex h-full w-(--sidebar-width) flex-col",o.class)},i.$attrs),[b(i.$slots,"default")],16)):r(t)?(v(),w(r(sr),k({key:1,open:r(s)},i.$attrs,{"onUpdate:open":r(a)}),{default:m(()=>[O(r(hr),{"data-sidebar":"sidebar","data-slot":"sidebar","data-mobile":"true",side:i.side,class:"bg-sidebar text-sidebar-foreground w-(--sidebar-width) p-0 [&>button]:hidden",style:qe({"--sidebar-width":r(Cr)})},{default:m(()=>[O(vr,{class:"sr-only"},{default:m(()=>[O(yr,null,{default:m(()=>l[0]||(l[0]=[de("Sidebar")])),_:1,__:[0]}),O(gr,null,{default:m(()=>l[1]||(l[1]=[de("Displays the mobile sidebar.")])),_:1,__:[1]})]),_:1}),G("div",Br,[b(i.$slots,"default")])]),_:3},8,["side","style"])]),_:3},16,["open","onUpdate:open"])):(v(),L("div",{key:2,class:"group peer text-sidebar-foreground hidden md:block","data-slot":"sidebar","data-state":r(n),"data-collapsible":r(n)==="collapsed"?i.collapsible:"","data-variant":i.variant,"data-side":i.side},[G("div",{class:W(r(I)("relative w-(--sidebar-width) bg-transparent transition-[width] duration-200 ease-linear","group-data-[collapsible=offcanvas]:w-0","group-data-[side=right]:rotate-180",i.variant==="floating"||i.variant==="inset"?"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4)))]":"group-data-[collapsible=icon]:w-(--sidebar-width-icon)"))},null,2),G("div",k({class:r(I)("fixed inset-y-0 z-10 hidden h-svh w-(--sidebar-width) transition-[left,right,width] duration-200 ease-linear md:flex",i.side==="left"?"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]":"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]",i.variant==="floating"||i.variant==="inset"?"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4))+2px)]":"group-data-[collapsible=icon]:w-(--sidebar-width-icon) group-data-[side=left]:border-r group-data-[side=right]:border-l",o.class)},i.$attrs),[G("div",Dr,[b(i.$slots,"default")])],16)],8,Ar))}}),Er=y({__name:"SidebarContent",props:{class:{}},setup(e){const o=e;return(t,n)=>(v(),L("div",{"data-slot":"sidebar-content","data-sidebar":"content",class:W(r(I)("flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden",o.class))},[b(t.$slots,"default")],2))}}),Sr=y({__name:"SidebarFooter",props:{class:{}},setup(e){const o=e;return(t,n)=>(v(),L("div",{"data-slot":"sidebar-footer","data-sidebar":"footer",class:W(r(I)("flex flex-col gap-2 p-2",o.class))},[b(t.$slots,"default")],2))}}),Uo=y({__name:"SidebarGroup",props:{class:{}},setup(e){const o=e;return(t,n)=>(v(),L("div",{"data-slot":"sidebar-group","data-sidebar":"group",class:W(r(I)("relative flex w-full min-w-0 flex-col p-2",o.class))},[b(t.$slots,"default")],2))}}),kr=y({__name:"SidebarGroupContent",props:{class:{}},setup(e){const o=e;return(t,n)=>(v(),L("div",{"data-slot":"sidebar-group-content","data-sidebar":"group-content",class:W(r(I)("w-full text-sm",o.class))},[b(t.$slots,"default")],2))}}),Tr=y({__name:"SidebarGroupLabel",props:{asChild:{type:Boolean},as:{type:[String,Object,Function]},class:{}},setup(e){const o=e;return(t,n)=>(v(),w(r(H),{"data-slot":"sidebar-group-label","data-sidebar":"group-label",as:t.as,"as-child":t.asChild,class:W(r(I)("text-sidebar-foreground/70 ring-sidebar-ring flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium outline-hidden transition-[margin,opacity] duration-200 ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0","group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0",o.class))},{default:m(()=>[b(t.$slots,"default")]),_:3},8,["as","as-child","class"]))}}),Mr=y({__name:"SidebarHeader",props:{class:{}},setup(e){const o=e;return(t,n)=>(v(),L("div",{"data-slot":"sidebar-header","data-sidebar":"header",class:W(r(I)("flex flex-col gap-2 p-2",o.class))},[b(t.$slots,"default")],2))}}),Rr=y({__name:"SidebarInset",props:{class:{}},setup(e){const o=e;return(t,n)=>(v(),L("main",{"data-slot":"sidebar-inset",class:W(r(I)("bg-background relative flex w-full flex-1 flex-col","md:peer-data-[variant=inset]:m-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow-sm md:peer-data-[variant=inset]:peer-data-[state=collapsed]:ml-0",o.class))},[b(t.$slots,"default")],2))}}),lt=y({__name:"SidebarMenu",props:{class:{}},setup(e){const o=e;return(t,n)=>(v(),L("ul",{"data-slot":"sidebar-menu","data-sidebar":"menu",class:W(r(I)("flex w-full min-w-0 flex-col gap-1",o.class))},[b(t.$slots,"default")],2))}}),Fr=y({__name:"Tooltip",props:{defaultOpen:{type:Boolean},open:{type:Boolean},delayDuration:{},disableHoverableContent:{type:Boolean},disableClosingTrigger:{type:Boolean},disabled:{type:Boolean},ignoreNonKeyboardFocus:{type:Boolean}},emits:["update:open"],setup(e,{emit:o}){const s=ie(e,o);return(a,i)=>(v(),w(r(Ja),k({"data-slot":"tooltip"},r(s)),{default:m(()=>[b(a.$slots,"default")]),_:3},16))}}),Lr=y({inheritAttrs:!1,__name:"TooltipContent",props:{forceMount:{type:Boolean},ariaLabel:{},asChild:{type:Boolean},as:{},side:{},sideOffset:{default:4},align:{},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},arrowPadding:{},sticky:{},hideWhenDetached:{type:Boolean},positionStrategy:{},updatePositionStrategy:{},class:{}},emits:["escapeKeyDown","pointerDownOutside"],setup(e,{emit:o}){const t=e,n=o,s=Qe(t,"class"),a=ie(s,n);return(i,l)=>(v(),w(r(or),null,{default:m(()=>[O(r(tr),k({"data-slot":"tooltip-content"},{...r(a),...i.$attrs},{class:r(I)("bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit rounded-md px-3 py-1.5 text-xs text-balance",t.class)}),{default:m(()=>[b(i.$slots,"default"),O(r(Xa),{class:"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]"})]),_:3},16,["class"])]),_:3}))}}),Ir=y({__name:"TooltipTrigger",props:{reference:{},asChild:{type:Boolean},as:{type:[String,Object,Function]}},setup(e){const o=e;return(t,n)=>(v(),w(r(nr),k({"data-slot":"tooltip-trigger"},o),{default:m(()=>[b(t.$slots,"default")]),_:3},16))}}),to=y({__name:"SidebarMenuButtonChild",props:{variant:{default:"default"},size:{default:"default"},isActive:{type:Boolean},class:{},asChild:{type:Boolean},as:{type:[String,Object,Function],default:"button"}},setup(e){const o=e;return(t,n)=>(v(),w(r(H),k({"data-slot":"sidebar-menu-button","data-sidebar":"menu-button","data-size":t.size,"data-active":t.isActive,class:r(I)(r(Kr)({variant:t.variant,size:t.size}),o.class),as:t.as,"as-child":t.asChild},t.$attrs),{default:m(()=>[b(t.$slots,"default")]),_:3},16,["data-size","data-active","class","as","as-child"]))}}),ut=y({inheritAttrs:!1,__name:"SidebarMenuButton",props:{variant:{default:"default"},size:{default:"default"},isActive:{type:Boolean},class:{},asChild:{type:Boolean},as:{type:[String,Object,Function],default:"button"},tooltip:{}},setup(e){const o=e,{isMobile:t,state:n}=it(),s=E(()=>{const{tooltip:a,...i}=o;return i});return(a,i)=>a.tooltip?(v(),w(r(Fr),{key:1},{default:m(()=>[O(r(Ir),{"as-child":""},{default:m(()=>[O(to,X(oe({...s.value,...a.$attrs})),{default:m(()=>[b(a.$slots,"default")]),_:3},16)]),_:3}),O(r(Lr),{side:"right",align:"center",hidden:r(n)!=="collapsed"||r(t)},{default:m(()=>[typeof a.tooltip=="string"?(v(),L(he,{key:0},[de(me(a.tooltip),1)],64)):(v(),w(Ze(a.tooltip),{key:1}))]),_:1},8,["hidden"])]),_:3})):(v(),w(to,X(k({key:0},{...s.value,...a.$attrs})),{default:m(()=>[b(a.$slots,"default")]),_:3},16))}}),dt=y({__name:"SidebarMenuItem",props:{class:{}},setup(e){const o=e;return(t,n)=>(v(),L("li",{"data-slot":"sidebar-menu-item","data-sidebar":"menu-item",class:W(r(I)("group/menu-item relative",o.class))},[b(t.$slots,"default")],2))}}),zr=y({__name:"SidebarProvider",props:{defaultOpen:{type:Boolean,default:!0},open:{type:Boolean,default:void 0},class:{}},emits:["update:open"],setup(e,{emit:o}){const t=e,n=o,s=vn("(max-width: 768px)"),a=S(!1),i=We(t,"open",n,{defaultValue:t.defaultOpen??!1,passive:t.open===void 0});function l(c){i.value=c,document.cookie=`${br}=${i.value}; path=/; max-age=${wr}`}function d(c){a.value=c}function f(){return s.value?d(!a.value):l(!i.value)}Me("keydown",c=>{c.key===Or&&(c.metaKey||c.ctrlKey)&&(c.preventDefault(),f())});const u=E(()=>i.value?"expanded":"collapsed");return $r({state:u,open:i,setOpen:l,isMobile:s,openMobile:a,setOpenMobile:d,toggleSidebar:f}),(c,p)=>(v(),w(r(Za),{"delay-duration":0},{default:m(()=>[G("div",k({"data-slot":"sidebar-wrapper",style:{"--sidebar-width":r(_r),"--sidebar-width-icon":r(xr)},class:r(I)("group/sidebar-wrapper has-data-[variant=inset]:bg-sidebar flex min-h-svh w-full",t.class)},c.$attrs),[b(c.$slots,"default")],16)]),_:3}))}}),Nr=y({__name:"SidebarTrigger",props:{class:{}},setup(e){const o=e,{toggleSidebar:t}=it();return(n,s)=>(v(),w(r(Xo),{"data-sidebar":"trigger","data-slot":"sidebar-trigger",variant:"ghost",size:"icon",class:W(r(I)("h-7 w-7",o.class)),onClick:r(t)},{default:m(()=>[O(r(cr)),s[0]||(s[0]=G("span",{class:"sr-only"},"Toggle Sidebar",-1))]),_:1,__:[0]},8,["class","onClick"]))}}),Kr=qo("peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-hidden ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-data-[sidebar=menu-action]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:size-8! group-data-[collapsible=icon]:pr-2! [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0",{variants:{variant:{default:"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",outline:"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]"},size:{default:"h-8 text-sm",sm:"h-7 text-xs",lg:"h-12 text-sm group-data-[collapsible=icon]:p-0!"}},defaultVariants:{variant:"default",size:"default"}}),Wr=y({__name:"AppContent",props:{variant:{},class:{}},setup(e){const o=e,t=E(()=>o.class);return(n,s)=>o.variant==="sidebar"?(v(),w(r(Rr),{key:0,class:W(t.value)},{default:m(()=>[b(n.$slots,"default")]),_:3},8,["class"])):(v(),L("main",{key:1,class:W(["mx-auto flex h-full w-full max-w-7xl flex-1 flex-col gap-4 rounded-xl",t.value])},[b(n.$slots,"default")],2))}}),Hr={key:0,class:"flex min-h-screen w-full flex-col"},Vr=y({__name:"AppShell",props:{variant:{}},setup(e){const o=Bt().props.sidebarOpen;return(t,n)=>t.variant==="header"?(v(),L("div",Hr,[b(t.$slots,"default")])):(v(),w(r(zr),{key:1,"default-open":r(o)},{default:m(()=>[b(t.$slots,"default")]),_:3},8,["default-open"]))}}),jr=["href"],Ur=y({__name:"NavFooter",props:{items:{},class:{}},setup(e){return(o,t)=>(v(),w(r(Uo),{class:W(`group-data-[collapsible=icon]:p-0 ${o.$props.class||""}`)},{default:m(()=>[O(r(kr),null,{default:m(()=>[O(r(lt),null,{default:m(()=>[(v(!0),L(he,null,At(o.items,n=>(v(),w(r(dt),{key:n.title},{default:m(()=>[O(r(ut),{class:"text-neutral-600 hover:text-neutral-800 dark:text-neutral-300 dark:hover:text-neutral-100","as-child":""},{default:m(()=>[G("a",{href:n.href,target:"_blank",rel:"noopener noreferrer"},[(v(),w(Ze(n.icon))),G("span",null,me(n.title),1)],8,jr)]),_:2},1024)]),_:2},1024))),128))]),_:1})]),_:1})]),_:1},8,["class"]))}}),Gr=y({__name:"NavMain",props:{items:{}},setup(e){const o=Bt();return(t,n)=>(v(),w(r(Uo),{class:"px-2 py-0"},{default:m(()=>[O(r(Tr),null,{default:m(()=>n[0]||(n[0]=[de("Platform")])),_:1,__:[0]}),O(r(lt),null,{default:m(()=>[(v(!0),L(he,null,At(t.items,s=>(v(),w(r(dt),{key:s.title},{default:m(()=>[O(r(ut),{"as-child":"","is-active":s.href===r(o).url,tooltip:s.title},{default:m(()=>[O(r(Ie),{href:s.href},{default:m(()=>[(v(),w(Ze(s.icon))),G("span",null,me(s.title),1)]),_:2},1032,["href"])]),_:2},1032,["is-active","tooltip"])]),_:2},1024))),128))]),_:1})]),_:1}))}}),Yr=y({__name:"Avatar",props:{class:{}},setup(e){const o=e;return(t,n)=>(v(),w(r(Zs),{"data-slot":"avatar",class:W(r(I)("relative flex size-8 shrink-0 overflow-hidden rounded-full",o.class))},{default:m(()=>[b(t.$slots,"default")]),_:3},8,["class"]))}}),Xr=y({__name:"AvatarFallback",props:{delayMs:{},asChild:{type:Boolean},as:{type:[String,Object,Function]},class:{}},setup(e){const o=e,t=E(()=>{const{class:n,...s}=o;return s});return(n,s)=>(v(),w(r(Qs),k({"data-slot":"avatar-fallback"},t.value,{class:r(I)("bg-muted flex size-full items-center justify-center rounded-full",o.class)}),{default:m(()=>[b(n.$slots,"default")]),_:3},16,["class"]))}}),qr=y({__name:"AvatarImage",props:{src:{},referrerPolicy:{},crossOrigin:{},asChild:{type:Boolean},as:{type:[String,Object,Function]}},setup(e){const o=e;return(t,n)=>(v(),w(r(ea),k({"data-slot":"avatar-image"},o,{class:"aspect-square size-full"}),{default:m(()=>[b(t.$slots,"default")]),_:3},16))}});function Zr(e){if(!e)return"";const o=e.trim().split(" ");return o.length===0?"":o.length===1?o[0].charAt(0).toUpperCase():`${o[0].charAt(0)}${o[o.length-1].charAt(0)}`.toUpperCase()}function Qr(){return{getInitials:Zr}}const Jr={class:"grid flex-1 text-left text-sm leading-tight"},ei={class:"truncate font-medium"},ti={key:0,class:"truncate text-xs text-muted-foreground"},Go=y({__name:"UserInfo",props:{user:{},showEmail:{type:Boolean,default:!1}},setup(e){const o=e,{getInitials:t}=Qr(),n=E(()=>o.user.avatar&&o.user.avatar!=="");return(s,a)=>(v(),L(he,null,[O(r(Yr),{class:"h-8 w-8 overflow-hidden rounded-lg"},{default:m(()=>[n.value?(v(),w(r(qr),{key:0,src:s.user.avatar,alt:s.user.name},null,8,["src","alt"])):$e("",!0),O(r(Xr),{class:"rounded-lg text-black dark:text-white"},{default:m(()=>[de(me(r(t)(s.user.name)),1)]),_:1})]),_:1}),G("div",Jr,[G("span",ei,me(s.user.name),1),s.showEmail?(v(),L("span",ti,me(s.user.email),1)):$e("",!0)])],64))}}),oi=y({__name:"DropdownMenu",props:{defaultOpen:{type:Boolean},open:{type:Boolean},dir:{},modal:{type:Boolean}},emits:["update:open"],setup(e,{emit:o}){const s=ie(e,o);return(a,i)=>(v(),w(r(Ma),k({"data-slot":"dropdown-menu"},r(s)),{default:m(()=>[b(a.$slots,"default")]),_:3},16))}}),ni=y({__name:"DropdownMenuContent",props:{forceMount:{type:Boolean},loop:{type:Boolean},side:{},sideOffset:{default:4},align:{},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},arrowPadding:{},sticky:{},hideWhenDetached:{type:Boolean},positionStrategy:{},updatePositionStrategy:{},disableUpdateOnLayoutShift:{type:Boolean},prioritizePosition:{type:Boolean},reference:{},asChild:{type:Boolean},as:{},class:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","closeAutoFocus"],setup(e,{emit:o}){const t=e,n=o,s=E(()=>{const{class:i,...l}=t;return l}),a=ie(s,n);return(i,l)=>(v(),w(r(za),null,{default:m(()=>[O(r(Ra),k({"data-slot":"dropdown-menu-content"},r(a),{class:r(I)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--reka-dropdown-menu-content-available-height) min-w-[8rem] origin-(--reka-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",t.class)}),{default:m(()=>[b(i.$slots,"default")]),_:3},16,["class"])]),_:3}))}}),si=y({__name:"DropdownMenuGroup",props:{asChild:{type:Boolean},as:{type:[String,Object,Function]}},setup(e){const o=e;return(t,n)=>(v(),w(r(Fa),k({"data-slot":"dropdown-menu-group"},o),{default:m(()=>[b(t.$slots,"default")]),_:3},16))}}),oo=y({__name:"DropdownMenuItem",props:{disabled:{type:Boolean},textValue:{},asChild:{type:Boolean},as:{},class:{},inset:{type:Boolean},variant:{default:"default"}},setup(e){const o=e,t=Qe(o,"inset","variant"),n=tt(t);return(s,a)=>(v(),w(r(La),k({"data-slot":"dropdown-menu-item","data-inset":s.inset?"":void 0,"data-variant":s.variant},r(n),{class:r(I)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive-foreground data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/40 data-[variant=destructive]:focus:text-destructive-foreground data-[variant=destructive]:*:[svg]:!text-destructive-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",o.class)}),{default:m(()=>[b(s.$slots,"default")]),_:3},16,["data-inset","data-variant","class"]))}}),ai=y({__name:"DropdownMenuLabel",props:{asChild:{type:Boolean},as:{type:[String,Object,Function]},class:{},inset:{type:Boolean}},setup(e){const o=e,t=Qe(o,"class","inset"),n=tt(t);return(s,a)=>(v(),w(r(Ia),k({"data-slot":"dropdown-menu-label","data-inset":s.inset?"":void 0},r(n),{class:r(I)("px-2 py-1.5 text-sm font-medium data-[inset]:pl-8",o.class)}),{default:m(()=>[b(s.$slots,"default")]),_:3},16,["data-inset","class"]))}}),no=y({__name:"DropdownMenuSeparator",props:{asChild:{type:Boolean},as:{type:[String,Object,Function]},class:{}},setup(e){const o=e,t=E(()=>{const{class:n,...s}=o;return s});return(n,s)=>(v(),w(r(Na),k({"data-slot":"dropdown-menu-separator"},t.value,{class:r(I)("bg-border -mx-1 my-1 h-px",o.class)}),null,16,["class"]))}}),ri=y({__name:"DropdownMenuTrigger",props:{disabled:{type:Boolean},asChild:{type:Boolean},as:{type:[String,Object,Function]}},setup(e){const t=tt(e);return(n,s)=>(v(),w(r(Ka),k({"data-slot":"dropdown-menu-trigger"},r(t)),{default:m(()=>[b(n.$slots,"default")]),_:3},16))}}),ii={class:"flex items-center gap-2 px-1 py-1.5 text-left text-sm"},li=y({__name:"UserMenuContent",props:{user:{}},setup(e){const o=()=>{dn.flushAll()};return(t,n)=>(v(),L(he,null,[O(r(ai),{class:"p-0 font-normal"},{default:m(()=>[G("div",ii,[O(Go,{user:t.user,"show-email":!0},null,8,["user"])])]),_:1}),O(r(no)),O(r(si),null,{default:m(()=>[O(r(oo),{"as-child":!0},{default:m(()=>[O(r(Ie),{class:"block w-full",href:t.route("profile.edit"),prefetch:"",as:"button"},{default:m(()=>[O(r(fr),{class:"mr-2 h-4 w-4"}),n[0]||(n[0]=de(" Settings "))]),_:1,__:[0]},8,["href"])]),_:1})]),_:1}),O(r(no)),O(r(oo),{"as-child":!0},{default:m(()=>[O(r(Ie),{class:"block w-full",method:"post",href:t.route("logout"),onClick:o,as:"button"},{default:m(()=>[O(r(dr),{class:"mr-2 h-4 w-4"}),n[1]||(n[1]=de(" Log out "))]),_:1,__:[1]},8,["href"])]),_:1})],64))}}),ui=y({__name:"NavUser",setup(e){const t=Bt().props.auth.user,{isMobile:n,state:s}=it();return(a,i)=>(v(),w(r(lt),null,{default:m(()=>[O(r(dt),null,{default:m(()=>[O(r(oi),null,{default:m(()=>[O(r(ri),{"as-child":""},{default:m(()=>[O(r(ut),{size:"lg",class:"data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"},{default:m(()=>[O(Go,{user:r(t)},null,8,["user"]),O(r(ir),{class:"ml-auto size-4"})]),_:1})]),_:1}),O(r(ni),{class:"w-(--reka-dropdown-menu-trigger-width) min-w-56 rounded-lg",side:r(n)?"bottom":r(s)==="collapsed"?"left":"bottom",align:"end","side-offset":4},{default:m(()=>[O(li,{user:r(t)},null,8,["user"])]),_:1},8,["side"])]),_:1})]),_:1})]),_:1}))}}),di={class:"flex aspect-square size-8 items-center justify-center rounded-md bg-sidebar-primary text-sidebar-primary-foreground"},ci=y({__name:"AppLogo",setup(e){return(o,t)=>(v(),L(he,null,[G("div",di,[O(Zo,{class:"size-5 fill-current text-white dark:text-black"})]),t[0]||(t[0]=G("div",{class:"ml-1 grid flex-1 text-left text-sm"},[G("span",{class:"mb-0.5 truncate leading-tight font-semibold"},"Laravel Starter Kit")],-1))],64))}}),fi=y({__name:"AppSidebar",setup(e){const o=[{title:"Dashboard",href:"/dashboard",icon:ur}],t=[{title:"Github Repo",href:"https://github.com/laravel/vue-starter-kit",icon:lr},{title:"Documentation",href:"https://laravel.com/docs/starter-kits#vue",icon:ar}];return(n,s)=>(v(),L(he,null,[O(r(Pr),{collapsible:"icon",variant:"inset"},{default:m(()=>[O(r(Mr),null,{default:m(()=>[O(r(lt),null,{default:m(()=>[O(r(dt),null,{default:m(()=>[O(r(ut),{size:"lg","as-child":""},{default:m(()=>[O(r(Ie),{href:n.route("dashboard")},{default:m(()=>[O(ci)]),_:1},8,["href"])]),_:1})]),_:1})]),_:1})]),_:1}),O(r(Er),null,{default:m(()=>[O(Gr,{items:o})]),_:1}),O(r(Sr),null,{default:m(()=>[O(Ur,{items:t}),O(ui)]),_:1})]),_:1}),b(n.$slots,"default")],64))}}),pi=y({__name:"Breadcrumb",props:{class:{}},setup(e){const o=e;return(t,n)=>(v(),L("nav",{"aria-label":"breadcrumb","data-slot":"breadcrumb",class:W(o.class)},[b(t.$slots,"default")],2))}}),mi=y({__name:"BreadcrumbItem",props:{class:{}},setup(e){const o=e;return(t,n)=>(v(),L("li",{"data-slot":"breadcrumb-item",class:W(r(I)("inline-flex items-center gap-1.5",o.class))},[b(t.$slots,"default")],2))}}),hi=y({__name:"BreadcrumbLink",props:{asChild:{type:Boolean},as:{type:[String,Object,Function],default:"a"},class:{}},setup(e){const o=e;return(t,n)=>(v(),w(r(H),{"data-slot":"breadcrumb-link",as:t.as,"as-child":t.asChild,class:W(r(I)("hover:text-foreground transition-colors",o.class))},{default:m(()=>[b(t.$slots,"default")]),_:3},8,["as","as-child","class"]))}}),gi=y({__name:"BreadcrumbList",props:{class:{}},setup(e){const o=e;return(t,n)=>(v(),L("ol",{"data-slot":"breadcrumb-list",class:W(r(I)("text-muted-foreground flex flex-wrap items-center gap-1.5 text-sm break-words sm:gap-2.5",o.class))},[b(t.$slots,"default")],2))}}),vi=y({__name:"BreadcrumbPage",props:{class:{}},setup(e){const o=e;return(t,n)=>(v(),L("span",{"data-slot":"breadcrumb-page",role:"link","aria-disabled":"true","aria-current":"page",class:W(r(I)("text-foreground font-normal",o.class))},[b(t.$slots,"default")],2))}}),yi=y({__name:"BreadcrumbSeparator",props:{class:{}},setup(e){const o=e;return(t,n)=>(v(),L("li",{"data-slot":"breadcrumb-separator",role:"presentation","aria-hidden":"true",class:W(r(I)("[&>svg]:size-3.5",o.class))},[b(t.$slots,"default",{},()=>[O(r(rr))])],2))}}),bi=y({__name:"Breadcrumbs",props:{breadcrumbs:{}},setup(e){return(o,t)=>(v(),w(r(pi),null,{default:m(()=>[O(r(gi),null,{default:m(()=>[(v(!0),L(he,null,At(o.breadcrumbs,(n,s)=>(v(),L(he,{key:s},[O(r(mi),null,{default:m(()=>[s===o.breadcrumbs.length-1?(v(),w(r(vi),{key:0},{default:m(()=>[de(me(n.title),1)]),_:2},1024)):(v(),w(r(hi),{key:1,"as-child":""},{default:m(()=>[O(r(Ie),{href:n.href??"#"},{default:m(()=>[de(me(n.title),1)]),_:2},1032,["href"])]),_:2},1024))]),_:2},1024),s!==o.breadcrumbs.length-1?(v(),w(r(yi),{key:0})):$e("",!0)],64))),128))]),_:1})]),_:1}))}}),wi={class:"flex h-16 shrink-0 items-center gap-2 border-b border-sidebar-border/70 px-6 transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-12 md:px-4"},_i={class:"flex items-center gap-2"},Ci=y({__name:"AppSidebarHeader",props:{breadcrumbs:{default:()=>[]}},setup(e){return(o,t)=>(v(),L("header",wi,[G("div",_i,[O(r(Nr),{class:"-ml-1"}),o.breadcrumbs&&o.breadcrumbs.length>0?(v(),w(bi,{key:0,breadcrumbs:o.breadcrumbs},null,8,["breadcrumbs"])):$e("",!0)])]))}}),xi=y({__name:"AppSidebarLayout",props:{breadcrumbs:{default:()=>[]}},setup(e){return(o,t)=>(v(),w(Vr,{variant:"sidebar"},{default:m(()=>[O(fi),O(Wr,{variant:"sidebar",class:"overflow-x-hidden"},{default:m(()=>[O(Ci,{breadcrumbs:o.breadcrumbs},null,8,["breadcrumbs"]),b(o.$slots,"default")]),_:3})]),_:3}))}}),Di=y({__name:"AppLayout",props:{breadcrumbs:{default:()=>[]}},setup(e){return(o,t)=>(v(),w(xi,{breadcrumbs:o.breadcrumbs},{default:m(()=>[b(o.$slots,"default")]),_:3},8,["breadcrumbs"]))}});export{pr as X,Di as _,gs as a,vs as b,Ys as c,ka as d,Ks as e,Ws as f,Xs as g,be as i};
