{"version": 3, "file": "TooltipTrigger.js", "sources": ["../../src/Tooltip/TooltipTrigger.vue"], "sourcesContent": ["<script lang=\"ts\">\nimport { useForwardExpose, useId } from '@/shared'\n\nexport type TooltipTriggerDataState =\n  | 'closed'\n  | 'delayed-open'\n  | 'instant-open'\n\nexport interface TooltipTriggerProps extends PopperAnchorProps {}\n</script>\n\n<script setup lang=\"ts\">\nimport type { PopperAnchorProps } from '@/Popper'\nimport { computed, onMounted, ref } from 'vue'\nimport { PopperAnchor } from '@/Popper'\nimport {\n  Primitive,\n} from '@/Primitive'\nimport { injectTooltipProviderContext } from './TooltipProvider.vue'\nimport { injectTooltipRootContext } from './TooltipRoot.vue'\n\nconst props = withDefaults(defineProps<TooltipTriggerProps>(), {\n  as: 'button',\n})\nconst rootContext = injectTooltipRootContext()\nconst providerContext = injectTooltipProviderContext()\n\nrootContext.contentId ||= useId(undefined, 'reka-tooltip-content')\n\nconst { forwardRef, currentElement: triggerElement } = useForwardExpose()\n\nconst isPointerDown = ref(false)\nconst hasPointerMoveOpened = ref(false)\n\nconst tooltipListeners = computed(() => {\n  if (rootContext.disabled.value)\n    return {}\n\n  return {\n    click: handleClick,\n    focus: handleFocus,\n    pointermove: handlePointerMove,\n    pointerleave: handlePointerLeave,\n    pointerdown: handlePointerDown,\n    blur: handleBlur,\n  }\n})\n\nonMounted(() => {\n  rootContext.onTriggerChange(triggerElement.value)\n})\n\nfunction handlePointerUp() {\n  setTimeout(() => {\n    isPointerDown.value = false\n  }, 1)\n}\n\nfunction handlePointerDown() {\n  if (rootContext.open && !rootContext.disableClosingTrigger.value) {\n    rootContext.onClose()\n  }\n  isPointerDown.value = true\n  document.addEventListener('pointerup', handlePointerUp, { once: true })\n}\n\nfunction handlePointerMove(event: PointerEvent) {\n  if (event.pointerType === 'touch')\n    return\n  if (\n    !hasPointerMoveOpened.value && !providerContext.isPointerInTransitRef.value\n  ) {\n    rootContext.onTriggerEnter()\n    hasPointerMoveOpened.value = true\n  }\n}\n\nfunction handlePointerLeave() {\n  rootContext.onTriggerLeave()\n  hasPointerMoveOpened.value = false\n}\n\nfunction handleFocus(event: FocusEvent) {\n  if (isPointerDown.value)\n    return\n\n  if (rootContext.ignoreNonKeyboardFocus.value && !(event.target as HTMLElement).matches?.(':focus-visible'))\n    return\n\n  rootContext.onOpen()\n}\n\nfunction handleBlur() {\n  rootContext.onClose()\n}\n\nfunction handleClick() {\n  if (!rootContext.disableClosingTrigger.value)\n    rootContext.onClose()\n}\n</script>\n\n<template>\n  <PopperAnchor\n    as-child\n    :reference=\"reference\"\n  >\n    <Primitive\n      :ref=\"forwardRef\"\n      :aria-describedby=\"\n        rootContext.open.value ? rootContext.contentId : undefined\n      \"\n      :data-state=\"rootContext.stateAttribute.value\"\n      :as=\"as\"\n      :as-child=\"props.asChild\"\n      data-grace-area-trigger\n      v-on=\"tooltipListeners\"\n    >\n      <slot />\n    </Primitive>\n  </PopperAnchor>\n</template>\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAqBA,IAAA,MAAM,KAAQ,GAAA,OAAA;AAGd,IAAA,MAAM,cAAc,wBAAyB,EAAA;AAC7C,IAAA,MAAM,kBAAkB,4BAA6B,EAAA;AAErD,IAAY,WAAA,CAAA,SAAA,KAAc,KAAM,CAAA,MAAA,EAAW,sBAAsB,CAAA;AAEjE,IAAA,MAAM,EAAE,UAAA,EAAY,cAAgB,EAAA,cAAA,KAAmB,gBAAiB,EAAA;AAExE,IAAM,MAAA,aAAA,GAAgB,IAAI,KAAK,CAAA;AAC/B,IAAM,MAAA,oBAAA,GAAuB,IAAI,KAAK,CAAA;AAEtC,IAAM,MAAA,gBAAA,GAAmB,SAAS,MAAM;AACtC,MAAA,IAAI,YAAY,QAAS,CAAA,KAAA;AACvB,QAAA,OAAO,EAAC;AAEV,MAAO,OAAA;AAAA,QACL,KAAO,EAAA,WAAA;AAAA,QACP,KAAO,EAAA,WAAA;AAAA,QACP,WAAa,EAAA,iBAAA;AAAA,QACb,YAAc,EAAA,kBAAA;AAAA,QACd,WAAa,EAAA,iBAAA;AAAA,QACb,IAAM,EAAA;AAAA,OACR;AAAA,KACD,CAAA;AAED,IAAA,SAAA,CAAU,MAAM;AACd,MAAY,WAAA,CAAA,eAAA,CAAgB,eAAe,KAAK,CAAA;AAAA,KACjD,CAAA;AAED,IAAA,SAAS,eAAkB,GAAA;AACzB,MAAA,UAAA,CAAW,MAAM;AACf,QAAA,aAAA,CAAc,KAAQ,GAAA,KAAA;AAAA,SACrB,CAAC,CAAA;AAAA;AAGN,IAAA,SAAS,iBAAoB,GAAA;AAC3B,MAAA,IAAI,WAAY,CAAA,IAAA,IAAQ,CAAC,WAAA,CAAY,sBAAsB,KAAO,EAAA;AAChE,QAAA,WAAA,CAAY,OAAQ,EAAA;AAAA;AAEtB,MAAA,aAAA,CAAc,KAAQ,GAAA,IAAA;AACtB,MAAA,QAAA,CAAS,iBAAiB,WAAa,EAAA,eAAA,EAAiB,EAAE,IAAA,EAAM,MAAM,CAAA;AAAA;AAGxE,IAAA,SAAS,kBAAkB,KAAqB,EAAA;AAC9C,MAAA,IAAI,MAAM,WAAgB,KAAA,OAAA;AACxB,QAAA;AACF,MAAA,IACE,CAAC,oBAAqB,CAAA,KAAA,IAAS,CAAC,eAAA,CAAgB,sBAAsB,KACtE,EAAA;AACA,QAAA,WAAA,CAAY,cAAe,EAAA;AAC3B,QAAA,oBAAA,CAAqB,KAAQ,GAAA,IAAA;AAAA;AAC/B;AAGF,IAAA,SAAS,kBAAqB,GAAA;AAC5B,MAAA,WAAA,CAAY,cAAe,EAAA;AAC3B,MAAA,oBAAA,CAAqB,KAAQ,GAAA,KAAA;AAAA;AAG/B,IAAA,SAAS,YAAY,KAAmB,EAAA;AACtC,MAAA,IAAI,aAAc,CAAA,KAAA;AAChB,QAAA;AAEF,MAAA,IAAI,YAAY,sBAAuB,CAAA,KAAA,IAAS,CAAE,KAAM,CAAA,MAAA,CAAuB,UAAU,gBAAgB,CAAA;AACvG,QAAA;AAEF,MAAA,WAAA,CAAY,MAAO,EAAA;AAAA;AAGrB,IAAA,SAAS,UAAa,GAAA;AACpB,MAAA,WAAA,CAAY,OAAQ,EAAA;AAAA;AAGtB,IAAA,SAAS,WAAc,GAAA;AACrB,MAAI,IAAA,CAAC,YAAY,qBAAsB,CAAA,KAAA;AACrC,QAAA,WAAA,CAAY,OAAQ,EAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}