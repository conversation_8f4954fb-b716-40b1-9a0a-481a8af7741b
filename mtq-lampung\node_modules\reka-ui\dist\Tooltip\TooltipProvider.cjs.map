{"version": 3, "file": "TooltipProvider.cjs", "sources": ["../../src/Tooltip/TooltipProvider.vue"], "sourcesContent": ["<script lang=\"ts\">\nimport type { Ref } from 'vue'\nimport { createContext, useForwardExpose } from '@/shared'\n\ninterface TooltipProviderContext {\n  isOpenDelayed: Ref<boolean>\n  delayDuration: Ref<number>\n  onOpen: () => void\n  onClose: () => void\n  isPointerInTransitRef: Ref<boolean>\n  disableHoverableContent: Ref<boolean>\n  disableClosingTrigger: Ref<boolean>\n  disabled: Ref<boolean>\n  ignoreNonKeyboardFocus: Ref<boolean>\n}\n\nexport const [injectTooltipProviderContext, provideTooltipProviderContext]\n  = createContext<TooltipProviderContext>('TooltipProvider')\n\nexport interface TooltipProviderProps {\n  /**\n   * The duration from when the pointer enters the trigger until the tooltip gets opened.\n   * @defaultValue 700\n   */\n  delayDuration?: number\n  /**\n   * How much time a user has to enter another trigger without incurring a delay again.\n   * @defaultValue 300\n   */\n  skipDelayDuration?: number\n  /**\n   * When `true`, trying to hover the content will result in the tooltip closing as the pointer leaves the trigger.\n   * @defaultValue false\n   */\n  disableHoverableContent?: boolean\n  /**\n   * When `true`, clicking on trigger will not close the content.\n   * @defaultValue false\n   */\n  disableClosingTrigger?: boolean\n  /**\n   * When `true`, disable tooltip\n   * @defaultValue false\n   */\n  disabled?: boolean\n  /**\n   * Prevent the tooltip from opening if the focus did not come from\n   * the keyboard by matching against the `:focus-visible` selector.\n   * This is useful if you want to avoid opening it when switching\n   * browser tabs or closing a dialog.\n   * @defaultValue false\n   */\n  ignoreNonKeyboardFocus?: boolean\n}\n</script>\n\n<script setup lang=\"ts\">\nimport { useTimeoutFn } from '@vueuse/shared'\nimport { ref, toRefs } from 'vue'\n\ndefineOptions({\n  inheritAttrs: false,\n})\n\nconst props = withDefaults(defineProps<TooltipProviderProps>(), {\n  delayDuration: 700,\n  skipDelayDuration: 300,\n  disableHoverableContent: false,\n  ignoreNonKeyboardFocus: false,\n})\nconst { delayDuration, skipDelayDuration, disableHoverableContent, disableClosingTrigger, ignoreNonKeyboardFocus, disabled } = toRefs(props)\nuseForwardExpose()\n\nconst isOpenDelayed = ref(true)\n// Reset the inTransit state if idle/scrolled.\nconst isPointerInTransitRef = ref(false)\n\nconst { start: startTimer, stop: clearTimer } = useTimeoutFn(() => {\n  isOpenDelayed.value = true\n}, skipDelayDuration, { immediate: false })\n\nprovideTooltipProviderContext({\n  isOpenDelayed,\n  delayDuration,\n  onOpen() {\n    clearTimer()\n    isOpenDelayed.value = false\n  },\n  onClose() {\n    startTimer()\n  },\n  isPointerInTransitRef,\n  disableHoverableContent,\n  disableClosingTrigger,\n  disabled,\n  ignoreNonKeyboardFocus,\n})\n</script>\n\n<template>\n  <slot />\n</template>\n"], "names": ["createContext", "toRefs", "useForwardExpose", "ref", "useTimeoutFn"], "mappings": ";;;;;;;AAgBO,MAAM,CAAC,4BAAA,EAA8B,6BAA6B,CAAA,GACrEA,mCAAsC,iBAAiB;;;;;;;;;;;;;;;AA+C3D,IAAA,MAAM,KAAQ,GAAA,OAAA;AAMd,IAAM,MAAA,EAAE,eAAe,iBAAmB,EAAA,uBAAA,EAAyB,uBAAuB,sBAAwB,EAAA,QAAA,EAAa,GAAAC,UAAA,CAAO,KAAK,CAAA;AAC3I,IAAiBC,wCAAA,EAAA;AAEjB,IAAM,MAAA,aAAA,GAAgBC,QAAI,IAAI,CAAA;AAE9B,IAAM,MAAA,qBAAA,GAAwBA,QAAI,KAAK,CAAA;AAEvC,IAAA,MAAM,EAAE,KAAO,EAAA,UAAA,EAAY,MAAM,UAAW,EAAA,GAAIC,oBAAa,MAAM;AACjE,MAAA,aAAA,CAAc,KAAQ,GAAA,IAAA;AAAA,KACrB,EAAA,iBAAA,EAAmB,EAAE,SAAA,EAAW,OAAO,CAAA;AAE1C,IAA8B,6BAAA,CAAA;AAAA,MAC5B,aAAA;AAAA,MACA,aAAA;AAAA,MACA,MAAS,GAAA;AACP,QAAW,UAAA,EAAA;AACX,QAAA,aAAA,CAAc,KAAQ,GAAA,KAAA;AAAA,OACxB;AAAA,MACA,OAAU,GAAA;AACR,QAAW,UAAA,EAAA;AAAA,OACb;AAAA,MACA,qBAAA;AAAA,MACA,uBAAA;AAAA,MACA,qBAAA;AAAA,MACA,QAAA;AAAA,MACA;AAAA,KACD,CAAA;;;;;;;;;;"}