{"version": 3, "file": "VisuallyHiddenInput.js", "sources": ["../../src/VisuallyHidden/VisuallyHiddenInput.vue"], "sourcesContent": ["<script setup lang=\"ts\" generic=\"T\">\nimport type { VisuallyHiddenInputBubbleProps } from './VisuallyHiddenInputBubble.vue'\nimport { computed } from 'vue'\nimport VisuallyHiddenInputBubble from './VisuallyHiddenInputBubble.vue'\n\ndefineOptions({\n  inheritAttrs: false,\n})\n\nconst props = withDefaults(defineProps<VisuallyHiddenInputBubbleProps<T>>(), {\n  feature: 'fully-hidden',\n  checked: undefined,\n})\n\nconst isFormArrayEmptyAndRequired = computed(() =>\n  typeof props.value === 'object'\n  && Array.isArray(props.value)\n  && props.value.length === 0\n  && props.required,\n)\n\nconst parsedValue = computed(() => {\n  // if primitive value\n  if (typeof props.value === 'string' || typeof props.value === 'number' || typeof props.value === 'boolean') {\n    return [{ name: props.name, value: props.value }]\n  }\n\n  // if array value\n  else if (typeof props.value === 'object' && Array.isArray(props.value)) {\n    return props.value.flatMap((obj, index) => {\n      // if item in array is object\n      if (typeof obj === 'object')\n        return Object.entries(obj).map(([key, value]) => ({ name: `[${props.name}][${index}][${key}]`, value }))\n      // if item in array is not object, may be primitive\n      else\n        return ({ name: `[${props.name}][${index}]`, value: obj })\n    })\n  }\n\n  // if object value\n  else if (props.value !== null && typeof props.value === 'object' && !Array.isArray(props.value)) {\n    return Object.entries(props.value as object).map(([key, value]) => ({ name: `[${props.name}][${key}]`, value }))\n  }\n\n  return []\n})\n</script>\n\n<template>\n  <!-- We render single input if it's required -->\n  <VisuallyHiddenInputBubble\n    v-if=\"isFormArrayEmptyAndRequired\"\n    :key=\"name\"\n    v-bind=\"{ ...props, ...$attrs }\"\n    :name=\"name\"\n    :value=\"value\"\n  />\n\n  <VisuallyHiddenInputBubble\n    v-for=\"parsed in parsedValue\"\n    v-else\n    :key=\"parsed.name\"\n    v-bind=\"{ ...props, ...$attrs }\"\n    :name=\"parsed.name\"\n    :value=\"parsed.value\"\n  />\n</template>\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AASA,IAAA,MAAM,KAAQ,GAAA,OAAA;AAKd,IAAA,MAAM,2BAA8B,GAAA,QAAA;AAAA,MAAS,MAC3C,OAAO,KAAM,CAAA,KAAA,KAAU,YACpB,KAAM,CAAA,OAAA,CAAQ,KAAM,CAAA,KAAK,CACzB,IAAA,KAAA,CAAM,KAAM,CAAA,MAAA,KAAW,KACvB,KAAM,CAAA;AAAA,KACX;AAEA,IAAM,MAAA,WAAA,GAAc,SAAS,MAAM;AAEjC,MAAI,IAAA,OAAO,KAAM,CAAA,KAAA,KAAU,QAAY,IAAA,OAAO,KAAM,CAAA,KAAA,KAAU,QAAY,IAAA,OAAO,KAAM,CAAA,KAAA,KAAU,SAAW,EAAA;AAC1G,QAAO,OAAA,CAAC,EAAE,IAAM,EAAA,KAAA,CAAM,MAAM,KAAO,EAAA,KAAA,CAAM,OAAO,CAAA;AAAA,OAClD,MAAA,IAGS,OAAO,KAAM,CAAA,KAAA,KAAU,YAAY,KAAM,CAAA,OAAA,CAAQ,KAAM,CAAA,KAAK,CAAG,EAAA;AACtE,QAAA,OAAO,KAAM,CAAA,KAAA,CAAM,OAAQ,CAAA,CAAC,KAAK,KAAU,KAAA;AAEzC,UAAA,IAAI,OAAO,GAAQ,KAAA,QAAA;AACjB,YAAO,OAAA,MAAA,CAAO,QAAQ,GAAG,CAAA,CAAE,IAAI,CAAC,CAAC,KAAK,KAAK,CAAA,MAAO,EAAE,IAAM,EAAA,CAAA,CAAA,EAAI,MAAM,IAAI,CAAA,EAAA,EAAK,KAAK,CAAK,EAAA,EAAA,GAAG,CAAK,CAAA,CAAA,EAAA,KAAA,EAAQ,CAAA,CAAA;AAAA;AAGvG,YAAQ,OAAA,EAAE,MAAM,CAAI,CAAA,EAAA,KAAA,CAAM,IAAI,CAAK,EAAA,EAAA,KAAK,CAAK,CAAA,CAAA,EAAA,KAAA,EAAO,GAAI,EAAA;AAAA,SAC3D,CAAA;AAAA,OAIM,MAAA,IAAA,KAAA,CAAM,KAAU,KAAA,IAAA,IAAQ,OAAO,KAAA,CAAM,KAAU,KAAA,QAAA,IAAY,CAAC,KAAA,CAAM,OAAQ,CAAA,KAAA,CAAM,KAAK,CAAG,EAAA;AAC/F,QAAO,OAAA,MAAA,CAAO,QAAQ,KAAM,CAAA,KAAe,EAAE,GAAI,CAAA,CAAC,CAAC,GAAK,EAAA,KAAK,OAAO,EAAE,IAAA,EAAM,IAAI,KAAM,CAAA,IAAI,KAAK,GAAG,CAAA,CAAA,CAAA,EAAK,OAAQ,CAAA,CAAA;AAAA;AAGjH,MAAA,OAAO,EAAC;AAAA,KACT,CAAA;;;;;;;;;;;;;;;;;;;;"}