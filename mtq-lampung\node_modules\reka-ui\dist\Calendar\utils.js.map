{"version": 3, "file": "utils.js", "sources": ["../../src/Calendar/utils.ts"], "sourcesContent": ["export const SELECTOR\n  = '[data-reka-calendar-cell-trigger]:not([data-outside-view]):not([data-outside-visible-view])'\nexport function getSelectableCells(calendar: HTMLElement): HTMLElement[] {\n  return Array.from(calendar.querySelectorAll(SELECTOR)) ?? []\n}\n"], "names": [], "mappings": "AAAO,MAAM,QACT,GAAA,6FAAA;AACG,SAAS,mBAAmB,QAAsC,EAAA;AACvE,EAAA,OAAO,MAAM,IAAK,CAAA,QAAA,CAAS,iBAAiB,QAAQ,CAAC,KAAK,EAAC;AAC7D;;;;"}