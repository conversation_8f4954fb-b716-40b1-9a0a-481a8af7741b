import{d as f,x as _,h as n,o as l,w as r,e as a,a as c,k as d,b as o,u as s,g,t as w,j as x,i as m}from"./app-BxByyVXe.js";import{_ as k,a as y,b}from"./Label.vue_vue_type_script_setup_true_lang-Dc3XvMbX.js";import{_ as v}from"./TextLink.vue_vue_type_script_setup_true_lang-1wzIiHGk.js";import{_ as V}from"./AppLogoIcon.vue_vue_type_script_setup_true_lang-43VByX6w.js";import{L as $,_ as B}from"./AuthLayout.vue_vue_type_script_setup_true_lang-DidZ5Moo.js";import"./useForwardExpose-67BWFZEI.js";const C={key:0,class:"mb-4 text-center text-sm font-medium text-green-600"},E={class:"space-y-6"},N={class:"grid gap-2"},h={class:"my-6 flex items-center justify-start"},F={class:"space-x-1 text-center text-sm text-muted-foreground"},T=f({__name:"ForgotPassword",props:{status:{}},setup(j){const t=_({email:""}),p=()=>{t.post(route("password.email"))};return(i,e)=>(l(),n(B,{title:"Forgot password",description:"Enter your email to receive a password reset link"},{default:r(()=>[a(s(g),{title:"Forgot password"}),i.status?(l(),c("div",C,w(i.status),1)):d("",!0),o("div",E,[o("form",{onSubmit:x(p,["prevent"])},[o("div",N,[a(s(k),{for:"email"},{default:r(()=>e[1]||(e[1]=[m("Email address")])),_:1,__:[1]}),a(s(y),{id:"email",type:"email",name:"email",autocomplete:"off",modelValue:s(t).email,"onUpdate:modelValue":e[0]||(e[0]=u=>s(t).email=u),autofocus:"",placeholder:"<EMAIL>"},null,8,["modelValue"]),a(b,{message:s(t).errors.email},null,8,["message"])]),o("div",h,[a(s(V),{class:"w-full",disabled:s(t).processing},{default:r(()=>[s(t).processing?(l(),n(s($),{key:0,class:"h-4 w-4 animate-spin"})):d("",!0),e[2]||(e[2]=m(" Email password reset link "))]),_:1,__:[2]},8,["disabled"])])],32),o("div",F,[e[4]||(e[4]=o("span",null,"Or, return to",-1)),a(v,{href:i.route("login")},{default:r(()=>e[3]||(e[3]=[m("log in")])),_:1,__:[3]},8,["href"])])])]),_:1}))}});export{T as default};
