import{d as l,h as d,o as n,w as u,q as p,m as c,u as o,C as m,G as _,a as f,A as x,H as v,D as h,b as w,t as y,c as V}from"./app-BxByyVXe.js";import{P as k,a as b}from"./AppLogoIcon.vue_vue_type_script_setup_true_lang-43VByX6w.js";import{a as B,b as C}from"./useForwardExpose-67BWFZEI.js";const P=l({__name:"Label",props:{for:{},asChild:{type:Boolean},as:{default:"label"}},setup(a){const s=a;return B(),(t,r)=>(n(),d(o(k),c(s,{onMousedown:r[0]||(r[0]=e=>{!e.defaultPrevented&&e.detail>1&&e.preventDefault()})}),{default:u(()=>[p(t.$slots,"default")]),_:3},16))}}),F=l({__name:"Input",props:{defaultValue:{},modelValue:{},class:{}},emits:["update:modelValue"],setup(a,{emit:s}){const t=a,e=C(t,"modelValue",s,{passive:!0,defaultValue:t.defaultValue});return(D,i)=>m((n(),f("input",{"onUpdate:modelValue":i[0]||(i[0]=g=>v(e)?e.value=g:null),"data-slot":"input",class:x(o(b)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t.class))},null,2)),[[_,o(e)]])}}),$={class:"text-sm text-red-600 dark:text-red-500"},I=l({__name:"InputError",props:{message:{}},setup(a){return(s,t)=>m((n(),f("div",null,[w("p",$,y(s.message),1)],512)),[[h,s.message]])}}),L=l({__name:"Label",props:{for:{},asChild:{type:Boolean},as:{type:[String,Object,Function]},class:{}},setup(a){const s=a,t=V(()=>{const{class:r,...e}=s;return e});return(r,e)=>(n(),d(o(P),c({"data-slot":"label"},t.value,{class:o(b)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",s.class)}),{default:u(()=>[p(r.$slots,"default")]),_:3},16,["class"]))}});export{L as _,F as a,I as b};
