{"version": 3, "file": "utils.js", "sources": ["../../src/date/utils.ts", "../../src/shared/date/utils.ts"], "sourcesContent": ["/**\n * Splits an array into chunks of a given size.\n * @param arr The array to split.\n * @param size The size of each chunk.\n * @returns An array of arrays, where each sub-array has `size` elements from the original array.\n * @example ```ts\n * const arr = [1, 2, 3, 4, 5, 6, 7, 8];\n * const chunks = chunk(arr, 3);\n * // chunks = [[1, 2, 3], [4, 5, 6], [7, 8]]\n * ```\n */\nexport function chunk<T>(arr: T[], size: number): T[][] {\n  const result = []\n  for (let i = 0; i < arr.length; i += size)\n    result.push(arr.slice(i, i + size))\n\n  return result\n}\n", "import type { Granularity } from './comparators'\nimport type { DateStep, HourCycle } from './types'\nimport { defu } from 'defu'\n\nexport function getOptsByGranularity(granularity: Granularity, hourCycle: HourCycle, isTimeValue: boolean = false) {\n  const opts: Intl.DateTimeFormatOptions = {\n    year: 'numeric',\n    month: '2-digit',\n    day: '2-digit',\n    hour: '2-digit',\n    minute: '2-digit',\n    second: '2-digit',\n    timeZoneName: 'short',\n    hourCycle: normalizeHourCycle(hourCycle),\n    hour12: normalizeHour12(hourCycle),\n  }\n  if (isTimeValue) {\n    delete opts.year\n    delete opts.month\n    delete opts.day\n  }\n\n  if (granularity === 'day') {\n    delete opts.second\n    delete opts.hour\n    delete opts.minute\n    delete opts.timeZoneName\n  }\n  if (granularity === 'hour') {\n    delete opts.minute\n    delete opts.second\n  }\n\n  if (granularity === 'minute')\n    delete opts.second\n\n  return opts\n}\n\ntype GetDefaultDateStepProps = {\n  step?: DateStep\n}\n\nexport function normalizeDateStep(props?: GetDefaultDateStepProps): DateStep {\n  return defu(props?.step, {\n    year: 1,\n    month: 1,\n    day: 1,\n    hour: 1,\n    minute: 1,\n    second: 1,\n    millisecond: 1,\n  } satisfies DateStep)\n}\n\nexport function handleCalendarInitialFocus(calendar: HTMLElement) {\n  const selectedDay = calendar.querySelector<HTMLElement>('[data-selected]')\n  if (selectedDay)\n    return selectedDay.focus()\n\n  const today = calendar.querySelector<HTMLElement>('[data-today]')\n  if (today)\n    return today.focus()\n\n  const firstDay = calendar.querySelector<HTMLElement>('[data-reka-calendar-day]')\n  if (firstDay)\n    return firstDay.focus()\n}\n\nexport function normalizeHourCycle(hourCycle: HourCycle) {\n  if (hourCycle === 24)\n    return 'h23'\n  if (hourCycle === 12)\n    return 'h11'\n  return undefined\n}\n\nexport function normalizeHour12(hourCycle: HourCycle) {\n  if (hourCycle === 24)\n    return false\n  if (hourCycle === 12)\n    return true\n  return undefined\n}\n"], "names": [], "mappings": ";;AAWgB,SAAA,KAAA,CAAS,KAAU,IAAqB,EAAA;AACtD,EAAA,MAAM,SAAS,EAAC;AAChB,EAAA,KAAA,IAAS,CAAI,GAAA,CAAA,EAAG,CAAI,GAAA,GAAA,CAAI,QAAQ,CAAK,IAAA,IAAA;AACnC,IAAA,MAAA,CAAO,KAAK,GAAI,CAAA,KAAA,CAAM,CAAG,EAAA,CAAA,GAAI,IAAI,CAAC,CAAA;AAEpC,EAAO,OAAA,MAAA;AACT;;ACbO,SAAS,oBAAqB,CAAA,WAAA,EAA0B,SAAsB,EAAA,WAAA,GAAuB,KAAO,EAAA;AACjH,EAAA,MAAM,IAAmC,GAAA;AAAA,IACvC,IAAM,EAAA,SAAA;AAAA,IACN,KAAO,EAAA,SAAA;AAAA,IACP,GAAK,EAAA,SAAA;AAAA,IACL,IAAM,EAAA,SAAA;AAAA,IACN,MAAQ,EAAA,SAAA;AAAA,IACR,MAAQ,EAAA,SAAA;AAAA,IACR,YAAc,EAAA,OAAA;AAAA,IACd,SAAA,EAAW,mBAAmB,SAAS,CAAA;AAAA,IACvC,MAAA,EAAQ,gBAAgB,SAAS;AAAA,GACnC;AACA,EAAA,IAAI,WAAa,EAAA;AACf,IAAA,OAAO,IAAK,CAAA,IAAA;AACZ,IAAA,OAAO,IAAK,CAAA,KAAA;AACZ,IAAA,OAAO,IAAK,CAAA,GAAA;AAAA;AAGd,EAAA,IAAI,gBAAgB,KAAO,EAAA;AACzB,IAAA,OAAO,IAAK,CAAA,MAAA;AACZ,IAAA,OAAO,IAAK,CAAA,IAAA;AACZ,IAAA,OAAO,IAAK,CAAA,MAAA;AACZ,IAAA,OAAO,IAAK,CAAA,YAAA;AAAA;AAEd,EAAA,IAAI,gBAAgB,MAAQ,EAAA;AAC1B,IAAA,OAAO,IAAK,CAAA,MAAA;AACZ,IAAA,OAAO,IAAK,CAAA,MAAA;AAAA;AAGd,EAAA,IAAI,WAAgB,KAAA,QAAA;AAClB,IAAA,OAAO,IAAK,CAAA,MAAA;AAEd,EAAO,OAAA,IAAA;AACT;AAMO,SAAS,kBAAkB,KAA2C,EAAA;AAC3E,EAAO,OAAA,IAAA,CAAK,OAAO,IAAM,EAAA;AAAA,IACvB,IAAM,EAAA,CAAA;AAAA,IACN,KAAO,EAAA,CAAA;AAAA,IACP,GAAK,EAAA,CAAA;AAAA,IACL,IAAM,EAAA,CAAA;AAAA,IACN,MAAQ,EAAA,CAAA;AAAA,IACR,MAAQ,EAAA,CAAA;AAAA,IACR,WAAa,EAAA;AAAA,GACK,CAAA;AACtB;AAEO,SAAS,2BAA2B,QAAuB,EAAA;AAChE,EAAM,MAAA,WAAA,GAAc,QAAS,CAAA,aAAA,CAA2B,iBAAiB,CAAA;AACzE,EAAI,IAAA,WAAA;AACF,IAAA,OAAO,YAAY,KAAM,EAAA;AAE3B,EAAM,MAAA,KAAA,GAAQ,QAAS,CAAA,aAAA,CAA2B,cAAc,CAAA;AAChE,EAAI,IAAA,KAAA;AACF,IAAA,OAAO,MAAM,KAAM,EAAA;AAErB,EAAM,MAAA,QAAA,GAAW,QAAS,CAAA,aAAA,CAA2B,0BAA0B,CAAA;AAC/E,EAAI,IAAA,QAAA;AACF,IAAA,OAAO,SAAS,KAAM,EAAA;AAC1B;AAEO,SAAS,mBAAmB,SAAsB,EAAA;AACvD,EAAA,IAAI,SAAc,KAAA,EAAA;AAChB,IAAO,OAAA,KAAA;AACT,EAAA,IAAI,SAAc,KAAA,EAAA;AAChB,IAAO,OAAA,KAAA;AACT,EAAO,OAAA,MAAA;AACT;AAEO,SAAS,gBAAgB,SAAsB,EAAA;AACpD,EAAA,IAAI,SAAc,KAAA,EAAA;AAChB,IAAO,OAAA,KAAA;AACT,EAAA,IAAI,SAAc,KAAA,EAAA;AAChB,IAAO,OAAA,IAAA;AACT,EAAO,OAAA,MAAA;AACT;;;;"}