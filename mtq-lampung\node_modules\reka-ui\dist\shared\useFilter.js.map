{"version": 3, "file": "useFilter.js", "sources": ["../../src/shared/useFilter.ts"], "sourcesContent": ["import type { MaybeRef } from 'vue'\nimport { computed, unref } from 'vue'\n\n/**\n * Provides locale-aware string filtering functions.\n * Uses `Intl.Collator` for comparison to ensure proper Unicode handling.\n *\n * @param options - Optional collator options to customize comparison behavior.\n *   See [Intl.CollatorOptions](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/Collator/Collator#options) for details.\n * @returns An object with methods to check if a string starts with, ends with, or contains a substring.\n *\n * @example\n * const { startsWith, endsWith, contains } = useFilter();\n *\n * startsWith('hello', 'he'); // true\n * endsWith('hello', 'lo'); // true\n * contains('hello', 'ell'); // true\n */\nexport function useFilter(options?: MaybeRef<Intl.CollatorOptions>) {\n  const computedOptions = computed(() => unref(options))\n  const collator = computed(() => new Intl.Collator('en', { usage: 'search', ...computedOptions.value }))\n\n  const startsWith = (string: string, substring: string) => {\n    if (substring.length === 0)\n      return true\n\n    string = string.normalize('NFC')\n    substring = substring.normalize('NFC')\n    return collator.value.compare(string.slice(0, substring.length), substring) === 0\n  }\n\n  const endsWith = (string: string, substring: string) => {\n    if (substring.length === 0)\n      return true\n\n    string = string.normalize('NFC')\n    substring = substring.normalize('NFC')\n    return collator.value.compare(string.slice(-substring.length), substring) === 0\n  }\n\n  const contains = (string: string, substring: string) => {\n    if (substring.length === 0)\n      return true\n\n    string = string.normalize('NFC')\n    substring = substring.normalize('NFC')\n\n    let scan = 0\n    const sliceLen = substring.length\n    for (; scan + sliceLen <= string.length; scan++) {\n      const slice = string.slice(scan, scan + sliceLen)\n      if (collator.value.compare(substring, slice) === 0)\n        return true\n    }\n\n    return false\n  }\n\n  return {\n    startsWith,\n    endsWith,\n    contains,\n  }\n}\n"], "names": [], "mappings": ";;AAkBO,SAAS,UAAU,OAA0C,EAAA;AAClE,EAAA,MAAM,eAAkB,GAAA,QAAA,CAAS,MAAM,KAAA,CAAM,OAAO,CAAC,CAAA;AACrD,EAAA,MAAM,QAAW,GAAA,QAAA,CAAS,MAAM,IAAI,KAAK,QAAS,CAAA,IAAA,EAAM,EAAE,KAAA,EAAO,QAAU,EAAA,GAAG,eAAgB,CAAA,KAAA,EAAO,CAAC,CAAA;AAEtG,EAAM,MAAA,UAAA,GAAa,CAAC,MAAA,EAAgB,SAAsB,KAAA;AACxD,IAAA,IAAI,UAAU,MAAW,KAAA,CAAA;AACvB,MAAO,OAAA,IAAA;AAET,IAAS,MAAA,GAAA,MAAA,CAAO,UAAU,KAAK,CAAA;AAC/B,IAAY,SAAA,GAAA,SAAA,CAAU,UAAU,KAAK,CAAA;AACrC,IAAO,OAAA,QAAA,CAAS,KAAM,CAAA,OAAA,CAAQ,MAAO,CAAA,KAAA,CAAM,GAAG,SAAU,CAAA,MAAM,CAAG,EAAA,SAAS,CAAM,KAAA,CAAA;AAAA,GAClF;AAEA,EAAM,MAAA,QAAA,GAAW,CAAC,MAAA,EAAgB,SAAsB,KAAA;AACtD,IAAA,IAAI,UAAU,MAAW,KAAA,CAAA;AACvB,MAAO,OAAA,IAAA;AAET,IAAS,MAAA,GAAA,MAAA,CAAO,UAAU,KAAK,CAAA;AAC/B,IAAY,SAAA,GAAA,SAAA,CAAU,UAAU,KAAK,CAAA;AACrC,IAAO,OAAA,QAAA,CAAS,KAAM,CAAA,OAAA,CAAQ,MAAO,CAAA,KAAA,CAAM,CAAC,SAAU,CAAA,MAAM,CAAG,EAAA,SAAS,CAAM,KAAA,CAAA;AAAA,GAChF;AAEA,EAAM,MAAA,QAAA,GAAW,CAAC,MAAA,EAAgB,SAAsB,KAAA;AACtD,IAAA,IAAI,UAAU,MAAW,KAAA,CAAA;AACvB,MAAO,OAAA,IAAA;AAET,IAAS,MAAA,GAAA,MAAA,CAAO,UAAU,KAAK,CAAA;AAC/B,IAAY,SAAA,GAAA,SAAA,CAAU,UAAU,KAAK,CAAA;AAErC,IAAA,IAAI,IAAO,GAAA,CAAA;AACX,IAAA,MAAM,WAAW,SAAU,CAAA,MAAA;AAC3B,IAAA,OAAO,IAAO,GAAA,QAAA,IAAY,MAAO,CAAA,MAAA,EAAQ,IAAQ,EAAA,EAAA;AAC/C,MAAA,MAAM,KAAQ,GAAA,MAAA,CAAO,KAAM,CAAA,IAAA,EAAM,OAAO,QAAQ,CAAA;AAChD,MAAA,IAAI,QAAS,CAAA,KAAA,CAAM,OAAQ,CAAA,SAAA,EAAW,KAAK,CAAM,KAAA,CAAA;AAC/C,QAAO,OAAA,IAAA;AAAA;AAGX,IAAO,OAAA,KAAA;AAAA,GACT;AAEA,EAAO,OAAA;AAAA,IACL,UAAA;AAAA,IACA,QAAA;AAAA,IACA;AAAA,GACF;AACF;;;;"}