{"version": 3, "file": "useNonce.cjs", "sources": ["../../src/shared/useNonce.ts"], "sourcesContent": ["import type { Ref } from 'vue'\nimport { computed, ref } from 'vue'\nimport { injectConfigProviderContext } from '@/ConfigProvider/ConfigProvider.vue'\n\nexport function useNonce(nonce?: Ref<string | undefined>) {\n  const context = injectConfigProviderContext({\n    nonce: ref(),\n  })\n  return computed(() => nonce?.value || context.nonce?.value)\n}\n"], "names": ["injectConfigProviderContext", "ref", "computed"], "mappings": ";;;;;AAIO,SAAS,SAAS,KAAiC,EAAA;AACxD,EAAA,MAAM,UAAUA,yDAA4B,CAAA;AAAA,IAC1C,OAAOC,OAAI;AAAA,GACZ,CAAA;AACD,EAAA,OAAOC,aAAS,MAAM,KAAA,EAAO,KAAS,IAAA,OAAA,CAAQ,OAAO,KAAK,CAAA;AAC5D;;;;"}