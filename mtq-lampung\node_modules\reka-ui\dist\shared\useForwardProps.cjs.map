{"version": 3, "file": "useForwardProps.cjs", "sources": ["../../src/shared/useForwardProps.ts"], "sourcesContent": ["import type { MaybeRefOrGetter } from 'vue'\nimport { camelize, computed, getCurrentInstance, toRef, toRefs } from 'vue'\n\ninterface PropOptions {\n  type?: any\n  required?: boolean\n  default?: any\n}\n\n/**\n * The `useForwardProps` function in TypeScript takes in a set of props and returns a computed value\n * that combines default props with assigned props from the current instance.\n * @param {T} props - The `props` parameter is an object that represents the props passed to a\n * component.\n * @returns computed value that combines the default props, preserved props, and assigned props.\n */\nexport function useForwardProps<T extends Record<string, any>>(props: MaybeRefOrGetter<T>) {\n  const vm = getCurrentInstance()\n  // Default value for declared props\n  const defaultProps = Object.keys(vm?.type.props ?? {}).reduce((prev, curr) => {\n    const defaultValue = (vm?.type.props[curr] as PropOptions).default\n    if (defaultValue !== undefined)\n      prev[curr as keyof T] = defaultValue\n    return prev\n  }, {} as T)\n\n  const refProps = toRef(props)\n  return computed(() => {\n    const propsAsRefs = toRefs(refProps.value)\n    const preservedProps = {} as T\n    const assignedProps = vm?.vnode.props ?? {}\n\n    Object.keys(assignedProps).forEach((key) => {\n      preservedProps[camelize(key) as keyof T] = assignedProps[key]\n    })\n\n    // Only return value from the props parameter\n    return Object.keys({ ...defaultProps, ...preservedProps }).reduce((prev, curr) => {\n      const val = propsAsRefs[curr]?.value\n      if (val !== undefined)\n        prev[curr as keyof T] = val\n      return prev\n    }, {} as T)\n  })\n}\n"], "names": ["getCurrentInstance", "toRef", "computed", "toRefs", "camelize"], "mappings": ";;;;AAgBO,SAAS,gBAA+C,KAA4B,EAAA;AACzF,EAAA,MAAM,KAAKA,sBAAmB,EAAA;AAE9B,EAAA,MAAM,YAAe,GAAA,MAAA,CAAO,IAAK,CAAA,EAAA,EAAI,IAAK,CAAA,KAAA,IAAS,EAAE,CAAE,CAAA,MAAA,CAAO,CAAC,IAAA,EAAM,IAAS,KAAA;AAC5E,IAAA,MAAM,YAAgB,GAAA,CAAA,EAAA,EAAI,IAAK,CAAA,KAAA,CAAM,IAAI,CAAkB,EAAA,OAAA;AAC3D,IAAA,IAAI,YAAiB,KAAA,MAAA;AACnB,MAAA,IAAA,CAAK,IAAe,CAAI,GAAA,YAAA;AAC1B,IAAO,OAAA,IAAA;AAAA,GACT,EAAG,EAAO,CAAA;AAEV,EAAM,MAAA,QAAA,GAAWC,UAAM,KAAK,CAAA;AAC5B,EAAA,OAAOC,aAAS,MAAM;AACpB,IAAM,MAAA,WAAA,GAAcC,UAAO,CAAA,QAAA,CAAS,KAAK,CAAA;AACzC,IAAA,MAAM,iBAAiB,EAAC;AACxB,IAAA,MAAM,aAAgB,GAAA,EAAA,EAAI,KAAM,CAAA,KAAA,IAAS,EAAC;AAE1C,IAAA,MAAA,CAAO,IAAK,CAAA,aAAa,CAAE,CAAA,OAAA,CAAQ,CAAC,GAAQ,KAAA;AAC1C,MAAA,cAAA,CAAeC,YAAS,CAAA,GAAG,CAAY,CAAA,GAAI,cAAc,GAAG,CAAA;AAAA,KAC7D,CAAA;AAGD,IAAA,OAAO,MAAO,CAAA,IAAA,CAAK,EAAE,GAAG,YAAc,EAAA,GAAG,cAAe,EAAC,CAAE,CAAA,MAAA,CAAO,CAAC,IAAA,EAAM,IAAS,KAAA;AAChF,MAAM,MAAA,GAAA,GAAM,WAAY,CAAA,IAAI,CAAG,EAAA,KAAA;AAC/B,MAAA,IAAI,GAAQ,KAAA,MAAA;AACV,QAAA,IAAA,CAAK,IAAe,CAAI,GAAA,GAAA;AAC1B,MAAO,OAAA,IAAA;AAAA,KACT,EAAG,EAAO,CAAA;AAAA,GACX,CAAA;AACH;;;;"}