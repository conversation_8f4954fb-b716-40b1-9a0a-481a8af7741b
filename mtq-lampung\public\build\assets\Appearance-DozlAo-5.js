import{d,z as k,a as i,o as t,F as y,r as g,b as s,h as m,y as x,t as b,A as f,u as r,w as l,e,g as v}from"./app-BxByyVXe.js";import{c}from"./AppLogoIcon.vue_vue_type_script_setup_true_lang-43VByX6w.js";import{_ as M,a as w}from"./Layout.vue_vue_type_script_setup_true_lang-BUvfOgPo.js";import{_ as A}from"./AppLayout.vue_vue_type_script_setup_true_lang-va1QG6ga.js";import"./useForwardExpose-67BWFZEI.js";import"./RovingFocusGroup-hazQWYmS.js";/**
 * @license lucide-vue-next v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const I=c("MonitorIcon",[["rect",{width:"20",height:"14",x:"2",y:"3",rx:"2",key:"48i651"}],["line",{x1:"8",x2:"16",y1:"21",y2:"21",key:"1svkeh"}],["line",{x1:"12",x2:"12",y1:"17",y2:"21",key:"vw1qmm"}]]);/**
 * @license lucide-vue-next v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const C=c("MoonIcon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]]);/**
 * @license lucide-vue-next v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $=c("SunIcon",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]]),z={class:"inline-flex gap-1 rounded-lg bg-neutral-100 p-1 dark:bg-neutral-800"},B=["onClick"],S={class:"ml-1.5 text-sm"},D=d({__name:"AppearanceTabs",setup(u){const{appearance:a,updateAppearance:o}=k(),p=[{value:"light",Icon:$,label:"Light"},{value:"dark",Icon:C,label:"Dark"},{value:"system",Icon:I,label:"System"}];return(j,q)=>(t(),i("div",z,[(t(),i(y,null,g(p,({value:n,Icon:h,label:_})=>s("button",{key:n,onClick:F=>r(o)(n),class:f(["flex items-center rounded-md px-3.5 py-1.5 transition-colors",r(a)===n?"bg-white shadow-xs dark:bg-neutral-700 dark:text-neutral-100":"text-neutral-500 hover:bg-neutral-200/60 hover:text-black dark:text-neutral-400 dark:hover:bg-neutral-700/60"])},[(t(),m(x(h),{class:"-ml-1 h-4 w-4"})),s("span",S,b(_),1)],10,B)),64))]))}}),L={class:"space-y-6"},G=d({__name:"Appearance",setup(u){const a=[{title:"Appearance settings",href:"/settings/appearance"}];return(o,p)=>(t(),m(A,{breadcrumbs:a},{default:l(()=>[e(r(v),{title:"Appearance settings"}),e(M,null,{default:l(()=>[s("div",L,[e(w,{title:"Appearance settings",description:"Update your account's appearance settings"}),e(D)])]),_:1})]),_:1}))}});export{G as default};
