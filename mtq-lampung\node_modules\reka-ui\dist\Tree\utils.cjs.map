{"version": 3, "file": "utils.cjs", "sources": ["../../src/Tree/utils.ts"], "sourcesContent": ["export function flatten<U, T extends { children: any[] }>(items: T[]): U[] {\n  return items.reduce((acc: any[], item: T) => {\n    acc.push(item)\n\n    if (item.children)\n      acc.push(...flatten(item.children))\n\n    return acc\n  }, [])\n}\n\n// TODO: expose more utility function to handle flattened item\n"], "names": [], "mappings": ";;AAAO,SAAS,QAA0C,KAAiB,EAAA;AACzE,EAAA,OAAO,KAAM,CAAA,MAAA,CAAO,CAAC,GAAA,EAAY,IAAY,KAAA;AAC3C,IAAA,GAAA,CAAI,KAAK,IAAI,CAAA;AAEb,IAAA,IAAI,IAAK,CAAA,QAAA;AACP,MAAA,GAAA,CAAI,IAAK,CAAA,GAAG,OAAQ,CAAA,IAAA,CAAK,QAAQ,CAAC,CAAA;AAEpC,IAAO,OAAA,GAAA;AAAA,GACT,EAAG,EAAE,CAAA;AACP;;;;"}