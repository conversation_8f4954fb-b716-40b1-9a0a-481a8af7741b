import{d as c,x as f,h as n,o as a,w as i,e as o,a as u,k as m,b as p,u as e,g as _,j as k,i as d}from"./app-BxByyVXe.js";import{_ as y}from"./TextLink.vue_vue_type_script_setup_true_lang-1wzIiHGk.js";import{_ as b}from"./AppLogoIcon.vue_vue_type_script_setup_true_lang-43VByX6w.js";import{L as g,_ as v}from"./AuthLayout.vue_vue_type_script_setup_true_lang-DidZ5Moo.js";const x={key:0,class:"mb-4 text-center text-sm font-medium text-green-600"},N=c({__name:"VerifyEmail",props:{status:{}},setup(h){const s=f({}),l=()=>{s.post(route("verification.send"))};return(r,t)=>(a(),n(v,{title:"Verify email",description:"Please verify your email address by clicking on the link we just emailed to you."},{default:i(()=>[o(e(_),{title:"Email verification"}),r.status==="verification-link-sent"?(a(),u("div",x," A new verification link has been sent to the email address you provided during registration. ")):m("",!0),p("form",{onSubmit:k(l,["prevent"]),class:"space-y-6 text-center"},[o(e(b),{disabled:e(s).processing,variant:"secondary"},{default:i(()=>[e(s).processing?(a(),n(e(g),{key:0,class:"h-4 w-4 animate-spin"})):m("",!0),t[0]||(t[0]=d(" Resend verification email "))]),_:1,__:[0]},8,["disabled"]),o(y,{href:r.route("logout"),method:"post",as:"button",class:"mx-auto block text-sm"},{default:i(()=>t[1]||(t[1]=[d(" Log out ")])),_:1,__:[1]},8,["href"])],32)]),_:1}))}});export{N as default};
