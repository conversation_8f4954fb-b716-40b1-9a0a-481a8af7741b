{"version": 3, "file": "useDateField.js", "sources": ["../../src/shared/date/useDateField.ts"], "sourcesContent": ["import type { CalendarDateTime, CycleTimeOptions, DateFields, DateValue, TimeFields } from '@internationalized/date'\nimport type { Ref } from 'vue'\nimport type { AnyExceptLiteral, DateStep, HourCycle, SegmentPart, SegmentValueObj } from './types'\nimport type { Formatter } from '@/shared'\nimport { computed } from 'vue'\nimport { getDaysInMonth, toDate } from '@/date'\nimport { useKbd } from '@/shared'\nimport { isAcceptableSegmentKey, isNumberString, isSegmentNavigationKey } from './segment'\n\ntype MinuteSecondIncrementProps = {\n  e: KeyboardEvent\n  part: keyof TimeFields\n  dateRef: DateValue\n  prevValue: number | null\n}\n\ntype DateTimeValueIncrementation = {\n  e: KeyboardEvent\n  part: keyof Omit<DateFields, 'era'> | keyof TimeFields\n  dateRef: DateValue\n  prevValue: number | null\n  hourCycle?: HourCycle\n}\n\ntype SegmentAttrProps = {\n  disabled: boolean\n  segmentValues: SegmentValueObj\n  hourCycle: HourCycle\n  placeholder: DateValue\n  formatter: Formatter\n}\n\nfunction commonSegmentAttrs(props: SegmentAttrProps) {\n  return {\n    role: 'spinbutton',\n    contenteditable: true,\n    tabindex: props.disabled ? undefined : 0,\n    spellcheck: false,\n    inputmode: 'numeric',\n    autocorrect: 'off',\n    enterkeyhint: 'next',\n    style: 'caret-color: transparent;',\n  }\n}\n\nfunction daySegmentAttrs(props: SegmentAttrProps) {\n  const { segmentValues, placeholder } = props\n  const isEmpty = segmentValues.day === null\n  const date = segmentValues.day ? placeholder.set({ day: segmentValues.day }) : placeholder\n\n  const valueNow = date.day\n  const valueMin = 1\n  const valueMax = getDaysInMonth(date)\n  const valueText = isEmpty ? 'Empty' : `${valueNow}`\n\n  return {\n    ...commonSegmentAttrs(props),\n    'aria-label': 'day,',\n    'aria-valuemin': valueMin,\n    'aria-valuemax': valueMax,\n    'aria-valuenow': valueNow,\n    'aria-valuetext': valueText,\n    'data-placeholder': isEmpty ? '' : undefined,\n  }\n}\n\nfunction monthSegmentAttrs(props: SegmentAttrProps) {\n  const { segmentValues, placeholder, formatter } = props\n  const isEmpty = segmentValues.month === null\n  const date = segmentValues.month\n    ? placeholder.set({ month: segmentValues.month })\n    : placeholder\n  const valueNow = date.month\n  const valueMin = 1\n  const valueMax = 12\n  const valueText = isEmpty ? 'Empty' : `${valueNow} - ${formatter.fullMonth(toDate(date))}`\n\n  return {\n    ...commonSegmentAttrs(props),\n    'aria-label': 'month, ',\n    'contenteditable': true,\n    'aria-valuemin': valueMin,\n    'aria-valuemax': valueMax,\n    'aria-valuenow': valueNow,\n    'aria-valuetext': valueText,\n    'data-placeholder': isEmpty ? '' : undefined,\n  }\n}\n\nfunction yearSegmentAttrs(props: SegmentAttrProps) {\n  const { segmentValues, placeholder } = props\n  const isEmpty = segmentValues.year === null\n  const date = segmentValues.year ? placeholder.set({ year: segmentValues.year }) : placeholder\n  const valueMin = 1\n  const valueMax = 9999\n  const valueNow = date.year\n  const valueText = isEmpty ? 'Empty' : `${valueNow}`\n\n  return {\n    ...commonSegmentAttrs(props),\n    'aria-label': 'year, ',\n    'aria-valuemin': valueMin,\n    'aria-valuemax': valueMax,\n    'aria-valuenow': valueNow,\n    'aria-valuetext': valueText,\n    'data-placeholder': isEmpty ? '' : undefined,\n  }\n}\n\nfunction hourSegmentAttrs(props: SegmentAttrProps) {\n  const { segmentValues, hourCycle, placeholder } = props\n\n  if (!('hour' in segmentValues) || !('hour' in placeholder))\n    return {}\n  const isEmpty = segmentValues.hour === null\n  const date = segmentValues.hour ? placeholder.set({ hour: segmentValues.hour }) : placeholder\n  const valueMin = hourCycle === 12 ? 1 : 0\n  const valueMax = hourCycle === 12 ? 12 : 23\n  const valueNow = date.hour\n  const valueText = isEmpty ? 'Empty' : `${valueNow} ${segmentValues.dayPeriod ?? ''}`\n\n  return {\n    ...commonSegmentAttrs(props),\n    'aria-label': 'hour, ',\n    'aria-valuemin': valueMin,\n    'aria-valuemax': valueMax,\n    'aria-valuenow': valueNow,\n    'aria-valuetext': valueText,\n    'data-placeholder': isEmpty ? '' : undefined,\n  }\n}\n\nfunction minuteSegmentAttrs(props: SegmentAttrProps) {\n  const { segmentValues, placeholder } = props\n  if (!('minute' in segmentValues) || !('minute' in placeholder))\n    return {}\n  const isEmpty = segmentValues.minute === null\n  const date = segmentValues.minute\n    ? placeholder.set({ minute: segmentValues.minute })\n    : placeholder\n  const valueNow = date.minute\n  const valueMin = 0\n  const valueMax = 59\n  const valueText = isEmpty ? 'Empty' : `${valueNow}`\n\n  return {\n    ...commonSegmentAttrs(props),\n    'aria-label': 'minute, ',\n    'aria-valuemin': valueMin,\n    'aria-valuemax': valueMax,\n    'aria-valuenow': valueNow,\n    'aria-valuetext': valueText,\n    'data-placeholder': isEmpty ? '' : undefined,\n  }\n}\n\nfunction secondSegmentAttrs(props: SegmentAttrProps) {\n  const { segmentValues, placeholder } = props\n  if (!('second' in segmentValues) || !('second' in placeholder))\n    return {}\n  const isEmpty = segmentValues.second === null\n  const date = segmentValues.second\n    ? placeholder.set({ second: segmentValues.second })\n    : placeholder\n  const valueNow = date.second\n  const valueMin = 0\n  const valueMax = 59\n  const valueText = isEmpty ? 'Empty' : `${valueNow}`\n\n  return {\n    ...commonSegmentAttrs(props),\n    'aria-label': 'second, ',\n    'aria-valuemin': valueMin,\n    'aria-valuemax': valueMax,\n    'aria-valuenow': valueNow,\n    'aria-valuetext': valueText,\n    'data-placeholder': isEmpty ? '' : undefined,\n  }\n}\n\nfunction dayPeriodSegmentAttrs(props: SegmentAttrProps) {\n  const { segmentValues } = props\n  if (!('dayPeriod' in segmentValues))\n    return {}\n\n  const valueMin = 0\n  const valueMax = 12\n  const valueNow = segmentValues.hour ? (segmentValues.hour > 12 ? segmentValues.hour - 12 : segmentValues.hour) : 0\n  const valueText = segmentValues.dayPeriod ?? 'AM'\n\n  return {\n    ...commonSegmentAttrs(props),\n    'inputmode': 'text',\n    'aria-label': 'AM/PM',\n    'aria-valuemin': valueMin,\n    'aria-valuemax': valueMax,\n    'aria-valuenow': valueNow,\n    'aria-valuetext': valueText,\n  }\n}\n\nfunction literalSegmentAttrs(_props: SegmentAttrProps) {\n  return {\n    'aria-hidden': true,\n    'data-segment': 'literal',\n  }\n}\n\nfunction timeZoneSegmentAttrs(props: SegmentAttrProps) {\n  return {\n    'role': 'textbox',\n    'aria-label': 'timezone, ',\n    'data-readonly': true,\n    'data-segment': 'timeZoneName',\n    'tabindex': props.disabled ? undefined : 0,\n    'style': 'caret-color: transparent;',\n  }\n}\n\nfunction eraSegmentAttrs(props: SegmentAttrProps) {\n  const { segmentValues, placeholder } = props\n\n  const valueMin = 0\n  const valueMax = 0\n  const valueNow = 0\n  const valueText = 'era' in segmentValues ? segmentValues.era : placeholder.era\n\n  return {\n    ...commonSegmentAttrs(props),\n    'aria-label': 'era',\n    'aria-valuemin': valueMin,\n    'aria-valuemax': valueMax,\n    'aria-valuenow': valueNow,\n    'aria-valuetext': valueText,\n  }\n}\n\nexport const segmentBuilders = {\n  day: {\n    attrs: daySegmentAttrs,\n  },\n  month: {\n    attrs: monthSegmentAttrs,\n  },\n  year: {\n    attrs: yearSegmentAttrs,\n  },\n  hour: {\n    attrs: hourSegmentAttrs,\n  },\n  minute: {\n    attrs: minuteSegmentAttrs,\n  },\n  second: {\n    attrs: secondSegmentAttrs,\n  },\n  dayPeriod: {\n    attrs: dayPeriodSegmentAttrs,\n  },\n  literal: {\n    attrs: literalSegmentAttrs,\n  },\n  timeZoneName: {\n    attrs: timeZoneSegmentAttrs,\n  },\n  era: {\n    attrs: eraSegmentAttrs,\n  },\n}\n\nexport type UseDateFieldProps = {\n  hasLeftFocus: Ref<boolean>\n  lastKeyZero: Ref<boolean>\n  placeholder: Ref<DateValue>\n  hourCycle: HourCycle\n  step: Ref<DateStep>\n  formatter: Formatter\n  segmentValues: Ref<SegmentValueObj>\n  disabled: Ref<boolean>\n  readonly: Ref<boolean>\n  part: SegmentPart\n  modelValue: Ref<DateValue | undefined>\n  focusNext: () => void\n}\n\nexport function useDateField(props: UseDateFieldProps) {\n  const kbd = useKbd()\n\n  function minuteSecondIncrementation({ e, part, dateRef, prevValue }: MinuteSecondIncrementProps): number {\n    const step = props.step.value[part] ?? 1\n    const sign = e.key === kbd.ARROW_UP ? step : -step\n    const min = 0\n    const max = 59\n\n    if (prevValue === null)\n      return sign > 0 ? min : max\n\n    const cycleArgs: [keyof TimeFields, number] = [part, sign]\n    return (dateRef as CalendarDateTime).set({ [part]: prevValue }).cycle(...cycleArgs)[part]\n  }\n\n  function deleteValue(prevValue: number | null) {\n    props.hasLeftFocus.value = false\n    if (prevValue === null)\n      return prevValue\n\n    const str = prevValue.toString()\n    if (str.length === 1) {\n      props.modelValue.value = undefined\n      return null\n    }\n\n    return Number.parseInt(str.slice(0, -1))\n  }\n  function dateTimeValueIncrementation({ e, part, dateRef, prevValue, hourCycle }: DateTimeValueIncrementation): number {\n    const step = props.step.value[part] ?? 1\n    const sign = e.key === kbd.ARROW_UP ? step : -step\n\n    if (prevValue === null)\n      return dateRef[part as keyof Omit<DateFields, 'era'>]\n\n    if (part === 'hour' && 'hour' in dateRef) {\n      const cycleArgs: [keyof DateFields | keyof TimeFields, number, CycleTimeOptions?] = [part, sign, { hourCycle }]\n      return dateRef.set({ [part as keyof DateValue]: prevValue }).cycle(...cycleArgs)[part]\n    }\n\n    const cycleArgs: [keyof DateFields, number] = [part as keyof DateFields, sign]\n    if (part === 'day') {\n      return dateRef.set({\n        [part as keyof DateValue]: prevValue,\n        /**\n         * Edge case for the day field:\n         *\n         * 1. If the month is filled,\n         *   we need to ensure that the day snaps to the maximum value of that month.\n         * 2. If the month is not filled,\n         *   we default to the month with the maximum number of days (here just using January, 31 days),\n         *   so that user can input any possible day.\n         */\n        month: props.segmentValues.value.month ?? 1,\n      }).cycle(...cycleArgs)[part as keyof Omit<DateFields, 'era'>]\n    }\n\n    return dateRef.set({ [part as keyof DateValue]: prevValue }).cycle(...cycleArgs)[part as keyof Omit<DateFields, 'era'>]\n  }\n  function updateDayOrMonth(max: number, num: number, prev: number | null) {\n    let moveToNext = false\n    const maxStart = Math.floor(max / 10)\n\n    /**\n     * If the user has left the segment, we want to reset the\n     * `prev` value so that we can start the segment over again\n     * when the user types a number.\n     */\n    if (props.hasLeftFocus.value) {\n      props.hasLeftFocus.value = false\n      prev = null\n    }\n\n    if (prev === null) {\n    /**\n     * If the user types a 0 as the first number, we want\n     * to keep track of that so that when they type the next\n     * number, we can move to the next segment.\n     */\n\n      if (num === 0) {\n        props.lastKeyZero.value = true\n        return { value: null, moveToNext }\n      }\n      /**\n       * If the last key was a 0, or if the first number is\n       * greater than the max start digit (0-3 in most cases), then\n       * we want to move to the next segment, since it's not possible\n       * to continue typing a valid number in this segment.\n       */\n\n      if (props.lastKeyZero.value || num > maxStart) {\n      // move to next\n        moveToNext = true\n      }\n      props.lastKeyZero.value = false\n      /**\n       * If none of the above conditions are met, then we can just\n       * return the number as the segment value and continue typing\n       * in this segment.\n       */\n      return { value: num, moveToNext }\n    }\n\n    /**\n     * If the number of digits is 2, or if the total with the existing digit\n     * and the pressed digit is greater than the maximum value for this\n     * month, then we will reset the segment as if the user had pressed the\n     * backspace key and then typed the number.\n     */\n    const digits = prev.toString().length\n    const total = Number.parseInt(prev.toString() + num.toString())\n    /**\n     * If the number of digits is 2, or if the total with the existing digit\n     * and the pressed digit is greater than the maximum value for this\n     * month, then we will reset the segment as if the user had pressed the\n     * backspace key and then typed the number.\n     */\n\n    if (digits === 2 || total > max) {\n    /**\n     * As we're doing elsewhere, we're checking if the number is greater\n     * than the max start digit (0-3 in most months), and if so, we're\n     * going to move to the next segment.\n     */\n      if (num > maxStart || total > max) {\n      // move to next\n        moveToNext = true\n      }\n      return { value: num, moveToNext }\n    }\n    // move to next\n    moveToNext = true\n    return { value: total, moveToNext }\n  }\n\n  function updateMinuteOrSecond(num: number, prev: number | null) {\n    const max = 59\n    let moveToNext = false\n    const maxStart = Math.floor(max / 10)\n\n    /**\n     * If the user has left the segment, we want to reset the\n     * `prev` value so that we can start the segment over again\n     * when the user types a number.\n     */\n    if (props.hasLeftFocus.value) {\n      props.hasLeftFocus.value = false\n      prev = null\n    }\n\n    if (prev === null) {\n    /**\n     * If the user types a 0 as the first number, we want\n     * to keep track of that so that when they type the next\n     * number, we can move to the next segment.\n     */\n\n      if (num === 0) {\n        props.lastKeyZero.value = true\n        return { value: 0, moveToNext }\n      }\n      /**\n       * If the last key was a 0, or if the first number is\n       * greater than the max start digit (0-3 in most cases), then\n       * we want to move to the next segment, since it's not possible\n       * to continue typing a valid number in this segment.\n       */\n\n      if (props.lastKeyZero.value || num > maxStart) {\n      // move to next\n        moveToNext = true\n      }\n      props.lastKeyZero.value = false\n      /**\n       * If none of the above conditions are met, then we can just\n       * return the number as the segment value and continue typing\n       * in this segment.\n       */\n      return { value: num, moveToNext }\n    }\n\n    /**\n     * If the number of digits is 2, or if the total with the existing digit\n     * and the pressed digit is greater than the maximum value for this\n     * month, then we will reset the segment as if the user had pressed the\n     * backspace key and then typed the number.\n     */\n    const digits = prev.toString().length\n    const total = Number.parseInt(prev.toString() + num.toString())\n\n    /**\n     * If the number of digits is 2, or if the total with the existing digit\n     * and the pressed digit is greater than the maximum value for this\n     * month, then we will reset the segment as if the user had pressed the\n     * backspace key and then typed the number.\n     */\n\n    if (digits === 2 || total > max) {\n    /**\n     * As we're doing elsewhere, we're checking if the number is greater\n     * than the max start digit (0-3 in most months), and if so, we're\n     * going to move to the next segment.\n     */\n      if (num > maxStart) {\n      // move to next\n        moveToNext = true\n      }\n      return { value: num, moveToNext }\n    }\n    // move to next\n    moveToNext = true\n    return { value: total, moveToNext }\n  }\n\n  function updateHour(num: number, prev: number | null) {\n    const max = 24\n    let moveToNext = false\n    const maxStart = Math.floor(max / 10)\n\n    /**\n     * If the user has left the segment, we want to reset the\n     * `prev` value so that we can start the segment over again\n     * when the user types a number.\n     */\n    // probably not implement, kind of weird\n    if (props.hasLeftFocus.value) {\n      props.hasLeftFocus.value = false\n      prev = null\n    }\n\n    if (prev === null) {\n    /**\n     * If the user types a 0 as the first number, we want\n     * to keep track of that so that when they type the next\n     * number, we can move to the next segment.\n     */\n\n      if (num === 0) {\n        props.lastKeyZero.value = true\n        return { value: 0, moveToNext }\n      }\n      /**\n       * If the last key was a 0, or if the first number is\n       * greater than the max start digit (0-3 in most cases), then\n       * we want to move to the next segment, since it's not possible\n       * to continue typing a valid number in this segment.\n       */\n\n      if (props.lastKeyZero.value || num > maxStart) {\n      // move to next\n        moveToNext = true\n      }\n      props.lastKeyZero.value = false\n      /**\n       * If none of the above conditions are met, then we can just\n       * return the number as the segment value and continue typing\n       * in this segment.\n       */\n      return { value: num, moveToNext }\n    }\n\n    /**\n     * If the number of digits is 2, or if the total with the existing digit\n     * and the pressed digit is greater than the maximum value for this\n     * month, then we will reset the segment as if the user had pressed the\n     * backspace key and then typed the number.\n     */\n    const digits = prev.toString().length\n    const total = Number.parseInt(prev.toString() + num.toString())\n\n    /**\n     * If the number of digits is 2, or if the total with the existing digit\n     * and the pressed digit is greater than the maximum value for this\n     * month, then we will reset the segment as if the user had pressed the\n     * backspace key and then typed the number.\n     */\n\n    if (digits === 2 || total > max) {\n    /**\n     * As we're doing elsewhere, we're checking if the number is greater\n     * than the max start digit (0-3 in most months), and if so, we're\n     * going to move to the next segment.\n     */\n      if (num > maxStart) {\n      // move to next\n        moveToNext = true\n      }\n      return { value: num, moveToNext }\n    }\n    // move to next\n    moveToNext = true\n    return { value: total, moveToNext }\n  }\n\n  function updateYear(num: number, prev: number | null) {\n    let moveToNext = false\n\n    /**\n     * If the user has left the segment, we want to reset the\n     * `prev` value so that we can start the segment over again\n     * when the user types a number.\n     */\n    // probably not implement, kind of weird\n    if (props.hasLeftFocus.value) {\n      props.hasLeftFocus.value = false\n      prev = null\n    }\n\n    if (prev === null)\n      return { value: num === 0 ? 1 : num, moveToNext }\n\n    const str = prev.toString() + num.toString()\n\n    if (str.length > 4)\n      return { value: num === 0 ? 1 : num, moveToNext }\n\n    if (str.length === 4)\n      moveToNext = true\n\n    const int = Number.parseInt(str)\n    return { value: int, moveToNext }\n  }\n\n  const attributes = computed(() => segmentBuilders[props.part]?.attrs({\n    disabled: props.disabled.value,\n    placeholder: props.placeholder.value,\n    hourCycle: props.hourCycle,\n    segmentValues: props.segmentValues.value,\n    formatter: props.formatter,\n  }) ?? {})\n\n  // TODO: look into abstracting segment keydown functions since they have the same structure (checks -> arrow_up, arrow_down update -> number string update -> move to next -> backspace update)\n  function handleDaySegmentKeydown(e: KeyboardEvent) {\n    if (!isAcceptableSegmentKey(e.key) || isSegmentNavigationKey(e.key))\n      return\n\n    const prevValue = props.segmentValues.value.day\n\n    if (e.key === kbd.ARROW_DOWN || e.key === kbd.ARROW_UP) {\n      props.segmentValues.value.day = dateTimeValueIncrementation({ e, part: 'day', dateRef: props.placeholder.value, prevValue })\n      return\n    }\n\n    if (isNumberString(e.key)) {\n      const num = Number.parseInt(e.key)\n      const segmentMonthValue = props.segmentValues.value.month\n\n      const daysInMonth = segmentMonthValue\n        ? getDaysInMonth(props.placeholder.value.set({ month: segmentMonthValue }))\n        // if the month is not set, we default to the maximum number of days in a month\n        // so that user can input any possible day\n        : 31\n\n      const { value, moveToNext } = updateDayOrMonth(daysInMonth, num, prevValue)\n\n      props.segmentValues.value.day = value\n\n      if (moveToNext)\n        props.focusNext()\n    }\n\n    if (e.key === kbd.BACKSPACE) {\n      props.hasLeftFocus.value = false\n      props.segmentValues.value.day = deleteValue(prevValue)\n    }\n  }\n\n  function handleMonthSegmentKeydown(e: KeyboardEvent) {\n    if (!isAcceptableSegmentKey(e.key) || isSegmentNavigationKey(e.key))\n      return\n\n    const prevValue = props.segmentValues.value.month\n\n    if (e.key === kbd.ARROW_DOWN || e.key === kbd.ARROW_UP) {\n      props.segmentValues.value.month = dateTimeValueIncrementation({ e, part: 'month', dateRef: props.placeholder.value, prevValue })\n      return\n    }\n\n    if (isNumberString(e.key)) {\n      const num = Number.parseInt(e.key)\n      const { value, moveToNext } = updateDayOrMonth(12, num, prevValue)\n\n      props.segmentValues.value.month = value\n\n      if (moveToNext)\n        props.focusNext()\n    }\n\n    if (e.key === kbd.BACKSPACE) {\n      props.hasLeftFocus.value = false\n      props.segmentValues.value.month = deleteValue(prevValue)\n    }\n  }\n\n  function handleYearSegmentKeydown(e: KeyboardEvent) {\n    if (!isAcceptableSegmentKey(e.key) || isSegmentNavigationKey(e.key))\n      return\n\n    const prevValue = props.segmentValues.value.year\n\n    if (e.key === kbd.ARROW_DOWN || e.key === kbd.ARROW_UP) {\n      props.segmentValues.value.year = dateTimeValueIncrementation({ e, part: 'year', dateRef: props.placeholder.value, prevValue })\n      return\n    }\n\n    if (isNumberString(e.key)) {\n      const num = Number.parseInt(e.key)\n      const { value, moveToNext } = updateYear(num, prevValue)\n\n      props.segmentValues.value.year = value\n\n      if (moveToNext)\n        props.focusNext()\n    }\n\n    if (e.key === kbd.BACKSPACE) {\n      props.hasLeftFocus.value = false\n      props.segmentValues.value.year = deleteValue(prevValue)\n    }\n  }\n\n  function handleHourSegmentKeydown(e: KeyboardEvent) {\n    const dateRef = props.placeholder.value\n    if (!isAcceptableSegmentKey(e.key) || isSegmentNavigationKey(e.key) || !('hour' in dateRef) || !('hour' in props.segmentValues.value))\n      return\n\n    const prevValue = props.segmentValues.value.hour\n\n    const hourCycle = props.hourCycle\n\n    if (e.key === kbd.ARROW_UP || e.key === kbd.ARROW_DOWN) {\n      props.segmentValues.value.hour = dateTimeValueIncrementation({ e, part: 'hour', dateRef: props.placeholder.value, prevValue, hourCycle })\n\n      if ('dayPeriod' in props.segmentValues.value) {\n        if (props.segmentValues.value.hour < 12)\n          props.segmentValues.value.dayPeriod = 'AM'\n        else if (props.segmentValues.value.hour)\n          props.segmentValues.value.dayPeriod = 'PM'\n      }\n\n      return\n    }\n\n    if (isNumberString(e.key)) {\n      const num = Number.parseInt(e.key)\n      const { value, moveToNext } = updateHour(num, prevValue)\n\n      if ('dayPeriod' in props.segmentValues.value && value && value > 12)\n        props.segmentValues.value.dayPeriod = 'PM'\n      else if ('dayPeriod' in props.segmentValues.value && value)\n        props.segmentValues.value.dayPeriod = 'AM'\n\n      props.segmentValues.value.hour = value\n\n      if (moveToNext)\n        props.focusNext()\n    }\n\n    if (e.key === kbd.BACKSPACE) {\n      props.hasLeftFocus.value = false\n      props.segmentValues.value.hour = deleteValue(prevValue)\n    }\n  }\n\n  function handleMinuteSegmentKeydown(e: KeyboardEvent) {\n    const dateRef = props.placeholder.value\n\n    if (!isAcceptableSegmentKey(e.key) || isSegmentNavigationKey(e.key) || !('minute' in dateRef) || !('minute' in props.segmentValues.value))\n      return\n\n    const prevValue = props.segmentValues.value.minute\n\n    props.segmentValues.value.minute = minuteSecondIncrementation({ e, part: 'minute', dateRef: props.placeholder.value, prevValue })\n\n    if (isNumberString(e.key)) {\n      const num = Number.parseInt(e.key)\n\n      const { value, moveToNext } = updateMinuteOrSecond(num, prevValue)\n\n      props.segmentValues.value.minute = value\n\n      if (moveToNext)\n        props.focusNext()\n    }\n\n    if (e.key === kbd.BACKSPACE) {\n      props.hasLeftFocus.value = false\n      props.segmentValues.value.minute = deleteValue(prevValue)\n    }\n  }\n\n  function handleSecondSegmentKeydown(e: KeyboardEvent) {\n    const dateRef = props.placeholder.value\n\n    if (!isAcceptableSegmentKey(e.key) || isSegmentNavigationKey(e.key) || !('second' in dateRef) || !('second' in props.segmentValues.value))\n      return\n\n    const prevValue = props.segmentValues.value.second\n\n    props.segmentValues.value.second = minuteSecondIncrementation({ e, part: 'second', dateRef: props.placeholder.value, prevValue })\n\n    if (isNumberString(e.key)) {\n      const num = Number.parseInt(e.key)\n      const { value, moveToNext } = updateMinuteOrSecond(num, prevValue)\n\n      props.segmentValues.value.second = value\n\n      if (moveToNext)\n        props.focusNext()\n    }\n\n    if (e.key === kbd.BACKSPACE) {\n      props.hasLeftFocus.value = false\n      props.segmentValues.value.second = deleteValue(prevValue)\n    }\n  }\n\n  function handleDayPeriodSegmentKeydown(e: KeyboardEvent) {\n    if (((!isAcceptableSegmentKey(e.key) || isSegmentNavigationKey(e.key)) && e.key !== 'a' && e.key !== 'p') || !('hour' in props.placeholder.value) || !('dayPeriod' in props.segmentValues.value))\n      return\n\n    if (e.key === kbd.ARROW_UP || e.key === kbd.ARROW_DOWN) {\n      if (props.segmentValues.value.dayPeriod === 'AM') {\n        props.segmentValues.value.dayPeriod = 'PM'\n        props.segmentValues.value.hour = props.segmentValues.value.hour! + 12\n        return\n      }\n      props.segmentValues.value.dayPeriod = 'AM'\n      props.segmentValues.value.hour = props.segmentValues.value.hour! - 12\n      return\n    }\n\n    if (['a', 'A'].includes(e.key) && props.segmentValues.value.dayPeriod !== 'AM') {\n      props.segmentValues.value.dayPeriod = 'AM'\n      props.segmentValues.value.hour = props.segmentValues.value.hour! - 12\n      return\n    }\n\n    if (['p', 'P'].includes(e.key) && props.segmentValues.value.dayPeriod !== 'PM') {\n      props.segmentValues.value.dayPeriod = 'PM'\n      props.segmentValues.value.hour = props.segmentValues.value.hour! + 12\n    }\n  }\n\n  function handleSegmentClick(e: MouseEvent) {\n    const disabled = props.disabled.value\n    if (disabled)\n      e.preventDefault()\n  }\n\n  function handleSegmentKeydown(e: KeyboardEvent) {\n    const disabled = props.disabled.value\n    const readonly = props.readonly.value\n    if (e.key !== kbd.TAB)\n      e.preventDefault()\n\n    if (disabled || readonly)\n      return\n    const segmentKeydownHandlers = {\n      day: handleDaySegmentKeydown,\n      month: handleMonthSegmentKeydown,\n      year: handleYearSegmentKeydown,\n      hour: handleHourSegmentKeydown,\n      minute: handleMinuteSegmentKeydown,\n      second: handleSecondSegmentKeydown,\n      dayPeriod: handleDayPeriodSegmentKeydown,\n      timeZoneName: () => {},\n    } as const\n\n    segmentKeydownHandlers[props.part as keyof typeof segmentKeydownHandlers](e)\n\n    if (![kbd.ARROW_LEFT, kbd.ARROW_RIGHT].includes(e.key) && e.key !== kbd.TAB && e.key !== kbd.SHIFT && isAcceptableSegmentKey(e.key)) {\n      if (Object.values(props.segmentValues.value).every(item => item !== null)) {\n        const updateObject = { ...props.segmentValues.value as Record<AnyExceptLiteral, number> }\n\n        let dateRef = props.placeholder.value.copy()\n\n        Object.keys(updateObject).forEach((part) => {\n          const value = updateObject[part as AnyExceptLiteral]\n          dateRef = dateRef.set({ [part]: value })\n        })\n\n        props.modelValue.value = dateRef.copy()\n      }\n    }\n  }\n\n  return {\n    handleSegmentClick,\n    handleSegmentKeydown,\n    attributes,\n  }\n}\n"], "names": ["cycleArgs"], "mappings": ";;;;;AAgCA,SAAS,mBAAmB,KAAyB,EAAA;AACnD,EAAO,OAAA;AAAA,IACL,IAAM,EAAA,YAAA;AAAA,IACN,eAAiB,EAAA,IAAA;AAAA,IACjB,QAAA,EAAU,KAAM,CAAA,QAAA,GAAW,MAAY,GAAA,CAAA;AAAA,IACvC,UAAY,EAAA,KAAA;AAAA,IACZ,SAAW,EAAA,SAAA;AAAA,IACX,WAAa,EAAA,KAAA;AAAA,IACb,YAAc,EAAA,MAAA;AAAA,IACd,KAAO,EAAA;AAAA,GACT;AACF;AAEA,SAAS,gBAAgB,KAAyB,EAAA;AAChD,EAAM,MAAA,EAAE,aAAe,EAAA,WAAA,EAAgB,GAAA,KAAA;AACvC,EAAM,MAAA,OAAA,GAAU,cAAc,GAAQ,KAAA,IAAA;AACtC,EAAM,MAAA,IAAA,GAAO,aAAc,CAAA,GAAA,GAAM,WAAY,CAAA,GAAA,CAAI,EAAE,GAAK,EAAA,aAAA,CAAc,GAAI,EAAC,CAAI,GAAA,WAAA;AAE/E,EAAA,MAAM,WAAW,IAAK,CAAA,GAAA;AACtB,EAAA,MAAM,QAAW,GAAA,CAAA;AACjB,EAAM,MAAA,QAAA,GAAW,eAAe,IAAI,CAAA;AACpC,EAAA,MAAM,SAAY,GAAA,OAAA,GAAU,OAAU,GAAA,CAAA,EAAG,QAAQ,CAAA,CAAA;AAEjD,EAAO,OAAA;AAAA,IACL,GAAG,mBAAmB,KAAK,CAAA;AAAA,IAC3B,YAAc,EAAA,MAAA;AAAA,IACd,eAAiB,EAAA,QAAA;AAAA,IACjB,eAAiB,EAAA,QAAA;AAAA,IACjB,eAAiB,EAAA,QAAA;AAAA,IACjB,gBAAkB,EAAA,SAAA;AAAA,IAClB,kBAAA,EAAoB,UAAU,EAAK,GAAA;AAAA,GACrC;AACF;AAEA,SAAS,kBAAkB,KAAyB,EAAA;AAClD,EAAA,MAAM,EAAE,aAAA,EAAe,WAAa,EAAA,SAAA,EAAc,GAAA,KAAA;AAClD,EAAM,MAAA,OAAA,GAAU,cAAc,KAAU,KAAA,IAAA;AACxC,EAAM,MAAA,IAAA,GAAO,aAAc,CAAA,KAAA,GACvB,WAAY,CAAA,GAAA,CAAI,EAAE,KAAO,EAAA,aAAA,CAAc,KAAM,EAAC,CAC9C,GAAA,WAAA;AACJ,EAAA,MAAM,WAAW,IAAK,CAAA,KAAA;AACtB,EAAA,MAAM,QAAW,GAAA,CAAA;AACjB,EAAA,MAAM,QAAW,GAAA,EAAA;AACjB,EAAM,MAAA,SAAA,GAAY,OAAU,GAAA,OAAA,GAAU,CAAG,EAAA,QAAQ,CAAM,GAAA,EAAA,SAAA,CAAU,SAAU,CAAA,MAAA,CAAO,IAAI,CAAC,CAAC,CAAA,CAAA;AAExF,EAAO,OAAA;AAAA,IACL,GAAG,mBAAmB,KAAK,CAAA;AAAA,IAC3B,YAAc,EAAA,SAAA;AAAA,IACd,iBAAmB,EAAA,IAAA;AAAA,IACnB,eAAiB,EAAA,QAAA;AAAA,IACjB,eAAiB,EAAA,QAAA;AAAA,IACjB,eAAiB,EAAA,QAAA;AAAA,IACjB,gBAAkB,EAAA,SAAA;AAAA,IAClB,kBAAA,EAAoB,UAAU,EAAK,GAAA;AAAA,GACrC;AACF;AAEA,SAAS,iBAAiB,KAAyB,EAAA;AACjD,EAAM,MAAA,EAAE,aAAe,EAAA,WAAA,EAAgB,GAAA,KAAA;AACvC,EAAM,MAAA,OAAA,GAAU,cAAc,IAAS,KAAA,IAAA;AACvC,EAAM,MAAA,IAAA,GAAO,aAAc,CAAA,IAAA,GAAO,WAAY,CAAA,GAAA,CAAI,EAAE,IAAM,EAAA,aAAA,CAAc,IAAK,EAAC,CAAI,GAAA,WAAA;AAClF,EAAA,MAAM,QAAW,GAAA,CAAA;AACjB,EAAA,MAAM,QAAW,GAAA,IAAA;AACjB,EAAA,MAAM,WAAW,IAAK,CAAA,IAAA;AACtB,EAAA,MAAM,SAAY,GAAA,OAAA,GAAU,OAAU,GAAA,CAAA,EAAG,QAAQ,CAAA,CAAA;AAEjD,EAAO,OAAA;AAAA,IACL,GAAG,mBAAmB,KAAK,CAAA;AAAA,IAC3B,YAAc,EAAA,QAAA;AAAA,IACd,eAAiB,EAAA,QAAA;AAAA,IACjB,eAAiB,EAAA,QAAA;AAAA,IACjB,eAAiB,EAAA,QAAA;AAAA,IACjB,gBAAkB,EAAA,SAAA;AAAA,IAClB,kBAAA,EAAoB,UAAU,EAAK,GAAA;AAAA,GACrC;AACF;AAEA,SAAS,iBAAiB,KAAyB,EAAA;AACjD,EAAA,MAAM,EAAE,aAAA,EAAe,SAAW,EAAA,WAAA,EAAgB,GAAA,KAAA;AAElD,EAAA,IAAI,EAAE,MAAA,IAAU,aAAkB,CAAA,IAAA,EAAE,MAAU,IAAA,WAAA,CAAA;AAC5C,IAAA,OAAO,EAAC;AACV,EAAM,MAAA,OAAA,GAAU,cAAc,IAAS,KAAA,IAAA;AACvC,EAAM,MAAA,IAAA,GAAO,aAAc,CAAA,IAAA,GAAO,WAAY,CAAA,GAAA,CAAI,EAAE,IAAM,EAAA,aAAA,CAAc,IAAK,EAAC,CAAI,GAAA,WAAA;AAClF,EAAM,MAAA,QAAA,GAAW,SAAc,KAAA,EAAA,GAAK,CAAI,GAAA,CAAA;AACxC,EAAM,MAAA,QAAA,GAAW,SAAc,KAAA,EAAA,GAAK,EAAK,GAAA,EAAA;AACzC,EAAA,MAAM,WAAW,IAAK,CAAA,IAAA;AACtB,EAAM,MAAA,SAAA,GAAY,UAAU,OAAU,GAAA,CAAA,EAAG,QAAQ,CAAI,CAAA,EAAA,aAAA,CAAc,aAAa,EAAE,CAAA,CAAA;AAElF,EAAO,OAAA;AAAA,IACL,GAAG,mBAAmB,KAAK,CAAA;AAAA,IAC3B,YAAc,EAAA,QAAA;AAAA,IACd,eAAiB,EAAA,QAAA;AAAA,IACjB,eAAiB,EAAA,QAAA;AAAA,IACjB,eAAiB,EAAA,QAAA;AAAA,IACjB,gBAAkB,EAAA,SAAA;AAAA,IAClB,kBAAA,EAAoB,UAAU,EAAK,GAAA;AAAA,GACrC;AACF;AAEA,SAAS,mBAAmB,KAAyB,EAAA;AACnD,EAAM,MAAA,EAAE,aAAe,EAAA,WAAA,EAAgB,GAAA,KAAA;AACvC,EAAA,IAAI,EAAE,QAAA,IAAY,aAAkB,CAAA,IAAA,EAAE,QAAY,IAAA,WAAA,CAAA;AAChD,IAAA,OAAO,EAAC;AACV,EAAM,MAAA,OAAA,GAAU,cAAc,MAAW,KAAA,IAAA;AACzC,EAAM,MAAA,IAAA,GAAO,aAAc,CAAA,MAAA,GACvB,WAAY,CAAA,GAAA,CAAI,EAAE,MAAQ,EAAA,aAAA,CAAc,MAAO,EAAC,CAChD,GAAA,WAAA;AACJ,EAAA,MAAM,WAAW,IAAK,CAAA,MAAA;AACtB,EAAA,MAAM,QAAW,GAAA,CAAA;AACjB,EAAA,MAAM,QAAW,GAAA,EAAA;AACjB,EAAA,MAAM,SAAY,GAAA,OAAA,GAAU,OAAU,GAAA,CAAA,EAAG,QAAQ,CAAA,CAAA;AAEjD,EAAO,OAAA;AAAA,IACL,GAAG,mBAAmB,KAAK,CAAA;AAAA,IAC3B,YAAc,EAAA,UAAA;AAAA,IACd,eAAiB,EAAA,QAAA;AAAA,IACjB,eAAiB,EAAA,QAAA;AAAA,IACjB,eAAiB,EAAA,QAAA;AAAA,IACjB,gBAAkB,EAAA,SAAA;AAAA,IAClB,kBAAA,EAAoB,UAAU,EAAK,GAAA;AAAA,GACrC;AACF;AAEA,SAAS,mBAAmB,KAAyB,EAAA;AACnD,EAAM,MAAA,EAAE,aAAe,EAAA,WAAA,EAAgB,GAAA,KAAA;AACvC,EAAA,IAAI,EAAE,QAAA,IAAY,aAAkB,CAAA,IAAA,EAAE,QAAY,IAAA,WAAA,CAAA;AAChD,IAAA,OAAO,EAAC;AACV,EAAM,MAAA,OAAA,GAAU,cAAc,MAAW,KAAA,IAAA;AACzC,EAAM,MAAA,IAAA,GAAO,aAAc,CAAA,MAAA,GACvB,WAAY,CAAA,GAAA,CAAI,EAAE,MAAQ,EAAA,aAAA,CAAc,MAAO,EAAC,CAChD,GAAA,WAAA;AACJ,EAAA,MAAM,WAAW,IAAK,CAAA,MAAA;AACtB,EAAA,MAAM,QAAW,GAAA,CAAA;AACjB,EAAA,MAAM,QAAW,GAAA,EAAA;AACjB,EAAA,MAAM,SAAY,GAAA,OAAA,GAAU,OAAU,GAAA,CAAA,EAAG,QAAQ,CAAA,CAAA;AAEjD,EAAO,OAAA;AAAA,IACL,GAAG,mBAAmB,KAAK,CAAA;AAAA,IAC3B,YAAc,EAAA,UAAA;AAAA,IACd,eAAiB,EAAA,QAAA;AAAA,IACjB,eAAiB,EAAA,QAAA;AAAA,IACjB,eAAiB,EAAA,QAAA;AAAA,IACjB,gBAAkB,EAAA,SAAA;AAAA,IAClB,kBAAA,EAAoB,UAAU,EAAK,GAAA;AAAA,GACrC;AACF;AAEA,SAAS,sBAAsB,KAAyB,EAAA;AACtD,EAAM,MAAA,EAAE,eAAkB,GAAA,KAAA;AAC1B,EAAA,IAAI,EAAE,WAAe,IAAA,aAAA,CAAA;AACnB,IAAA,OAAO,EAAC;AAEV,EAAA,MAAM,QAAW,GAAA,CAAA;AACjB,EAAA,MAAM,QAAW,GAAA,EAAA;AACjB,EAAM,MAAA,QAAA,GAAW,aAAc,CAAA,IAAA,GAAQ,aAAc,CAAA,IAAA,GAAO,KAAK,aAAc,CAAA,IAAA,GAAO,EAAK,GAAA,aAAA,CAAc,IAAQ,GAAA,CAAA;AACjH,EAAM,MAAA,SAAA,GAAY,cAAc,SAAa,IAAA,IAAA;AAE7C,EAAO,OAAA;AAAA,IACL,GAAG,mBAAmB,KAAK,CAAA;AAAA,IAC3B,WAAa,EAAA,MAAA;AAAA,IACb,YAAc,EAAA,OAAA;AAAA,IACd,eAAiB,EAAA,QAAA;AAAA,IACjB,eAAiB,EAAA,QAAA;AAAA,IACjB,eAAiB,EAAA,QAAA;AAAA,IACjB,gBAAkB,EAAA;AAAA,GACpB;AACF;AAEA,SAAS,oBAAoB,MAA0B,EAAA;AACrD,EAAO,OAAA;AAAA,IACL,aAAe,EAAA,IAAA;AAAA,IACf,cAAgB,EAAA;AAAA,GAClB;AACF;AAEA,SAAS,qBAAqB,KAAyB,EAAA;AACrD,EAAO,OAAA;AAAA,IACL,MAAQ,EAAA,SAAA;AAAA,IACR,YAAc,EAAA,YAAA;AAAA,IACd,eAAiB,EAAA,IAAA;AAAA,IACjB,cAAgB,EAAA,cAAA;AAAA,IAChB,UAAA,EAAY,KAAM,CAAA,QAAA,GAAW,MAAY,GAAA,CAAA;AAAA,IACzC,OAAS,EAAA;AAAA,GACX;AACF;AAEA,SAAS,gBAAgB,KAAyB,EAAA;AAChD,EAAM,MAAA,EAAE,aAAe,EAAA,WAAA,EAAgB,GAAA,KAAA;AAEvC,EAAA,MAAM,QAAW,GAAA,CAAA;AACjB,EAAA,MAAM,QAAW,GAAA,CAAA;AACjB,EAAA,MAAM,QAAW,GAAA,CAAA;AACjB,EAAA,MAAM,SAAY,GAAA,KAAA,IAAS,aAAgB,GAAA,aAAA,CAAc,MAAM,WAAY,CAAA,GAAA;AAE3E,EAAO,OAAA;AAAA,IACL,GAAG,mBAAmB,KAAK,CAAA;AAAA,IAC3B,YAAc,EAAA,KAAA;AAAA,IACd,eAAiB,EAAA,QAAA;AAAA,IACjB,eAAiB,EAAA,QAAA;AAAA,IACjB,eAAiB,EAAA,QAAA;AAAA,IACjB,gBAAkB,EAAA;AAAA,GACpB;AACF;AAEO,MAAM,eAAkB,GAAA;AAAA,EAC7B,GAAK,EAAA;AAAA,IACH,KAAO,EAAA;AAAA,GACT;AAAA,EACA,KAAO,EAAA;AAAA,IACL,KAAO,EAAA;AAAA,GACT;AAAA,EACA,IAAM,EAAA;AAAA,IACJ,KAAO,EAAA;AAAA,GACT;AAAA,EACA,IAAM,EAAA;AAAA,IACJ,KAAO,EAAA;AAAA,GACT;AAAA,EACA,MAAQ,EAAA;AAAA,IACN,KAAO,EAAA;AAAA,GACT;AAAA,EACA,MAAQ,EAAA;AAAA,IACN,KAAO,EAAA;AAAA,GACT;AAAA,EACA,SAAW,EAAA;AAAA,IACT,KAAO,EAAA;AAAA,GACT;AAAA,EACA,OAAS,EAAA;AAAA,IACP,KAAO,EAAA;AAAA,GACT;AAAA,EACA,YAAc,EAAA;AAAA,IACZ,KAAO,EAAA;AAAA,GACT;AAAA,EACA,GAAK,EAAA;AAAA,IACH,KAAO,EAAA;AAAA;AAEX,CAAA;AAiBO,SAAS,aAAa,KAA0B,EAAA;AACrD,EAAA,MAAM,MAAM,MAAO,EAAA;AAEnB,EAAA,SAAS,2BAA2B,EAAE,CAAA,EAAG,IAAM,EAAA,OAAA,EAAS,WAAiD,EAAA;AACvG,IAAA,MAAM,IAAO,GAAA,KAAA,CAAM,IAAK,CAAA,KAAA,CAAM,IAAI,CAAK,IAAA,CAAA;AACvC,IAAA,MAAM,OAAO,CAAE,CAAA,GAAA,KAAQ,GAAI,CAAA,QAAA,GAAW,OAAO,CAAC,IAAA;AAC9C,IAAA,MAAM,GAAM,GAAA,CAAA;AACZ,IAAA,MAAM,GAAM,GAAA,EAAA;AAEZ,IAAA,IAAI,SAAc,KAAA,IAAA;AAChB,MAAO,OAAA,IAAA,GAAO,IAAI,GAAM,GAAA,GAAA;AAE1B,IAAM,MAAA,SAAA,GAAwC,CAAC,IAAA,EAAM,IAAI,CAAA;AACzD,IAAA,OAAQ,OAA6B,CAAA,GAAA,CAAI,EAAE,CAAC,IAAI,GAAG,SAAU,EAAC,CAAE,CAAA,KAAA,CAAM,GAAG,SAAS,EAAE,IAAI,CAAA;AAAA;AAG1F,EAAA,SAAS,YAAY,SAA0B,EAAA;AAC7C,IAAA,KAAA,CAAM,aAAa,KAAQ,GAAA,KAAA;AAC3B,IAAA,IAAI,SAAc,KAAA,IAAA;AAChB,MAAO,OAAA,SAAA;AAET,IAAM,MAAA,GAAA,GAAM,UAAU,QAAS,EAAA;AAC/B,IAAI,IAAA,GAAA,CAAI,WAAW,CAAG,EAAA;AACpB,MAAA,KAAA,CAAM,WAAW,KAAQ,GAAA,MAAA;AACzB,MAAO,OAAA,IAAA;AAAA;AAGT,IAAA,OAAO,OAAO,QAAS,CAAA,GAAA,CAAI,KAAM,CAAA,CAAA,EAAG,EAAE,CAAC,CAAA;AAAA;AAEzC,EAAA,SAAS,4BAA4B,EAAE,CAAA,EAAG,MAAM,OAAS,EAAA,SAAA,EAAW,WAAkD,EAAA;AACpH,IAAA,MAAM,IAAO,GAAA,KAAA,CAAM,IAAK,CAAA,KAAA,CAAM,IAAI,CAAK,IAAA,CAAA;AACvC,IAAA,MAAM,OAAO,CAAE,CAAA,GAAA,KAAQ,GAAI,CAAA,QAAA,GAAW,OAAO,CAAC,IAAA;AAE9C,IAAA,IAAI,SAAc,KAAA,IAAA;AAChB,MAAA,OAAO,QAAQ,IAAqC,CAAA;AAEtD,IAAI,IAAA,IAAA,KAAS,MAAU,IAAA,MAAA,IAAU,OAAS,EAAA;AACxC,MAAA,MAAMA,aAA8E,CAAC,IAAA,EAAM,IAAM,EAAA,EAAE,WAAW,CAAA;AAC9G,MAAA,OAAO,OAAQ,CAAA,GAAA,CAAI,EAAE,CAAC,IAAuB,GAAG,SAAU,EAAC,CAAE,CAAA,KAAA,CAAM,GAAGA,UAAS,EAAE,IAAI,CAAA;AAAA;AAGvF,IAAM,MAAA,SAAA,GAAwC,CAAC,IAAA,EAA0B,IAAI,CAAA;AAC7E,IAAA,IAAI,SAAS,KAAO,EAAA;AAClB,MAAA,OAAO,QAAQ,GAAI,CAAA;AAAA,QACjB,CAAC,IAAuB,GAAG,SAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAU3B,KAAO,EAAA,KAAA,CAAM,aAAc,CAAA,KAAA,CAAM,KAAS,IAAA;AAAA,OAC3C,CAAE,CAAA,KAAA,CAAM,GAAG,SAAS,EAAE,IAAqC,CAAA;AAAA;AAG9D,IAAA,OAAO,OAAQ,CAAA,GAAA,CAAI,EAAE,CAAC,IAAuB,GAAG,SAAU,EAAC,CAAE,CAAA,KAAA,CAAM,GAAG,SAAS,EAAE,IAAqC,CAAA;AAAA;AAExH,EAAS,SAAA,gBAAA,CAAiB,GAAa,EAAA,GAAA,EAAa,IAAqB,EAAA;AACvE,IAAA,IAAI,UAAa,GAAA,KAAA;AACjB,IAAA,MAAM,QAAW,GAAA,IAAA,CAAK,KAAM,CAAA,GAAA,GAAM,EAAE,CAAA;AAOpC,IAAI,IAAA,KAAA,CAAM,aAAa,KAAO,EAAA;AAC5B,MAAA,KAAA,CAAM,aAAa,KAAQ,GAAA,KAAA;AAC3B,MAAO,IAAA,GAAA,IAAA;AAAA;AAGT,IAAA,IAAI,SAAS,IAAM,EAAA;AAOjB,MAAA,IAAI,QAAQ,CAAG,EAAA;AACb,QAAA,KAAA,CAAM,YAAY,KAAQ,GAAA,IAAA;AAC1B,QAAO,OAAA,EAAE,KAAO,EAAA,IAAA,EAAM,UAAW,EAAA;AAAA;AASnC,MAAA,IAAI,KAAM,CAAA,WAAA,CAAY,KAAS,IAAA,GAAA,GAAM,QAAU,EAAA;AAE7C,QAAa,UAAA,GAAA,IAAA;AAAA;AAEf,MAAA,KAAA,CAAM,YAAY,KAAQ,GAAA,KAAA;AAM1B,MAAO,OAAA,EAAE,KAAO,EAAA,GAAA,EAAK,UAAW,EAAA;AAAA;AASlC,IAAM,MAAA,MAAA,GAAS,IAAK,CAAA,QAAA,EAAW,CAAA,MAAA;AAC/B,IAAM,MAAA,KAAA,GAAQ,OAAO,QAAS,CAAA,IAAA,CAAK,UAAa,GAAA,GAAA,CAAI,UAAU,CAAA;AAQ9D,IAAI,IAAA,MAAA,KAAW,CAAK,IAAA,KAAA,GAAQ,GAAK,EAAA;AAM/B,MAAI,IAAA,GAAA,GAAM,QAAY,IAAA,KAAA,GAAQ,GAAK,EAAA;AAEjC,QAAa,UAAA,GAAA,IAAA;AAAA;AAEf,MAAO,OAAA,EAAE,KAAO,EAAA,GAAA,EAAK,UAAW,EAAA;AAAA;AAGlC,IAAa,UAAA,GAAA,IAAA;AACb,IAAO,OAAA,EAAE,KAAO,EAAA,KAAA,EAAO,UAAW,EAAA;AAAA;AAGpC,EAAS,SAAA,oBAAA,CAAqB,KAAa,IAAqB,EAAA;AAC9D,IAAA,MAAM,GAAM,GAAA,EAAA;AACZ,IAAA,IAAI,UAAa,GAAA,KAAA;AACjB,IAAA,MAAM,QAAW,GAAA,IAAA,CAAK,KAAM,CAAA,GAAA,GAAM,EAAE,CAAA;AAOpC,IAAI,IAAA,KAAA,CAAM,aAAa,KAAO,EAAA;AAC5B,MAAA,KAAA,CAAM,aAAa,KAAQ,GAAA,KAAA;AAC3B,MAAO,IAAA,GAAA,IAAA;AAAA;AAGT,IAAA,IAAI,SAAS,IAAM,EAAA;AAOjB,MAAA,IAAI,QAAQ,CAAG,EAAA;AACb,QAAA,KAAA,CAAM,YAAY,KAAQ,GAAA,IAAA;AAC1B,QAAO,OAAA,EAAE,KAAO,EAAA,CAAA,EAAG,UAAW,EAAA;AAAA;AAShC,MAAA,IAAI,KAAM,CAAA,WAAA,CAAY,KAAS,IAAA,GAAA,GAAM,QAAU,EAAA;AAE7C,QAAa,UAAA,GAAA,IAAA;AAAA;AAEf,MAAA,KAAA,CAAM,YAAY,KAAQ,GAAA,KAAA;AAM1B,MAAO,OAAA,EAAE,KAAO,EAAA,GAAA,EAAK,UAAW,EAAA;AAAA;AASlC,IAAM,MAAA,MAAA,GAAS,IAAK,CAAA,QAAA,EAAW,CAAA,MAAA;AAC/B,IAAM,MAAA,KAAA,GAAQ,OAAO,QAAS,CAAA,IAAA,CAAK,UAAa,GAAA,GAAA,CAAI,UAAU,CAAA;AAS9D,IAAI,IAAA,MAAA,KAAW,CAAK,IAAA,KAAA,GAAQ,GAAK,EAAA;AAM/B,MAAA,IAAI,MAAM,QAAU,EAAA;AAElB,QAAa,UAAA,GAAA,IAAA;AAAA;AAEf,MAAO,OAAA,EAAE,KAAO,EAAA,GAAA,EAAK,UAAW,EAAA;AAAA;AAGlC,IAAa,UAAA,GAAA,IAAA;AACb,IAAO,OAAA,EAAE,KAAO,EAAA,KAAA,EAAO,UAAW,EAAA;AAAA;AAGpC,EAAS,SAAA,UAAA,CAAW,KAAa,IAAqB,EAAA;AACpD,IAAA,MAAM,GAAM,GAAA,EAAA;AACZ,IAAA,IAAI,UAAa,GAAA,KAAA;AACjB,IAAA,MAAM,QAAW,GAAA,IAAA,CAAK,KAAM,CAAA,GAAA,GAAM,EAAE,CAAA;AAQpC,IAAI,IAAA,KAAA,CAAM,aAAa,KAAO,EAAA;AAC5B,MAAA,KAAA,CAAM,aAAa,KAAQ,GAAA,KAAA;AAC3B,MAAO,IAAA,GAAA,IAAA;AAAA;AAGT,IAAA,IAAI,SAAS,IAAM,EAAA;AAOjB,MAAA,IAAI,QAAQ,CAAG,EAAA;AACb,QAAA,KAAA,CAAM,YAAY,KAAQ,GAAA,IAAA;AAC1B,QAAO,OAAA,EAAE,KAAO,EAAA,CAAA,EAAG,UAAW,EAAA;AAAA;AAShC,MAAA,IAAI,KAAM,CAAA,WAAA,CAAY,KAAS,IAAA,GAAA,GAAM,QAAU,EAAA;AAE7C,QAAa,UAAA,GAAA,IAAA;AAAA;AAEf,MAAA,KAAA,CAAM,YAAY,KAAQ,GAAA,KAAA;AAM1B,MAAO,OAAA,EAAE,KAAO,EAAA,GAAA,EAAK,UAAW,EAAA;AAAA;AASlC,IAAM,MAAA,MAAA,GAAS,IAAK,CAAA,QAAA,EAAW,CAAA,MAAA;AAC/B,IAAM,MAAA,KAAA,GAAQ,OAAO,QAAS,CAAA,IAAA,CAAK,UAAa,GAAA,GAAA,CAAI,UAAU,CAAA;AAS9D,IAAI,IAAA,MAAA,KAAW,CAAK,IAAA,KAAA,GAAQ,GAAK,EAAA;AAM/B,MAAA,IAAI,MAAM,QAAU,EAAA;AAElB,QAAa,UAAA,GAAA,IAAA;AAAA;AAEf,MAAO,OAAA,EAAE,KAAO,EAAA,GAAA,EAAK,UAAW,EAAA;AAAA;AAGlC,IAAa,UAAA,GAAA,IAAA;AACb,IAAO,OAAA,EAAE,KAAO,EAAA,KAAA,EAAO,UAAW,EAAA;AAAA;AAGpC,EAAS,SAAA,UAAA,CAAW,KAAa,IAAqB,EAAA;AACpD,IAAA,IAAI,UAAa,GAAA,KAAA;AAQjB,IAAI,IAAA,KAAA,CAAM,aAAa,KAAO,EAAA;AAC5B,MAAA,KAAA,CAAM,aAAa,KAAQ,GAAA,KAAA;AAC3B,MAAO,IAAA,GAAA,IAAA;AAAA;AAGT,IAAA,IAAI,IAAS,KAAA,IAAA;AACX,MAAA,OAAO,EAAE,KAAO,EAAA,GAAA,KAAQ,CAAI,GAAA,CAAA,GAAI,KAAK,UAAW,EAAA;AAElD,IAAA,MAAM,GAAM,GAAA,IAAA,CAAK,QAAS,EAAA,GAAI,IAAI,QAAS,EAAA;AAE3C,IAAA,IAAI,IAAI,MAAS,GAAA,CAAA;AACf,MAAA,OAAO,EAAE,KAAO,EAAA,GAAA,KAAQ,CAAI,GAAA,CAAA,GAAI,KAAK,UAAW,EAAA;AAElD,IAAA,IAAI,IAAI,MAAW,KAAA,CAAA;AACjB,MAAa,UAAA,GAAA,IAAA;AAEf,IAAM,MAAA,GAAA,GAAM,MAAO,CAAA,QAAA,CAAS,GAAG,CAAA;AAC/B,IAAO,OAAA,EAAE,KAAO,EAAA,GAAA,EAAK,UAAW,EAAA;AAAA;AAGlC,EAAA,MAAM,aAAa,QAAS,CAAA,MAAM,gBAAgB,KAAM,CAAA,IAAI,GAAG,KAAM,CAAA;AAAA,IACnE,QAAA,EAAU,MAAM,QAAS,CAAA,KAAA;AAAA,IACzB,WAAA,EAAa,MAAM,WAAY,CAAA,KAAA;AAAA,IAC/B,WAAW,KAAM,CAAA,SAAA;AAAA,IACjB,aAAA,EAAe,MAAM,aAAc,CAAA,KAAA;AAAA,IACnC,WAAW,KAAM,CAAA;AAAA,GAClB,CAAK,IAAA,EAAE,CAAA;AAGR,EAAA,SAAS,wBAAwB,CAAkB,EAAA;AACjD,IAAA,IAAI,CAAC,sBAAuB,CAAA,CAAA,CAAE,GAAG,CAAK,IAAA,sBAAA,CAAuB,EAAE,GAAG,CAAA;AAChE,MAAA;AAEF,IAAM,MAAA,SAAA,GAAY,KAAM,CAAA,aAAA,CAAc,KAAM,CAAA,GAAA;AAE5C,IAAA,IAAI,EAAE,GAAQ,KAAA,GAAA,CAAI,cAAc,CAAE,CAAA,GAAA,KAAQ,IAAI,QAAU,EAAA;AACtD,MAAA,KAAA,CAAM,aAAc,CAAA,KAAA,CAAM,GAAM,GAAA,2BAAA,CAA4B,EAAE,CAAA,EAAG,IAAM,EAAA,KAAA,EAAO,OAAS,EAAA,KAAA,CAAM,WAAY,CAAA,KAAA,EAAO,WAAW,CAAA;AAC3H,MAAA;AAAA;AAGF,IAAI,IAAA,cAAA,CAAe,CAAE,CAAA,GAAG,CAAG,EAAA;AACzB,MAAA,MAAM,GAAM,GAAA,MAAA,CAAO,QAAS,CAAA,CAAA,CAAE,GAAG,CAAA;AACjC,MAAM,MAAA,iBAAA,GAAoB,KAAM,CAAA,aAAA,CAAc,KAAM,CAAA,KAAA;AAEpD,MAAA,MAAM,WAAc,GAAA,iBAAA,GAChB,cAAe,CAAA,KAAA,CAAM,WAAY,CAAA,KAAA,CAAM,GAAI,CAAA,EAAE,KAAO,EAAA,iBAAA,EAAmB,CAAC,CAGxE,GAAA,EAAA;AAEJ,MAAA,MAAM,EAAE,KAAO,EAAA,UAAA,KAAe,gBAAiB,CAAA,WAAA,EAAa,KAAK,SAAS,CAAA;AAE1E,MAAM,KAAA,CAAA,aAAA,CAAc,MAAM,GAAM,GAAA,KAAA;AAEhC,MAAI,IAAA,UAAA;AACF,QAAA,KAAA,CAAM,SAAU,EAAA;AAAA;AAGpB,IAAI,IAAA,CAAA,CAAE,GAAQ,KAAA,GAAA,CAAI,SAAW,EAAA;AAC3B,MAAA,KAAA,CAAM,aAAa,KAAQ,GAAA,KAAA;AAC3B,MAAA,KAAA,CAAM,aAAc,CAAA,KAAA,CAAM,GAAM,GAAA,WAAA,CAAY,SAAS,CAAA;AAAA;AACvD;AAGF,EAAA,SAAS,0BAA0B,CAAkB,EAAA;AACnD,IAAA,IAAI,CAAC,sBAAuB,CAAA,CAAA,CAAE,GAAG,CAAK,IAAA,sBAAA,CAAuB,EAAE,GAAG,CAAA;AAChE,MAAA;AAEF,IAAM,MAAA,SAAA,GAAY,KAAM,CAAA,aAAA,CAAc,KAAM,CAAA,KAAA;AAE5C,IAAA,IAAI,EAAE,GAAQ,KAAA,GAAA,CAAI,cAAc,CAAE,CAAA,GAAA,KAAQ,IAAI,QAAU,EAAA;AACtD,MAAA,KAAA,CAAM,aAAc,CAAA,KAAA,CAAM,KAAQ,GAAA,2BAAA,CAA4B,EAAE,CAAA,EAAG,IAAM,EAAA,OAAA,EAAS,OAAS,EAAA,KAAA,CAAM,WAAY,CAAA,KAAA,EAAO,WAAW,CAAA;AAC/H,MAAA;AAAA;AAGF,IAAI,IAAA,cAAA,CAAe,CAAE,CAAA,GAAG,CAAG,EAAA;AACzB,MAAA,MAAM,GAAM,GAAA,MAAA,CAAO,QAAS,CAAA,CAAA,CAAE,GAAG,CAAA;AACjC,MAAA,MAAM,EAAE,KAAO,EAAA,UAAA,KAAe,gBAAiB,CAAA,EAAA,EAAI,KAAK,SAAS,CAAA;AAEjE,MAAM,KAAA,CAAA,aAAA,CAAc,MAAM,KAAQ,GAAA,KAAA;AAElC,MAAI,IAAA,UAAA;AACF,QAAA,KAAA,CAAM,SAAU,EAAA;AAAA;AAGpB,IAAI,IAAA,CAAA,CAAE,GAAQ,KAAA,GAAA,CAAI,SAAW,EAAA;AAC3B,MAAA,KAAA,CAAM,aAAa,KAAQ,GAAA,KAAA;AAC3B,MAAA,KAAA,CAAM,aAAc,CAAA,KAAA,CAAM,KAAQ,GAAA,WAAA,CAAY,SAAS,CAAA;AAAA;AACzD;AAGF,EAAA,SAAS,yBAAyB,CAAkB,EAAA;AAClD,IAAA,IAAI,CAAC,sBAAuB,CAAA,CAAA,CAAE,GAAG,CAAK,IAAA,sBAAA,CAAuB,EAAE,GAAG,CAAA;AAChE,MAAA;AAEF,IAAM,MAAA,SAAA,GAAY,KAAM,CAAA,aAAA,CAAc,KAAM,CAAA,IAAA;AAE5C,IAAA,IAAI,EAAE,GAAQ,KAAA,GAAA,CAAI,cAAc,CAAE,CAAA,GAAA,KAAQ,IAAI,QAAU,EAAA;AACtD,MAAA,KAAA,CAAM,aAAc,CAAA,KAAA,CAAM,IAAO,GAAA,2BAAA,CAA4B,EAAE,CAAA,EAAG,IAAM,EAAA,MAAA,EAAQ,OAAS,EAAA,KAAA,CAAM,WAAY,CAAA,KAAA,EAAO,WAAW,CAAA;AAC7H,MAAA;AAAA;AAGF,IAAI,IAAA,cAAA,CAAe,CAAE,CAAA,GAAG,CAAG,EAAA;AACzB,MAAA,MAAM,GAAM,GAAA,MAAA,CAAO,QAAS,CAAA,CAAA,CAAE,GAAG,CAAA;AACjC,MAAA,MAAM,EAAE,KAAO,EAAA,UAAA,EAAe,GAAA,UAAA,CAAW,KAAK,SAAS,CAAA;AAEvD,MAAM,KAAA,CAAA,aAAA,CAAc,MAAM,IAAO,GAAA,KAAA;AAEjC,MAAI,IAAA,UAAA;AACF,QAAA,KAAA,CAAM,SAAU,EAAA;AAAA;AAGpB,IAAI,IAAA,CAAA,CAAE,GAAQ,KAAA,GAAA,CAAI,SAAW,EAAA;AAC3B,MAAA,KAAA,CAAM,aAAa,KAAQ,GAAA,KAAA;AAC3B,MAAA,KAAA,CAAM,aAAc,CAAA,KAAA,CAAM,IAAO,GAAA,WAAA,CAAY,SAAS,CAAA;AAAA;AACxD;AAGF,EAAA,SAAS,yBAAyB,CAAkB,EAAA;AAClD,IAAM,MAAA,OAAA,GAAU,MAAM,WAAY,CAAA,KAAA;AAClC,IAAA,IAAI,CAAC,sBAAA,CAAuB,CAAE,CAAA,GAAG,KAAK,sBAAuB,CAAA,CAAA,CAAE,GAAG,CAAA,IAAK,EAAE,MAAU,IAAA,OAAA,CAAA,IAAY,EAAE,MAAA,IAAU,MAAM,aAAc,CAAA,KAAA,CAAA;AAC7H,MAAA;AAEF,IAAM,MAAA,SAAA,GAAY,KAAM,CAAA,aAAA,CAAc,KAAM,CAAA,IAAA;AAE5C,IAAA,MAAM,YAAY,KAAM,CAAA,SAAA;AAExB,IAAA,IAAI,EAAE,GAAQ,KAAA,GAAA,CAAI,YAAY,CAAE,CAAA,GAAA,KAAQ,IAAI,UAAY,EAAA;AACtD,MAAA,KAAA,CAAM,aAAc,CAAA,KAAA,CAAM,IAAO,GAAA,2BAAA,CAA4B,EAAE,CAAG,EAAA,IAAA,EAAM,MAAQ,EAAA,OAAA,EAAS,KAAM,CAAA,WAAA,CAAY,KAAO,EAAA,SAAA,EAAW,WAAW,CAAA;AAExI,MAAI,IAAA,WAAA,IAAe,KAAM,CAAA,aAAA,CAAc,KAAO,EAAA;AAC5C,QAAI,IAAA,KAAA,CAAM,aAAc,CAAA,KAAA,CAAM,IAAO,GAAA,EAAA;AACnC,UAAM,KAAA,CAAA,aAAA,CAAc,MAAM,SAAY,GAAA,IAAA;AAAA,aAC/B,IAAA,KAAA,CAAM,cAAc,KAAM,CAAA,IAAA;AACjC,UAAM,KAAA,CAAA,aAAA,CAAc,MAAM,SAAY,GAAA,IAAA;AAAA;AAG1C,MAAA;AAAA;AAGF,IAAI,IAAA,cAAA,CAAe,CAAE,CAAA,GAAG,CAAG,EAAA;AACzB,MAAA,MAAM,GAAM,GAAA,MAAA,CAAO,QAAS,CAAA,CAAA,CAAE,GAAG,CAAA;AACjC,MAAA,MAAM,EAAE,KAAO,EAAA,UAAA,EAAe,GAAA,UAAA,CAAW,KAAK,SAAS,CAAA;AAEvD,MAAA,IAAI,WAAe,IAAA,KAAA,CAAM,aAAc,CAAA,KAAA,IAAS,SAAS,KAAQ,GAAA,EAAA;AAC/D,QAAM,KAAA,CAAA,aAAA,CAAc,MAAM,SAAY,GAAA,IAAA;AAAA,WAC/B,IAAA,WAAA,IAAe,KAAM,CAAA,aAAA,CAAc,KAAS,IAAA,KAAA;AACnD,QAAM,KAAA,CAAA,aAAA,CAAc,MAAM,SAAY,GAAA,IAAA;AAExC,MAAM,KAAA,CAAA,aAAA,CAAc,MAAM,IAAO,GAAA,KAAA;AAEjC,MAAI,IAAA,UAAA;AACF,QAAA,KAAA,CAAM,SAAU,EAAA;AAAA;AAGpB,IAAI,IAAA,CAAA,CAAE,GAAQ,KAAA,GAAA,CAAI,SAAW,EAAA;AAC3B,MAAA,KAAA,CAAM,aAAa,KAAQ,GAAA,KAAA;AAC3B,MAAA,KAAA,CAAM,aAAc,CAAA,KAAA,CAAM,IAAO,GAAA,WAAA,CAAY,SAAS,CAAA;AAAA;AACxD;AAGF,EAAA,SAAS,2BAA2B,CAAkB,EAAA;AACpD,IAAM,MAAA,OAAA,GAAU,MAAM,WAAY,CAAA,KAAA;AAElC,IAAA,IAAI,CAAC,sBAAA,CAAuB,CAAE,CAAA,GAAG,KAAK,sBAAuB,CAAA,CAAA,CAAE,GAAG,CAAA,IAAK,EAAE,QAAY,IAAA,OAAA,CAAA,IAAY,EAAE,QAAA,IAAY,MAAM,aAAc,CAAA,KAAA,CAAA;AACjI,MAAA;AAEF,IAAM,MAAA,SAAA,GAAY,KAAM,CAAA,aAAA,CAAc,KAAM,CAAA,MAAA;AAE5C,IAAA,KAAA,CAAM,aAAc,CAAA,KAAA,CAAM,MAAS,GAAA,0BAAA,CAA2B,EAAE,CAAA,EAAG,IAAM,EAAA,QAAA,EAAU,OAAS,EAAA,KAAA,CAAM,WAAY,CAAA,KAAA,EAAO,WAAW,CAAA;AAEhI,IAAI,IAAA,cAAA,CAAe,CAAE,CAAA,GAAG,CAAG,EAAA;AACzB,MAAA,MAAM,GAAM,GAAA,MAAA,CAAO,QAAS,CAAA,CAAA,CAAE,GAAG,CAAA;AAEjC,MAAA,MAAM,EAAE,KAAO,EAAA,UAAA,EAAe,GAAA,oBAAA,CAAqB,KAAK,SAAS,CAAA;AAEjE,MAAM,KAAA,CAAA,aAAA,CAAc,MAAM,MAAS,GAAA,KAAA;AAEnC,MAAI,IAAA,UAAA;AACF,QAAA,KAAA,CAAM,SAAU,EAAA;AAAA;AAGpB,IAAI,IAAA,CAAA,CAAE,GAAQ,KAAA,GAAA,CAAI,SAAW,EAAA;AAC3B,MAAA,KAAA,CAAM,aAAa,KAAQ,GAAA,KAAA;AAC3B,MAAA,KAAA,CAAM,aAAc,CAAA,KAAA,CAAM,MAAS,GAAA,WAAA,CAAY,SAAS,CAAA;AAAA;AAC1D;AAGF,EAAA,SAAS,2BAA2B,CAAkB,EAAA;AACpD,IAAM,MAAA,OAAA,GAAU,MAAM,WAAY,CAAA,KAAA;AAElC,IAAA,IAAI,CAAC,sBAAA,CAAuB,CAAE,CAAA,GAAG,KAAK,sBAAuB,CAAA,CAAA,CAAE,GAAG,CAAA,IAAK,EAAE,QAAY,IAAA,OAAA,CAAA,IAAY,EAAE,QAAA,IAAY,MAAM,aAAc,CAAA,KAAA,CAAA;AACjI,MAAA;AAEF,IAAM,MAAA,SAAA,GAAY,KAAM,CAAA,aAAA,CAAc,KAAM,CAAA,MAAA;AAE5C,IAAA,KAAA,CAAM,aAAc,CAAA,KAAA,CAAM,MAAS,GAAA,0BAAA,CAA2B,EAAE,CAAA,EAAG,IAAM,EAAA,QAAA,EAAU,OAAS,EAAA,KAAA,CAAM,WAAY,CAAA,KAAA,EAAO,WAAW,CAAA;AAEhI,IAAI,IAAA,cAAA,CAAe,CAAE,CAAA,GAAG,CAAG,EAAA;AACzB,MAAA,MAAM,GAAM,GAAA,MAAA,CAAO,QAAS,CAAA,CAAA,CAAE,GAAG,CAAA;AACjC,MAAA,MAAM,EAAE,KAAO,EAAA,UAAA,EAAe,GAAA,oBAAA,CAAqB,KAAK,SAAS,CAAA;AAEjE,MAAM,KAAA,CAAA,aAAA,CAAc,MAAM,MAAS,GAAA,KAAA;AAEnC,MAAI,IAAA,UAAA;AACF,QAAA,KAAA,CAAM,SAAU,EAAA;AAAA;AAGpB,IAAI,IAAA,CAAA,CAAE,GAAQ,KAAA,GAAA,CAAI,SAAW,EAAA;AAC3B,MAAA,KAAA,CAAM,aAAa,KAAQ,GAAA,KAAA;AAC3B,MAAA,KAAA,CAAM,aAAc,CAAA,KAAA,CAAM,MAAS,GAAA,WAAA,CAAY,SAAS,CAAA;AAAA;AAC1D;AAGF,EAAA,SAAS,8BAA8B,CAAkB,EAAA;AACvD,IAAM,IAAA,CAAA,CAAC,uBAAuB,CAAE,CAAA,GAAG,KAAK,sBAAuB,CAAA,CAAA,CAAE,GAAG,CAAA,KAAM,CAAE,CAAA,GAAA,KAAQ,OAAO,CAAE,CAAA,GAAA,KAAQ,GAAQ,IAAA,EAAE,MAAU,IAAA,KAAA,CAAM,YAAY,KAAU,CAAA,IAAA,EAAE,WAAe,IAAA,KAAA,CAAM,aAAc,CAAA,KAAA,CAAA;AACxL,MAAA;AAEF,IAAA,IAAI,EAAE,GAAQ,KAAA,GAAA,CAAI,YAAY,CAAE,CAAA,GAAA,KAAQ,IAAI,UAAY,EAAA;AACtD,MAAA,IAAI,KAAM,CAAA,aAAA,CAAc,KAAM,CAAA,SAAA,KAAc,IAAM,EAAA;AAChD,QAAM,KAAA,CAAA,aAAA,CAAc,MAAM,SAAY,GAAA,IAAA;AACtC,QAAA,KAAA,CAAM,cAAc,KAAM,CAAA,IAAA,GAAO,KAAM,CAAA,aAAA,CAAc,MAAM,IAAQ,GAAA,EAAA;AACnE,QAAA;AAAA;AAEF,MAAM,KAAA,CAAA,aAAA,CAAc,MAAM,SAAY,GAAA,IAAA;AACtC,MAAA,KAAA,CAAM,cAAc,KAAM,CAAA,IAAA,GAAO,KAAM,CAAA,aAAA,CAAc,MAAM,IAAQ,GAAA,EAAA;AACnE,MAAA;AAAA;AAGF,IAAA,IAAI,CAAC,GAAA,EAAK,GAAG,CAAA,CAAE,QAAS,CAAA,CAAA,CAAE,GAAG,CAAA,IAAK,KAAM,CAAA,aAAA,CAAc,KAAM,CAAA,SAAA,KAAc,IAAM,EAAA;AAC9E,MAAM,KAAA,CAAA,aAAA,CAAc,MAAM,SAAY,GAAA,IAAA;AACtC,MAAA,KAAA,CAAM,cAAc,KAAM,CAAA,IAAA,GAAO,KAAM,CAAA,aAAA,CAAc,MAAM,IAAQ,GAAA,EAAA;AACnE,MAAA;AAAA;AAGF,IAAA,IAAI,CAAC,GAAA,EAAK,GAAG,CAAA,CAAE,QAAS,CAAA,CAAA,CAAE,GAAG,CAAA,IAAK,KAAM,CAAA,aAAA,CAAc,KAAM,CAAA,SAAA,KAAc,IAAM,EAAA;AAC9E,MAAM,KAAA,CAAA,aAAA,CAAc,MAAM,SAAY,GAAA,IAAA;AACtC,MAAA,KAAA,CAAM,cAAc,KAAM,CAAA,IAAA,GAAO,KAAM,CAAA,aAAA,CAAc,MAAM,IAAQ,GAAA,EAAA;AAAA;AACrE;AAGF,EAAA,SAAS,mBAAmB,CAAe,EAAA;AACzC,IAAM,MAAA,QAAA,GAAW,MAAM,QAAS,CAAA,KAAA;AAChC,IAAI,IAAA,QAAA;AACF,MAAA,CAAA,CAAE,cAAe,EAAA;AAAA;AAGrB,EAAA,SAAS,qBAAqB,CAAkB,EAAA;AAC9C,IAAM,MAAA,QAAA,GAAW,MAAM,QAAS,CAAA,KAAA;AAChC,IAAM,MAAA,QAAA,GAAW,MAAM,QAAS,CAAA,KAAA;AAChC,IAAI,IAAA,CAAA,CAAE,QAAQ,GAAI,CAAA,GAAA;AAChB,MAAA,CAAA,CAAE,cAAe,EAAA;AAEnB,IAAA,IAAI,QAAY,IAAA,QAAA;AACd,MAAA;AACF,IAAA,MAAM,sBAAyB,GAAA;AAAA,MAC7B,GAAK,EAAA,uBAAA;AAAA,MACL,KAAO,EAAA,yBAAA;AAAA,MACP,IAAM,EAAA,wBAAA;AAAA,MACN,IAAM,EAAA,wBAAA;AAAA,MACN,MAAQ,EAAA,0BAAA;AAAA,MACR,MAAQ,EAAA,0BAAA;AAAA,MACR,SAAW,EAAA,6BAAA;AAAA,MACX,cAAc,MAAM;AAAA;AAAC,KACvB;AAEA,IAAuB,sBAAA,CAAA,KAAA,CAAM,IAA2C,CAAA,CAAE,CAAC,CAAA;AAE3E,IAAI,IAAA,CAAC,CAAC,GAAI,CAAA,UAAA,EAAY,IAAI,WAAW,CAAA,CAAE,QAAS,CAAA,CAAA,CAAE,GAAG,CAAA,IAAK,EAAE,GAAQ,KAAA,GAAA,CAAI,OAAO,CAAE,CAAA,GAAA,KAAQ,IAAI,KAAS,IAAA,sBAAA,CAAuB,CAAE,CAAA,GAAG,CAAG,EAAA;AACnI,MAAI,IAAA,MAAA,CAAO,MAAO,CAAA,KAAA,CAAM,aAAc,CAAA,KAAK,EAAE,KAAM,CAAA,CAAA,IAAA,KAAQ,IAAS,KAAA,IAAI,CAAG,EAAA;AACzE,QAAA,MAAM,YAAe,GAAA,EAAE,GAAG,KAAA,CAAM,cAAc,KAA0C,EAAA;AAExF,QAAA,IAAI,OAAU,GAAA,KAAA,CAAM,WAAY,CAAA,KAAA,CAAM,IAAK,EAAA;AAE3C,QAAA,MAAA,CAAO,IAAK,CAAA,YAAY,CAAE,CAAA,OAAA,CAAQ,CAAC,IAAS,KAAA;AAC1C,UAAM,MAAA,KAAA,GAAQ,aAAa,IAAwB,CAAA;AACnD,UAAA,OAAA,GAAU,QAAQ,GAAI,CAAA,EAAE,CAAC,IAAI,GAAG,OAAO,CAAA;AAAA,SACxC,CAAA;AAED,QAAM,KAAA,CAAA,UAAA,CAAW,KAAQ,GAAA,OAAA,CAAQ,IAAK,EAAA;AAAA;AACxC;AACF;AAGF,EAAO,OAAA;AAAA,IACL,kBAAA;AAAA,IACA,oBAAA;AAAA,IACA;AAAA,GACF;AACF;;;;"}