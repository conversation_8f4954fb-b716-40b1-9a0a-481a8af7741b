{"name": "illuminate/notifications", "description": "The Illuminate Notifications package.", "license": "MIT", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": "^8.2", "illuminate/broadcasting": "^12.0", "illuminate/bus": "^12.0", "illuminate/collections": "^12.0", "illuminate/container": "^12.0", "illuminate/contracts": "^12.0", "illuminate/filesystem": "^12.0", "illuminate/mail": "^12.0", "illuminate/queue": "^12.0", "illuminate/support": "^12.0"}, "autoload": {"psr-4": {"Illuminate\\Notifications\\": ""}}, "extra": {"branch-alias": {"dev-master": "12.x-dev"}}, "suggest": {"illuminate/database": "Required to use the database transport (^12.0)."}, "config": {"sort-packages": true}, "minimum-stability": "dev"}