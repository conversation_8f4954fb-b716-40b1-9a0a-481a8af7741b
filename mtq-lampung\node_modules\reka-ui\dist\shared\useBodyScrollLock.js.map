{"version": 3, "file": "useBodyScrollLock.js", "sources": ["../../src/shared/useBodyScrollLock.ts"], "sourcesContent": ["import type { Fn } from '@vueuse/shared'\nimport {\n  createSharedComposable,\n  useEventListener,\n} from '@vueuse/core'\nimport { isClient, isIOS, tryOnBeforeUnmount } from '@vueuse/shared'\nimport { defu } from 'defu'\nimport { computed, nextTick, ref, watch } from 'vue'\nimport { injectConfigProviderContext } from '@/ConfigProvider/ConfigProvider.vue'\n\nconst useBodyLockStackCount = createSharedComposable(() => {\n  const map = ref<Map<string, boolean>>(new Map())\n  const initialOverflow = ref<string | undefined>()\n\n  const locked = computed(() => {\n    for (const value of map.value.values()) {\n      if (value)\n        return true\n    }\n    return false\n  })\n\n  const context = injectConfigProviderContext({\n    scrollBody: ref(true),\n  })\n\n  let stopTouchMoveListener: Fn | null = null\n\n  const resetBodyStyle = () => {\n    document.body.style.paddingRight = ''\n    document.body.style.marginRight = ''\n    document.body.style.pointerEvents = ''\n    document.documentElement.style.removeProperty('--scrollbar-width')\n    document.body.style.overflow = initialOverflow.value ?? ''\n    isIOS && stopTouchMoveListener?.()\n\n    initialOverflow.value = undefined\n  }\n\n  watch(locked, (val, oldVal) => {\n    if (!isClient)\n      return\n\n    if (!val) {\n      if (oldVal)\n        resetBodyStyle()\n      return\n    }\n\n    if (initialOverflow.value === undefined)\n      initialOverflow.value = document.body.style.overflow\n\n    const verticalScrollbarWidth = window.innerWidth - document.documentElement.clientWidth\n    const defaultConfig = { padding: verticalScrollbarWidth, margin: 0 }\n\n    const config = context.scrollBody?.value\n      ? typeof context.scrollBody.value === 'object'\n        ? defu({\n            padding: context.scrollBody.value.padding === true ? verticalScrollbarWidth : context.scrollBody.value.padding,\n            margin: context.scrollBody.value.margin === true ? verticalScrollbarWidth : context.scrollBody.value.margin,\n          }, defaultConfig)\n        : defaultConfig\n      : ({ padding: 0, margin: 0 })\n\n    if (verticalScrollbarWidth > 0) {\n      document.body.style.paddingRight = typeof config.padding === 'number' ? `${config.padding}px` : String(config.padding)\n      document.body.style.marginRight = typeof config.margin === 'number' ? `${config.margin}px` : String(config.margin)\n      document.documentElement.style.setProperty('--scrollbar-width', `${verticalScrollbarWidth}px`)\n      document.body.style.overflow = 'hidden'\n    }\n\n    if (isIOS) {\n      stopTouchMoveListener = useEventListener(\n        document,\n        'touchmove',\n        (e: TouchEvent) => preventDefault(e),\n        { passive: false },\n      )\n    }\n\n    // let dismissibleLayer set previous pointerEvent first\n    nextTick(() => {\n      document.body.style.pointerEvents = 'none'\n      document.body.style.overflow = 'hidden'\n    })\n  }, { immediate: true, flush: 'sync' })\n\n  return map\n})\n\nexport function useBodyScrollLock(initialState?: boolean | undefined) {\n  const id = Math.random().toString(36).substring(2, 7) // just simple random id, need not to be cryptographically secure\n  const map = useBodyLockStackCount()\n\n  map.value.set(id, initialState ?? false)\n\n  const locked = computed({\n    get: () => map.value.get(id) ?? false,\n    set: value => map.value.set(id, value),\n  })\n\n  tryOnBeforeUnmount(() => {\n    map.value.delete(id)\n  })\n\n  return locked\n}\n\n// Adapt from https://github.com/vueuse/vueuse/blob/main/packages/core/useScrollLock/index.ts#L28C10-L28C24\nfunction checkOverflowScroll(ele: Element): boolean {\n  const style = window.getComputedStyle(ele)\n  if (\n    style.overflowX === 'scroll'\n    || style.overflowY === 'scroll'\n    || (style.overflowX === 'auto' && ele.clientWidth < ele.scrollWidth)\n    || (style.overflowY === 'auto' && ele.clientHeight < ele.scrollHeight)\n  ) {\n    return true\n  }\n  else {\n    const parent = ele.parentNode\n\n    if (!(parent instanceof Element) || parent.tagName === 'BODY')\n      return false\n\n    return checkOverflowScroll(parent)\n  }\n}\n\nfunction preventDefault(rawEvent: TouchEvent): boolean {\n  const e = rawEvent || window.event\n\n  const _target = e.target\n\n  // Do not prevent if element or parentNodes have overflow: scroll set.\n  if (_target instanceof Element && checkOverflowScroll(_target))\n    return false\n\n  // Do not prevent if the event has more than one touch (usually meaning this is a multi touch gesture like pinch to zoom).\n  if (e.touches.length > 1)\n    return true\n\n  if (e.preventDefault && e.cancelable)\n    e.preventDefault()\n\n  return false\n}\n"], "names": [], "mappings": ";;;;;;AAUA,MAAM,qBAAA,GAAwB,uBAAuB,MAAM;AACzD,EAAA,MAAM,GAAM,GAAA,GAAA,iBAA8B,IAAA,GAAA,EAAK,CAAA;AAC/C,EAAA,MAAM,kBAAkB,GAAwB,EAAA;AAEhD,EAAM,MAAA,MAAA,GAAS,SAAS,MAAM;AAC5B,IAAA,KAAA,MAAW,KAAS,IAAA,GAAA,CAAI,KAAM,CAAA,MAAA,EAAU,EAAA;AACtC,MAAI,IAAA,KAAA;AACF,QAAO,OAAA,IAAA;AAAA;AAEX,IAAO,OAAA,KAAA;AAAA,GACR,CAAA;AAED,EAAA,MAAM,UAAU,2BAA4B,CAAA;AAAA,IAC1C,UAAA,EAAY,IAAI,IAAI;AAAA,GACrB,CAAA;AAED,EAAA,IAAI,qBAAmC,GAAA,IAAA;AAEvC,EAAA,MAAM,iBAAiB,MAAM;AAC3B,IAAS,QAAA,CAAA,IAAA,CAAK,MAAM,YAAe,GAAA,EAAA;AACnC,IAAS,QAAA,CAAA,IAAA,CAAK,MAAM,WAAc,GAAA,EAAA;AAClC,IAAS,QAAA,CAAA,IAAA,CAAK,MAAM,aAAgB,GAAA,EAAA;AACpC,IAAS,QAAA,CAAA,eAAA,CAAgB,KAAM,CAAA,cAAA,CAAe,mBAAmB,CAAA;AACjE,IAAA,QAAA,CAAS,IAAK,CAAA,KAAA,CAAM,QAAW,GAAA,eAAA,CAAgB,KAAS,IAAA,EAAA;AACxD,IAAA,KAAA,IAAS,qBAAwB,IAAA;AAEjC,IAAA,eAAA,CAAgB,KAAQ,GAAA,MAAA;AAAA,GAC1B;AAEA,EAAM,KAAA,CAAA,MAAA,EAAQ,CAAC,GAAA,EAAK,MAAW,KAAA;AAC7B,IAAA,IAAI,CAAC,QAAA;AACH,MAAA;AAEF,IAAA,IAAI,CAAC,GAAK,EAAA;AACR,MAAI,IAAA,MAAA;AACF,QAAe,cAAA,EAAA;AACjB,MAAA;AAAA;AAGF,IAAA,IAAI,gBAAgB,KAAU,KAAA,MAAA;AAC5B,MAAgB,eAAA,CAAA,KAAA,GAAQ,QAAS,CAAA,IAAA,CAAK,KAAM,CAAA,QAAA;AAE9C,IAAA,MAAM,sBAAyB,GAAA,MAAA,CAAO,UAAa,GAAA,QAAA,CAAS,eAAgB,CAAA,WAAA;AAC5E,IAAA,MAAM,aAAgB,GAAA,EAAE,OAAS,EAAA,sBAAA,EAAwB,QAAQ,CAAE,EAAA;AAEnE,IAAM,MAAA,MAAA,GAAS,QAAQ,UAAY,EAAA,KAAA,GAC/B,OAAO,OAAQ,CAAA,UAAA,CAAW,KAAU,KAAA,QAAA,GAClC,IAAK,CAAA;AAAA,MACH,OAAA,EAAS,QAAQ,UAAW,CAAA,KAAA,CAAM,YAAY,IAAO,GAAA,sBAAA,GAAyB,OAAQ,CAAA,UAAA,CAAW,KAAM,CAAA,OAAA;AAAA,MACvG,MAAA,EAAQ,QAAQ,UAAW,CAAA,KAAA,CAAM,WAAW,IAAO,GAAA,sBAAA,GAAyB,OAAQ,CAAA,UAAA,CAAW,KAAM,CAAA;AAAA,KACvG,EAAG,aAAa,CAChB,GAAA,aAAA,GACD,EAAE,OAAS,EAAA,CAAA,EAAG,QAAQ,CAAE,EAAA;AAE7B,IAAA,IAAI,yBAAyB,CAAG,EAAA;AAC9B,MAAA,QAAA,CAAS,IAAK,CAAA,KAAA,CAAM,YAAe,GAAA,OAAO,MAAO,CAAA,OAAA,KAAY,QAAW,GAAA,CAAA,EAAG,MAAO,CAAA,OAAO,CAAO,EAAA,CAAA,GAAA,MAAA,CAAO,OAAO,OAAO,CAAA;AACrH,MAAA,QAAA,CAAS,IAAK,CAAA,KAAA,CAAM,WAAc,GAAA,OAAO,MAAO,CAAA,MAAA,KAAW,QAAW,GAAA,CAAA,EAAG,MAAO,CAAA,MAAM,CAAO,EAAA,CAAA,GAAA,MAAA,CAAO,OAAO,MAAM,CAAA;AACjH,MAAA,QAAA,CAAS,gBAAgB,KAAM,CAAA,WAAA,CAAY,mBAAqB,EAAA,CAAA,EAAG,sBAAsB,CAAI,EAAA,CAAA,CAAA;AAC7F,MAAS,QAAA,CAAA,IAAA,CAAK,MAAM,QAAW,GAAA,QAAA;AAAA;AAGjC,IAAA,IAAI,KAAO,EAAA;AACT,MAAwB,qBAAA,GAAA,gBAAA;AAAA,QACtB,QAAA;AAAA,QACA,WAAA;AAAA,QACA,CAAC,CAAkB,KAAA,cAAA,CAAe,CAAC,CAAA;AAAA,QACnC,EAAE,SAAS,KAAM;AAAA,OACnB;AAAA;AAIF,IAAA,QAAA,CAAS,MAAM;AACb,MAAS,QAAA,CAAA,IAAA,CAAK,MAAM,aAAgB,GAAA,MAAA;AACpC,MAAS,QAAA,CAAA,IAAA,CAAK,MAAM,QAAW,GAAA,QAAA;AAAA,KAChC,CAAA;AAAA,KACA,EAAE,SAAA,EAAW,IAAM,EAAA,KAAA,EAAO,QAAQ,CAAA;AAErC,EAAO,OAAA,GAAA;AACT,CAAC,CAAA;AAEM,SAAS,kBAAkB,YAAoC,EAAA;AACpE,EAAM,MAAA,EAAA,GAAK,KAAK,MAAO,EAAA,CAAE,SAAS,EAAE,CAAA,CAAE,SAAU,CAAA,CAAA,EAAG,CAAC,CAAA;AACpD,EAAA,MAAM,MAAM,qBAAsB,EAAA;AAElC,EAAA,GAAA,CAAI,KAAM,CAAA,GAAA,CAAI,EAAI,EAAA,YAAA,IAAgB,KAAK,CAAA;AAEvC,EAAA,MAAM,SAAS,QAAS,CAAA;AAAA,IACtB,KAAK,MAAM,GAAA,CAAI,KAAM,CAAA,GAAA,CAAI,EAAE,CAAK,IAAA,KAAA;AAAA,IAChC,KAAK,CAAS,KAAA,KAAA,GAAA,CAAI,KAAM,CAAA,GAAA,CAAI,IAAI,KAAK;AAAA,GACtC,CAAA;AAED,EAAA,kBAAA,CAAmB,MAAM;AACvB,IAAI,GAAA,CAAA,KAAA,CAAM,OAAO,EAAE,CAAA;AAAA,GACpB,CAAA;AAED,EAAO,OAAA,MAAA;AACT;AAGA,SAAS,oBAAoB,GAAuB,EAAA;AAClD,EAAM,MAAA,KAAA,GAAQ,MAAO,CAAA,gBAAA,CAAiB,GAAG,CAAA;AACzC,EAAA,IACE,MAAM,SAAc,KAAA,QAAA,IACjB,MAAM,SAAc,KAAA,QAAA,IACnB,MAAM,SAAc,KAAA,MAAA,IAAU,IAAI,WAAc,GAAA,GAAA,CAAI,eACpD,KAAM,CAAA,SAAA,KAAc,UAAU,GAAI,CAAA,YAAA,GAAe,IAAI,YACzD,EAAA;AACA,IAAO,OAAA,IAAA;AAAA,GAEJ,MAAA;AACH,IAAA,MAAM,SAAS,GAAI,CAAA,UAAA;AAEnB,IAAA,IAAI,EAAE,MAAA,YAAkB,OAAY,CAAA,IAAA,MAAA,CAAO,OAAY,KAAA,MAAA;AACrD,MAAO,OAAA,KAAA;AAET,IAAA,OAAO,oBAAoB,MAAM,CAAA;AAAA;AAErC;AAEA,SAAS,eAAe,QAA+B,EAAA;AACrD,EAAM,MAAA,CAAA,GAAI,YAAY,MAAO,CAAA,KAAA;AAE7B,EAAA,MAAM,UAAU,CAAE,CAAA,MAAA;AAGlB,EAAI,IAAA,OAAA,YAAmB,OAAW,IAAA,mBAAA,CAAoB,OAAO,CAAA;AAC3D,IAAO,OAAA,KAAA;AAGT,EAAI,IAAA,CAAA,CAAE,QAAQ,MAAS,GAAA,CAAA;AACrB,IAAO,OAAA,IAAA;AAET,EAAI,IAAA,CAAA,CAAE,kBAAkB,CAAE,CAAA,UAAA;AACxB,IAAA,CAAA,CAAE,cAAe,EAAA;AAEnB,EAAO,OAAA,KAAA;AACT;;;;"}