{"version": 3, "file": "TreeVirtualizer.js", "sources": ["../../src/Tree/TreeVirtualizer.vue"], "sourcesContent": ["<script lang=\"ts\">\nexport interface TreeVirtualizerProps {\n  /** Number of items rendered outside the visible area */\n  overscan?: number\n  /** Estimated size (in px) of each item */\n  estimateSize?: number\n  /** Text content for each item to achieve type-ahead feature */\n  textContent?: (item: Record<string, any>) => string\n}\n</script>\n\n<script setup lang=\"ts\">\nimport type { VirtualItem, Virtualizer } from '@tanstack/vue-virtual'\nimport type { Ref } from 'vue'\nimport type { FlattenedItem } from './TreeRoot.vue'\nimport { useVirtualizer } from '@tanstack/vue-virtual'\nimport { refAutoReset, useParentElement } from '@vueuse/core'\nimport { cloneVNode, computed, nextTick, useSlots } from 'vue'\nimport { useCollection } from '@/Collection'\nimport { MAP_KEY_TO_FOCUS_INTENT } from '@/RovingFocus/utils'\nimport { getActiveElement } from '@/shared'\nimport { getNextMatch } from '@/shared/useTypeahead'\nimport { injectTreeRootContext } from './TreeRoot.vue'\n\nconst props = defineProps<TreeVirtualizerProps>()\n\ndefineSlots<{\n  default?: (props: {\n    item: FlattenedItem<Record<string, any>>\n    virtualizer: Virtualizer<Element | Window, Element>\n    virtualItem: VirtualItem\n  }) => any\n}>()\n\nconst slots = useSlots()\nconst rootContext = injectTreeRootContext()\nconst parentEl = useParentElement() as Ref<HTMLElement>\nconst { getItems } = useCollection()\n\n// Reset `search` 1 second after it was last updated\nconst search = refAutoReset('', 1000)\nconst optionsWithMetadata = computed(() => {\n  const parseTextContent = (option: Record<string, any>) => {\n    if (props.textContent)\n      return props.textContent(option)\n    else\n      return option.toString().toLowerCase()\n  }\n\n  return rootContext.expandedItems.value.map((option, index) => ({\n    index,\n    textContent: parseTextContent(option.value),\n  }))\n})\n\n// set virtual true when this component mounted\nrootContext.isVirtual.value = true\n\nconst padding = computed(() => {\n  const el = parentEl.value\n  if (!el) {\n    return { start: 0, end: 0 }\n  }\n  else {\n    const styles = window.getComputedStyle(el)\n    return {\n      start: Number.parseFloat(styles.paddingBlockStart || styles.paddingTop),\n      end: Number.parseFloat(styles.paddingBlockEnd || styles.paddingBottom),\n    }\n  }\n})\n\nconst virtualizer = useVirtualizer(\n  {\n    get scrollPaddingStart() { return padding.value.start },\n    get scrollPaddingEnd() { return padding.value.end },\n    get count() { return rootContext.expandedItems.value.length ?? 0 },\n    get horizontal() { return false },\n    getItemKey(index) {\n      return index + rootContext.getKey(rootContext.expandedItems.value[index].value)\n    },\n    estimateSize() {\n      return props.estimateSize ?? 28\n    },\n    getScrollElement() { return parentEl.value },\n    overscan: props.overscan ?? 12,\n  },\n)\n\nconst virtualizedItems = computed(() => virtualizer.value.getVirtualItems().map((item) => {\n  return {\n    item,\n    is: cloneVNode(slots.default!({\n      item: rootContext.expandedItems.value[item.index],\n      virtualizer: virtualizer.value,\n      virtualItem: item,\n    })![0], {\n      'data-index': item.index,\n      'style': {\n        position: 'absolute',\n        top: 0,\n        left: 0,\n        transform: `translateY(${item.start}px)`,\n        overflowAnchor: 'none',\n      },\n    }),\n  }\n}))\n\nfunction scrollToIndexAndFocus(index: number) {\n  virtualizer.value.scrollToIndex(index, { align: 'start' })\n  requestAnimationFrame(() => {\n    const item = parentEl.value.querySelector(`[data-index=\"${index}\"]`) as HTMLElement\n    if (item instanceof HTMLElement)\n      item.focus()\n  })\n}\n\nrootContext.virtualKeydownHook.on((event) => {\n  const isMetaKey = event.altKey || event.ctrlKey || event.metaKey\n  const isTabKey = event.key === 'Tab' && !isMetaKey\n  if (isTabKey)\n    return\n\n  const intent = MAP_KEY_TO_FOCUS_INTENT[event.key]\n\n  if (['first', 'last'].includes(intent)) {\n    event.preventDefault()\n\n    const index = intent === 'first' ? 0 : rootContext.expandedItems.value.length - 1\n    virtualizer.value.scrollToIndex(index)\n    requestAnimationFrame(() => {\n      const items = getItems()\n      const item = intent === 'first' ? items[0] : items[items.length - 1]\n      item.ref.focus()\n    })\n  }\n  else if (intent === 'prev' && event.key !== 'ArrowUp') {\n    const currentElement = getActiveElement() as HTMLElement\n    const currentIndex = Number(currentElement.getAttribute('data-index'))\n    const currentLevel = Number(currentElement.getAttribute('data-indent'))\n    const list = rootContext.expandedItems.value.slice(0, currentIndex).map((item, index) => ({ ...item, index })).reverse()\n\n    const parentItem = list.find(item => item.level === (currentLevel - 1))\n    if (parentItem)\n      scrollToIndexAndFocus(parentItem.index)\n  }\n  else if (!intent && !isMetaKey) {\n    search.value += event.key\n    const currentIndex = Number(getActiveElement()?.getAttribute('data-index'))\n    const currentMatch = optionsWithMetadata.value[currentIndex].textContent\n    const filteredOptions = optionsWithMetadata.value.map(i => i.textContent)\n    const next = getNextMatch(filteredOptions, search.value, currentMatch)\n\n    const nextMatch = optionsWithMetadata.value.find(option => option.textContent === next)\n    if (nextMatch)\n      scrollToIndexAndFocus(nextMatch.index)\n  }\n\n  nextTick(() => {\n    if (event.shiftKey && intent)\n      rootContext.handleMultipleReplace(intent, getActiveElement(), getItems, rootContext.expandedItems.value.map(i => i.value))\n  })\n})\n</script>\n\n<template>\n  <div\n    data-reka-virtualizer\n    :style=\"{\n      position: 'relative',\n      width: '100%',\n      height: `${virtualizer.getTotalSize()}px`,\n    }\"\n  >\n    <component\n      :is=\"is\"\n      v-for=\"{ is, item } in virtualizedItems\"\n      :key=\"item.key\"\n    />\n  </div>\n</template>\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAwBA,IAAA,MAAM,KAAQ,GAAA,OAAA;AAUd,IAAA,MAAM,QAAQ,QAAS,EAAA;AACvB,IAAA,MAAM,cAAc,qBAAsB,EAAA;AAC1C,IAAA,MAAM,WAAW,gBAAiB,EAAA;AAClC,IAAM,MAAA,EAAE,QAAS,EAAA,GAAI,aAAc,EAAA;AAGnC,IAAM,MAAA,MAAA,GAAS,YAAa,CAAA,EAAA,EAAI,GAAI,CAAA;AACpC,IAAM,MAAA,mBAAA,GAAsB,SAAS,MAAM;AACzC,MAAM,MAAA,gBAAA,GAAmB,CAAC,MAAgC,KAAA;AACxD,QAAA,IAAI,KAAM,CAAA,WAAA;AACR,UAAO,OAAA,KAAA,CAAM,YAAY,MAAM,CAAA;AAAA;AAE/B,UAAO,OAAA,MAAA,CAAO,QAAS,EAAA,CAAE,WAAY,EAAA;AAAA,OACzC;AAEA,MAAA,OAAO,YAAY,aAAc,CAAA,KAAA,CAAM,GAAI,CAAA,CAAC,QAAQ,KAAW,MAAA;AAAA,QAC7D,KAAA;AAAA,QACA,WAAA,EAAa,gBAAiB,CAAA,MAAA,CAAO,KAAK;AAAA,OAC1C,CAAA,CAAA;AAAA,KACH,CAAA;AAGD,IAAA,WAAA,CAAY,UAAU,KAAQ,GAAA,IAAA;AAE9B,IAAM,MAAA,OAAA,GAAU,SAAS,MAAM;AAC7B,MAAA,MAAM,KAAK,QAAS,CAAA,KAAA;AACpB,MAAA,IAAI,CAAC,EAAI,EAAA;AACP,QAAA,OAAO,EAAE,KAAA,EAAO,CAAG,EAAA,GAAA,EAAK,CAAE,EAAA;AAAA,OAEvB,MAAA;AACH,QAAM,MAAA,MAAA,GAAS,MAAO,CAAA,gBAAA,CAAiB,EAAE,CAAA;AACzC,QAAO,OAAA;AAAA,UACL,OAAO,MAAO,CAAA,UAAA,CAAW,MAAO,CAAA,iBAAA,IAAqB,OAAO,UAAU,CAAA;AAAA,UACtE,KAAK,MAAO,CAAA,UAAA,CAAW,MAAO,CAAA,eAAA,IAAmB,OAAO,aAAa;AAAA,SACvE;AAAA;AACF,KACD,CAAA;AAED,IAAA,MAAM,WAAc,GAAA,cAAA;AAAA,MAClB;AAAA,QACE,IAAI,kBAAqB,GAAA;AAAE,UAAA,OAAO,QAAQ,KAAM,CAAA,KAAA;AAAA,SAAM;AAAA,QACtD,IAAI,gBAAmB,GAAA;AAAE,UAAA,OAAO,QAAQ,KAAM,CAAA,GAAA;AAAA,SAAI;AAAA,QAClD,IAAI,KAAQ,GAAA;AAAE,UAAO,OAAA,WAAA,CAAY,aAAc,CAAA,KAAA,CAAM,MAAU,IAAA,CAAA;AAAA,SAAE;AAAA,QACjE,IAAI,UAAa,GAAA;AAAE,UAAO,OAAA,KAAA;AAAA,SAAM;AAAA,QAChC,WAAW,KAAO,EAAA;AAChB,UAAO,OAAA,KAAA,GAAQ,YAAY,MAAO,CAAA,WAAA,CAAY,cAAc,KAAM,CAAA,KAAK,EAAE,KAAK,CAAA;AAAA,SAChF;AAAA,QACA,YAAe,GAAA;AACb,UAAA,OAAO,MAAM,YAAgB,IAAA,EAAA;AAAA,SAC/B;AAAA,QACA,gBAAmB,GAAA;AAAE,UAAA,OAAO,QAAS,CAAA,KAAA;AAAA,SAAM;AAAA,QAC3C,QAAA,EAAU,MAAM,QAAY,IAAA;AAAA;AAC9B,KACF;AAEA,IAAM,MAAA,gBAAA,GAAmB,SAAS,MAAM,WAAA,CAAY,MAAM,eAAgB,EAAA,CAAE,GAAI,CAAA,CAAC,IAAS,KAAA;AACxF,MAAO,OAAA;AAAA,QACL,IAAA;AAAA,QACA,EAAA,EAAI,UAAW,CAAA,KAAA,CAAM,OAAS,CAAA;AAAA,UAC5B,IAAM,EAAA,WAAA,CAAY,aAAc,CAAA,KAAA,CAAM,KAAK,KAAK,CAAA;AAAA,UAChD,aAAa,WAAY,CAAA,KAAA;AAAA,UACzB,WAAa,EAAA;AAAA,SACd,CAAG,CAAA,CAAC,CAAG,EAAA;AAAA,UACN,cAAc,IAAK,CAAA,KAAA;AAAA,UACnB,OAAS,EAAA;AAAA,YACP,QAAU,EAAA,UAAA;AAAA,YACV,GAAK,EAAA,CAAA;AAAA,YACL,IAAM,EAAA,CAAA;AAAA,YACN,SAAA,EAAW,CAAc,WAAA,EAAA,IAAA,CAAK,KAAK,CAAA,GAAA,CAAA;AAAA,YACnC,cAAgB,EAAA;AAAA;AAClB,SACD;AAAA,OACH;AAAA,KACD,CAAC,CAAA;AAEF,IAAA,SAAS,sBAAsB,KAAe,EAAA;AAC5C,MAAA,WAAA,CAAY,MAAM,aAAc,CAAA,KAAA,EAAO,EAAE,KAAA,EAAO,SAAS,CAAA;AACzD,MAAA,qBAAA,CAAsB,MAAM;AAC1B,QAAA,MAAM,OAAO,QAAS,CAAA,KAAA,CAAM,aAAc,CAAA,CAAA,aAAA,EAAgB,KAAK,CAAI,EAAA,CAAA,CAAA;AACnE,QAAA,IAAI,IAAgB,YAAA,WAAA;AAClB,UAAA,IAAA,CAAK,KAAM,EAAA;AAAA,OACd,CAAA;AAAA;AAGH,IAAY,WAAA,CAAA,kBAAA,CAAmB,EAAG,CAAA,CAAC,KAAU,KAAA;AAC3C,MAAA,MAAM,SAAY,GAAA,KAAA,CAAM,MAAU,IAAA,KAAA,CAAM,WAAW,KAAM,CAAA,OAAA;AACzD,MAAA,MAAM,QAAW,GAAA,KAAA,CAAM,GAAQ,KAAA,KAAA,IAAS,CAAC,SAAA;AACzC,MAAI,IAAA,QAAA;AACF,QAAA;AAEF,MAAM,MAAA,MAAA,GAAS,uBAAwB,CAAA,KAAA,CAAM,GAAG,CAAA;AAEhD,MAAA,IAAI,CAAC,OAAS,EAAA,MAAM,CAAE,CAAA,QAAA,CAAS,MAAM,CAAG,EAAA;AACtC,QAAA,KAAA,CAAM,cAAe,EAAA;AAErB,QAAA,MAAM,QAAQ,MAAW,KAAA,OAAA,GAAU,IAAI,WAAY,CAAA,aAAA,CAAc,MAAM,MAAS,GAAA,CAAA;AAChF,QAAY,WAAA,CAAA,KAAA,CAAM,cAAc,KAAK,CAAA;AACrC,QAAA,qBAAA,CAAsB,MAAM;AAC1B,UAAA,MAAM,QAAQ,QAAS,EAAA;AACvB,UAAM,MAAA,IAAA,GAAO,WAAW,OAAU,GAAA,KAAA,CAAM,CAAC,CAAI,GAAA,KAAA,CAAM,KAAM,CAAA,MAAA,GAAS,CAAC,CAAA;AACnE,UAAA,IAAA,CAAK,IAAI,KAAM,EAAA;AAAA,SAChB,CAAA;AAAA,OAEM,MAAA,IAAA,MAAA,KAAW,MAAU,IAAA,KAAA,CAAM,QAAQ,SAAW,EAAA;AACrD,QAAA,MAAM,iBAAiB,gBAAiB,EAAA;AACxC,QAAA,MAAM,YAAe,GAAA,MAAA,CAAO,cAAe,CAAA,YAAA,CAAa,YAAY,CAAC,CAAA;AACrE,QAAA,MAAM,YAAe,GAAA,MAAA,CAAO,cAAe,CAAA,YAAA,CAAa,aAAa,CAAC,CAAA;AACtE,QAAA,MAAM,OAAO,WAAY,CAAA,aAAA,CAAc,MAAM,KAAM,CAAA,CAAA,EAAG,YAAY,CAAE,CAAA,GAAA,CAAI,CAAC,IAAA,EAAM,WAAW,EAAE,GAAG,MAAM,KAAM,EAAA,CAAE,EAAE,OAAQ,EAAA;AAEvH,QAAA,MAAM,aAAa,IAAK,CAAA,IAAA,CAAK,UAAQ,IAAK,CAAA,KAAA,KAAW,eAAe,CAAE,CAAA;AACtE,QAAI,IAAA,UAAA;AACF,UAAA,qBAAA,CAAsB,WAAW,KAAK,CAAA;AAAA,OAEjC,MAAA,IAAA,CAAC,MAAU,IAAA,CAAC,SAAW,EAAA;AAC9B,QAAA,MAAA,CAAO,SAAS,KAAM,CAAA,GAAA;AACtB,QAAA,MAAM,eAAe,MAAO,CAAA,gBAAA,EAAoB,EAAA,YAAA,CAAa,YAAY,CAAC,CAAA;AAC1E,QAAA,MAAM,YAAe,GAAA,mBAAA,CAAoB,KAAM,CAAA,YAAY,CAAE,CAAA,WAAA;AAC7D,QAAA,MAAM,kBAAkB,mBAAoB,CAAA,KAAA,CAAM,GAAI,CAAA,CAAA,CAAA,KAAK,EAAE,WAAW,CAAA;AACxE,QAAA,MAAM,IAAO,GAAA,YAAA,CAAa,eAAiB,EAAA,MAAA,CAAO,OAAO,YAAY,CAAA;AAErE,QAAA,MAAM,YAAY,mBAAoB,CAAA,KAAA,CAAM,KAAK,CAAU,MAAA,KAAA,MAAA,CAAO,gBAAgB,IAAI,CAAA;AACtF,QAAI,IAAA,SAAA;AACF,UAAA,qBAAA,CAAsB,UAAU,KAAK,CAAA;AAAA;AAGzC,MAAA,QAAA,CAAS,MAAM;AACb,QAAA,IAAI,MAAM,QAAY,IAAA,MAAA;AACpB,UAAA,WAAA,CAAY,qBAAsB,CAAA,MAAA,EAAQ,gBAAiB,EAAA,EAAG,QAAU,EAAA,WAAA,CAAY,aAAc,CAAA,KAAA,CAAM,GAAI,CAAA,CAAA,CAAA,KAAK,CAAE,CAAA,KAAK,CAAC,CAAA;AAAA,OAC5H,CAAA;AAAA,KACF,CAAA;;;;;;;;;;;;;;;;;;;;;;"}