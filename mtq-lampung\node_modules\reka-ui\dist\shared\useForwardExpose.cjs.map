{"version": 3, "file": "useForwardExpose.cjs", "sources": ["../../src/shared/useForwardExpose.ts"], "sourcesContent": ["import type { ComponentPublicInstance } from 'vue'\n// reference: https://github.com/vuejs/rfcs/issues/258#issuecomment-1068697672\nimport { unrefElement } from '@vueuse/core'\nimport { computed, getCurrentInstance, ref } from 'vue'\n\nexport function useForwardExpose<T extends ComponentPublicInstance>() {\n  const instance = getCurrentInstance()!\n\n  const currentRef = ref<Element | T | null>()\n  const currentElement = computed<HTMLElement>(() => {\n    // $el could be text/comment for non-single root normal or text root, thus we retrieve the nextElementSibling\n    // @ts-expect-error ignore ts error\n    return ['#text', '#comment'].includes(currentRef.value?.$el.nodeName) ? currentRef.value?.$el.nextElementSibling : unrefElement(currentRef)\n  })\n\n  // Do give us credit if you reference our code :D\n  // localExpose should only be assigned once else will create infinite loop\n  const localExpose: Record<string, any> | null = Object.assign({}, instance.exposed)\n  const ret: Record<string, any> = {}\n\n  // retrieve props for current instance\n  for (const key in instance.props) {\n    Object.defineProperty(ret, key, {\n      enumerable: true,\n      configurable: true,\n      get: () => instance.props[key],\n    })\n  }\n\n  // retrieve default exposed value\n  if (Object.keys(localExpose).length > 0) {\n    for (const key in localExpose) {\n      Object.defineProperty(ret, key, {\n        enumerable: true,\n        configurable: true,\n        get: () => localExpose![key],\n      })\n    }\n  }\n\n  // retrieve original first root element\n  Object.defineProperty(ret, '$el', {\n    enumerable: true,\n    configurable: true,\n    get: () => instance.vnode.el,\n  })\n  instance.exposed = ret\n\n  function forwardRef(ref: Element | T | null) {\n    currentRef.value = ref\n\n    if (!ref)\n      return\n\n    // retrieve the forwarded element\n    Object.defineProperty(ret, '$el', {\n      enumerable: true,\n      configurable: true,\n      get: () => (ref instanceof Element ? ref : ref.$el),\n    })\n\n    instance.exposed = ret\n  }\n\n  return { forwardRef, currentRef, currentElement }\n}\n"], "names": ["getCurrentInstance", "ref", "computed", "unrefElement"], "mappings": ";;;;;AAKO,SAAS,gBAAsD,GAAA;AACpE,EAAA,MAAM,WAAWA,sBAAmB,EAAA;AAEpC,EAAA,MAAM,aAAaC,OAAwB,EAAA;AAC3C,EAAM,MAAA,cAAA,GAAiBC,aAAsB,MAAM;AAGjD,IAAA,OAAO,CAAC,OAAA,EAAS,UAAU,CAAA,CAAE,SAAS,UAAW,CAAA,KAAA,EAAO,GAAI,CAAA,QAAQ,IAAI,UAAW,CAAA,KAAA,EAAO,GAAI,CAAA,kBAAA,GAAqBC,kBAAa,UAAU,CAAA;AAAA,GAC3I,CAAA;AAID,EAAA,MAAM,cAA0C,MAAO,CAAA,MAAA,CAAO,EAAC,EAAG,SAAS,OAAO,CAAA;AAClF,EAAA,MAAM,MAA2B,EAAC;AAGlC,EAAW,KAAA,MAAA,GAAA,IAAO,SAAS,KAAO,EAAA;AAChC,IAAO,MAAA,CAAA,cAAA,CAAe,KAAK,GAAK,EAAA;AAAA,MAC9B,UAAY,EAAA,IAAA;AAAA,MACZ,YAAc,EAAA,IAAA;AAAA,MACd,GAAK,EAAA,MAAM,QAAS,CAAA,KAAA,CAAM,GAAG;AAAA,KAC9B,CAAA;AAAA;AAIH,EAAA,IAAI,MAAO,CAAA,IAAA,CAAK,WAAW,CAAA,CAAE,SAAS,CAAG,EAAA;AACvC,IAAA,KAAA,MAAW,OAAO,WAAa,EAAA;AAC7B,MAAO,MAAA,CAAA,cAAA,CAAe,KAAK,GAAK,EAAA;AAAA,QAC9B,UAAY,EAAA,IAAA;AAAA,QACZ,YAAc,EAAA,IAAA;AAAA,QACd,GAAA,EAAK,MAAM,WAAA,CAAa,GAAG;AAAA,OAC5B,CAAA;AAAA;AACH;AAIF,EAAO,MAAA,CAAA,cAAA,CAAe,KAAK,KAAO,EAAA;AAAA,IAChC,UAAY,EAAA,IAAA;AAAA,IACZ,YAAc,EAAA,IAAA;AAAA,IACd,GAAA,EAAK,MAAM,QAAA,CAAS,KAAM,CAAA;AAAA,GAC3B,CAAA;AACD,EAAA,QAAA,CAAS,OAAU,GAAA,GAAA;AAEnB,EAAA,SAAS,WAAWF,IAAyB,EAAA;AAC3C,IAAA,UAAA,CAAW,KAAQA,GAAAA,IAAAA;AAEnB,IAAA,IAAI,CAACA,IAAAA;AACH,MAAA;AAGF,IAAO,MAAA,CAAA,cAAA,CAAe,KAAK,KAAO,EAAA;AAAA,MAChC,UAAY,EAAA,IAAA;AAAA,MACZ,YAAc,EAAA,IAAA;AAAA,MACd,GAAK,EAAA,MAAOA,IAAe,YAAA,OAAA,GAAUA,OAAMA,IAAI,CAAA;AAAA,KAChD,CAAA;AAED,IAAA,QAAA,CAAS,OAAU,GAAA,GAAA;AAAA;AAGrB,EAAO,OAAA,EAAE,UAAY,EAAA,UAAA,EAAY,cAAe,EAAA;AAClD;;;;"}