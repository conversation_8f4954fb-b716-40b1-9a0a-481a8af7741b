{"version": 3, "file": "TooltipContent.cjs", "sources": ["../../src/Tooltip/TooltipContent.vue"], "sourcesContent": ["<script lang=\"ts\">\nimport type { TooltipContentImplEmits, TooltipContentImplProps } from './TooltipContentImpl.vue'\n\nexport type TooltipContentEmits = TooltipContentImplEmits\n\nexport interface TooltipContentProps extends TooltipContentImplProps {\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with Vue animation libraries.\n   */\n  forceMount?: boolean\n}\n</script>\n\n<script setup lang=\"ts\">\nimport { Presence } from '@/Presence'\nimport { useForwardExpose, useForwardPropsEmits } from '@/shared'\nimport TooltipContentHoverable from './TooltipContentHoverable.vue'\nimport TooltipContentImpl from './TooltipContentImpl.vue'\nimport { injectTooltipRootContext } from './TooltipRoot.vue'\n\nconst props = withDefaults(defineProps<TooltipContentProps>(), {\n  side: 'top',\n})\nconst emits = defineEmits<TooltipContentEmits>()\n\nconst rootContext = injectTooltipRootContext()\nconst forwarded = useForwardPropsEmits(props, emits)\nconst { forwardRef } = useForwardExpose()\n</script>\n\n<template>\n  <Presence :present=\"forceMount || rootContext.open.value\">\n    <component\n      :is=\"rootContext.disableHoverableContent.value ? TooltipContentImpl : TooltipContentHoverable\"\n      :ref=\"forwardRef\"\n      v-bind=\"forwarded\"\n    >\n      <slot />\n    </component>\n  </Presence>\n</template>\n"], "names": ["injectTooltipRootContext", "useForwardPropsEmits", "useForwardExpose"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqBA,IAAA,MAAM,KAAQ,GAAA,OAAA;AAGd,IAAA,MAAM,KAAQ,GAAA,MAAA;AAEd,IAAA,MAAM,cAAcA,4CAAyB,EAAA;AAC7C,IAAM,MAAA,SAAA,GAAYC,gDAAqB,CAAA,KAAA,EAAO,KAAK,CAAA;AACnD,IAAM,MAAA,EAAE,UAAW,EAAA,GAAIC,wCAAiB,EAAA;;;;;;;;;;;;;;;;;;;;;"}