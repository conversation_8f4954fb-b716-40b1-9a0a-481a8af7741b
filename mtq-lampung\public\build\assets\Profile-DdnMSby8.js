import{d as m,n as E,h as g,o as p,w as a,q as _,m as y,u as e,c as $,e as t,b as u,a as b,A as C,B as j,x as P,i as f,E as z,g as T,j as I,k as B,S as U,T as A,C as M,D as N}from"./app-BxByyVXe.js";import{a as S,_ as q}from"./Layout.vue_vue_type_script_setup_true_lang-BUvfOgPo.js";import{_ as h,a as k,b as D}from"./Label.vue_vue_type_script_setup_true_lang-Dc3XvMbX.js";import{P as R,a as v,_ as w}from"./AppLogoIcon.vue_vue_type_script_setup_true_lang-43VByX6w.js";import{a as H,d as F,e as V}from"./RovingFocusGroup-hazQWYmS.js";import{i as K,a as W,b as O,c as X,d as Y,e as G,X as J,f as L,g as Q,_ as Z}from"./AppLayout.vue_vue_type_script_setup_true_lang-va1QG6ga.js";import{a as ee}from"./useForwardExpose-67BWFZEI.js";const se=m({__name:"DialogTrigger",props:{asChild:{type:Boolean},as:{default:"button"}},setup(l){const n=l,s=K(),{forwardRef:d,currentElement:r}=ee();return s.contentId||(s.contentId=H(void 0,"reka-dialog-content")),E(()=>{s.triggerElement.value=r.value}),(c,o)=>(p(),g(e(R),y(n,{ref:e(d),type:c.as==="button"?"button":void 0,"aria-haspopup":"dialog","aria-expanded":e(s).open.value||!1,"aria-controls":e(s).open.value?e(s).contentId:void 0,"data-state":e(s).open.value?"open":"closed",onClick:e(s).onOpenToggle}),{default:a(()=>[_(c.$slots,"default")]),_:3},16,["type","aria-expanded","aria-controls","data-state","onClick"]))}}),te=m({__name:"Dialog",props:{open:{type:Boolean},defaultOpen:{type:Boolean},modal:{type:Boolean}},emits:["update:open"],setup(l,{emit:n}){const r=F(l,n);return(c,o)=>(p(),g(e(W),y({"data-slot":"dialog"},e(r)),{default:a(()=>[_(c.$slots,"default")]),_:3},16))}}),ae=m({__name:"DialogClose",props:{asChild:{type:Boolean},as:{type:[String,Object,Function]}},setup(l){const n=l;return(s,d)=>(p(),g(e(O),y({"data-slot":"dialog-close"},n),{default:a(()=>[_(s.$slots,"default")]),_:3},16))}}),oe=m({__name:"DialogOverlay",props:{forceMount:{type:Boolean},asChild:{type:Boolean},as:{type:[String,Object,Function]},class:{}},setup(l){const n=l,s=$(()=>{const{class:d,...r}=n;return r});return(d,r)=>(p(),g(e(X),y({"data-slot":"dialog-overlay"},s.value,{class:e(v)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/80",n.class)}),{default:a(()=>[_(d.$slots,"default")]),_:3},16,["class"]))}}),ne=m({__name:"DialogContent",props:{forceMount:{type:Boolean},trapFocus:{type:Boolean},disableOutsidePointerEvents:{type:Boolean},asChild:{type:Boolean},as:{type:[String,Object,Function]},class:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","openAutoFocus","closeAutoFocus"],setup(l,{emit:n}){const s=l,d=n,r=$(()=>{const{class:o,...i}=s;return i}),c=F(r,d);return(o,i)=>(p(),g(e(Y),null,{default:a(()=>[t(oe),t(e(G),y({"data-slot":"dialog-content"},e(c),{class:e(v)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",s.class)}),{default:a(()=>[_(o.$slots,"default"),t(e(O),{class:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4"},{default:a(()=>[t(e(J)),i[0]||(i[0]=u("span",{class:"sr-only"},"Close",-1))]),_:1,__:[0]})]),_:3},16,["class"])]),_:3}))}}),re=m({__name:"DialogDescription",props:{asChild:{type:Boolean},as:{type:[String,Object,Function]},class:{}},setup(l){const n=l,s=$(()=>{const{class:r,...c}=n;return c}),d=V(s);return(r,c)=>(p(),g(e(L),y({"data-slot":"dialog-description"},e(d),{class:e(v)("text-muted-foreground text-sm",n.class)}),{default:a(()=>[_(r.$slots,"default")]),_:3},16,["class"]))}}),le=m({__name:"DialogFooter",props:{class:{}},setup(l){const n=l;return(s,d)=>(p(),b("div",{"data-slot":"dialog-footer",class:C(e(v)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",n.class))},[_(s.$slots,"default")],2))}}),de=m({__name:"DialogHeader",props:{class:{}},setup(l){const n=l;return(s,d)=>(p(),b("div",{"data-slot":"dialog-header",class:C(e(v)("flex flex-col gap-2 text-center sm:text-left",n.class))},[_(s.$slots,"default")],2))}}),ie=m({__name:"DialogTitle",props:{asChild:{type:Boolean},as:{type:[String,Object,Function]},class:{}},setup(l){const n=l,s=$(()=>{const{class:r,...c}=n;return c}),d=V(s);return(r,c)=>(p(),g(e(Q),y({"data-slot":"dialog-title"},e(d),{class:e(v)("text-lg leading-none font-semibold",n.class)}),{default:a(()=>[_(r.$slots,"default")]),_:3},16,["class"]))}}),ce=m({__name:"DialogTrigger",props:{asChild:{type:Boolean},as:{type:[String,Object,Function]}},setup(l){const n=l;return(s,d)=>(p(),g(e(se),y({"data-slot":"dialog-trigger"},n),{default:a(()=>[_(s.$slots,"default")]),_:3},16))}}),ue={class:"space-y-6"},pe={class:"space-y-4 rounded-lg border border-red-100 bg-red-50 p-4 dark:border-red-200/10 dark:bg-red-700/10"},me={class:"grid gap-2"},fe=m({__name:"DeleteUser",setup(l){const n=j(null),s=P({password:""}),d=c=>{c.preventDefault(),s.delete(route("profile.destroy"),{preserveScroll:!0,onSuccess:()=>r(),onError:()=>{var o;return(o=n.value)==null?void 0:o.focus()},onFinish:()=>s.reset()})},r=()=>{s.clearErrors(),s.reset()};return(c,o)=>(p(),b("div",ue,[t(S,{title:"Delete account",description:"Delete your account and all of its resources"}),u("div",pe,[o[7]||(o[7]=u("div",{class:"relative space-y-0.5 text-red-600 dark:text-red-100"},[u("p",{class:"font-medium"},"Warning"),u("p",{class:"text-sm"},"Please proceed with caution, this cannot be undone.")],-1)),t(e(te),null,{default:a(()=>[t(e(ce),{"as-child":""},{default:a(()=>[t(e(w),{variant:"destructive"},{default:a(()=>o[1]||(o[1]=[f("Delete account")])),_:1,__:[1]})]),_:1}),t(e(ne),null,{default:a(()=>[u("form",{class:"space-y-6",onSubmit:d},[t(e(de),{class:"space-y-3"},{default:a(()=>[t(e(ie),null,{default:a(()=>o[2]||(o[2]=[f("Are you sure you want to delete your account?")])),_:1,__:[2]}),t(e(re),null,{default:a(()=>o[3]||(o[3]=[f(" Once your account is deleted, all of its resources and data will also be permanently deleted. Please enter your password to confirm you would like to permanently delete your account. ")])),_:1,__:[3]})]),_:1}),u("div",me,[t(e(h),{for:"password",class:"sr-only"},{default:a(()=>o[4]||(o[4]=[f("Password")])),_:1,__:[4]}),t(e(k),{id:"password",type:"password",name:"password",ref_key:"passwordInput",ref:n,modelValue:e(s).password,"onUpdate:modelValue":o[0]||(o[0]=i=>e(s).password=i),placeholder:"Password"},null,8,["modelValue"]),t(D,{message:e(s).errors.password},null,8,["message"])]),t(e(le),{class:"gap-2"},{default:a(()=>[t(e(ae),{"as-child":""},{default:a(()=>[t(e(w),{variant:"secondary",onClick:r},{default:a(()=>o[5]||(o[5]=[f(" Cancel ")])),_:1,__:[5]})]),_:1}),t(e(w),{type:"submit",variant:"destructive",disabled:e(s).processing},{default:a(()=>o[6]||(o[6]=[f(" Delete account ")])),_:1,__:[6]},8,["disabled"])]),_:1})],32)]),_:1})]),_:1})])]))}}),_e={class:"flex flex-col space-y-6"},ge={class:"grid gap-2"},ye={class:"grid gap-2"},ve={key:0},be={class:"-mt-4 text-sm text-muted-foreground"},we={key:0,class:"mt-2 text-sm font-medium text-green-600"},$e={class:"flex items-center gap-4"},xe={class:"text-sm text-neutral-600"},Fe=m({__name:"Profile",props:{mustVerifyEmail:{type:Boolean},status:{}},setup(l){const n=[{title:"Profile settings",href:"/settings/profile"}],d=z().props.auth.user,r=P({name:d.name,email:d.email}),c=()=>{r.patch(route("profile.update"),{preserveScroll:!0})};return(o,i)=>(p(),g(Z,{breadcrumbs:n},{default:a(()=>[t(e(T),{title:"Profile settings"}),t(q,null,{default:a(()=>[u("div",_e,[t(S,{title:"Profile information",description:"Update your name and email address"}),u("form",{onSubmit:I(c,["prevent"]),class:"space-y-6"},[u("div",ge,[t(e(h),{for:"name"},{default:a(()=>i[2]||(i[2]=[f("Name")])),_:1,__:[2]}),t(e(k),{id:"name",class:"mt-1 block w-full",modelValue:e(r).name,"onUpdate:modelValue":i[0]||(i[0]=x=>e(r).name=x),required:"",autocomplete:"name",placeholder:"Full name"},null,8,["modelValue"]),t(D,{class:"mt-2",message:e(r).errors.name},null,8,["message"])]),u("div",ye,[t(e(h),{for:"email"},{default:a(()=>i[3]||(i[3]=[f("Email address")])),_:1,__:[3]}),t(e(k),{id:"email",type:"email",class:"mt-1 block w-full",modelValue:e(r).email,"onUpdate:modelValue":i[1]||(i[1]=x=>e(r).email=x),required:"",autocomplete:"username",placeholder:"Email address"},null,8,["modelValue"]),t(D,{class:"mt-2",message:e(r).errors.email},null,8,["message"])]),o.mustVerifyEmail&&!e(d).email_verified_at?(p(),b("div",ve,[u("p",be,[i[5]||(i[5]=f(" Your email address is unverified. ")),t(e(U),{href:o.route("verification.send"),method:"post",as:"button",class:"text-foreground underline decoration-neutral-300 underline-offset-4 transition-colors duration-300 ease-out hover:decoration-current! dark:decoration-neutral-500"},{default:a(()=>i[4]||(i[4]=[f(" Click here to resend the verification email. ")])),_:1,__:[4]},8,["href"])]),o.status==="verification-link-sent"?(p(),b("div",we," A new verification link has been sent to your email address. ")):B("",!0)])):B("",!0),u("div",$e,[t(e(w),{disabled:e(r).processing},{default:a(()=>i[6]||(i[6]=[f("Save")])),_:1,__:[6]},8,["disabled"]),t(A,{"enter-active-class":"transition ease-in-out","enter-from-class":"opacity-0","leave-active-class":"transition ease-in-out","leave-to-class":"opacity-0"},{default:a(()=>[M(u("p",xe,"Saved.",512),[[N,e(r).recentlySuccessful]])]),_:1})])],32)]),t(fe)]),_:1})]),_:1}))}});export{Fe as default};
