{"version": 3, "file": "ToastDescription.cjs", "sources": ["../../src/Toast/ToastDescription.vue"], "sourcesContent": ["<script lang=\"ts\">\nimport type { PrimitiveProps } from '@/Primitive'\nimport { useForwardExpose } from '@/shared'\n\nexport interface ToastDescriptionProps extends PrimitiveProps {}\n</script>\n\n<script setup lang=\"ts\">\nimport { Primitive } from '@/Primitive'\n\nconst props = defineProps<ToastDescriptionProps>()\nuseForwardExpose()\n</script>\n\n<template>\n  <Primitive v-bind=\"props\">\n    <slot />\n  </Primitive>\n</template>\n"], "names": ["useForwardExpose"], "mappings": ";;;;;;;;;;;;;AAUA,IAAA,MAAM,KAAQ,GAAA,OAAA;AACd,IAAiBA,wCAAA,EAAA;;;;;;;;;;;;;;"}