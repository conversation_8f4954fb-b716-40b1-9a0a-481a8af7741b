{"version": 3, "file": "TooltipRoot.js", "sources": ["../../src/Tooltip/TooltipRoot.vue"], "sourcesContent": ["<script lang=\"ts\">\nimport type { Ref } from 'vue'\nimport { createContext, useForwardExpose } from '@/shared'\n\nexport interface TooltipRootProps {\n  /**\n   * The open state of the tooltip when it is initially rendered.\n   * Use when you do not need to control its open state.\n   */\n  defaultOpen?: boolean\n  /**\n   * The controlled open state of the tooltip.\n   */\n  open?: boolean\n  /**\n   * Override the duration given to the `Provider` to customise\n   * the open delay for a specific tooltip.\n   *\n   * @defaultValue 700\n   */\n  delayDuration?: number\n  /**\n   * Prevents Tooltip.Content from remaining open when hovering.\n   * Disabling this has accessibility consequences. Inherits\n   * from Tooltip.Provider.\n   */\n  disableHoverableContent?: boolean\n  /**\n   * When `true`, clicking on trigger will not close the content.\n   * @defaultValue false\n   */\n  disableClosingTrigger?: boolean\n  /**\n   * When `true`, disable tooltip\n   * @defaultValue false\n   */\n  disabled?: boolean\n  /**\n   * Prevent the tooltip from opening if the focus did not come from\n   * the keyboard by matching against the `:focus-visible` selector.\n   * This is useful if you want to avoid opening it when switching\n   * browser tabs or closing a dialog.\n   * @defaultValue false\n   */\n  ignoreNonKeyboardFocus?: boolean\n}\n\nexport type TooltipRootEmits = {\n  /** Event handler called when the open state of the tooltip changes. */\n  'update:open': [value: boolean]\n}\n\nexport interface TooltipContext {\n  contentId: string\n  open: Ref<boolean>\n  stateAttribute: Ref<'closed' | 'delayed-open' | 'instant-open'>\n  trigger: Ref<HTMLElement | undefined>\n  onTriggerChange: (trigger: HTMLElement | undefined) => void\n  onTriggerEnter: () => void\n  onTriggerLeave: () => void\n  onOpen: () => void\n  onClose: () => void\n  disableHoverableContent: Ref<boolean>\n  disableClosingTrigger: Ref<boolean>\n  disabled: Ref<boolean>\n  ignoreNonKeyboardFocus: Ref<boolean>\n}\n\nexport const [injectTooltipRootContext, provideTooltipRootContext]\n  = createContext<TooltipContext>('TooltipRoot')\n</script>\n\n<script setup lang=\"ts\">\nimport { useTimeoutFn, useVModel } from '@vueuse/core'\nimport { computed, ref, watch } from 'vue'\nimport { PopperRoot } from '@/Popper'\nimport { injectTooltipProviderContext } from './TooltipProvider.vue'\nimport { TOOLTIP_OPEN } from './utils'\n\nconst props = withDefaults(defineProps<TooltipRootProps>(), {\n  defaultOpen: false,\n  open: undefined,\n  delayDuration: undefined,\n  disableHoverableContent: undefined,\n  disableClosingTrigger: undefined,\n  disabled: undefined,\n  ignoreNonKeyboardFocus: undefined,\n})\n\nconst emit = defineEmits<TooltipRootEmits>()\n\ndefineSlots<{\n  default?: (props: {\n    /** Current open state */\n    open: typeof open.value\n  }) => any\n}>()\n\nuseForwardExpose()\nconst providerContext = injectTooltipProviderContext()\n\nconst disableHoverableContent = computed(() => props.disableHoverableContent ?? providerContext.disableHoverableContent.value)\nconst disableClosingTrigger = computed(() => props.disableClosingTrigger ?? providerContext.disableClosingTrigger.value)\nconst disableTooltip = computed(() => props.disabled ?? providerContext.disabled.value)\n\nconst delayDuration = computed(() => props.delayDuration ?? providerContext.delayDuration.value)\nconst ignoreNonKeyboardFocus = computed(() => props.ignoreNonKeyboardFocus ?? providerContext.ignoreNonKeyboardFocus.value)\n\nconst open = useVModel(props, 'open', emit, {\n  defaultValue: props.defaultOpen,\n  passive: (props.open === undefined) as false,\n}) as Ref<boolean>\n\nwatch(open, (isOpen) => {\n  if (!providerContext.onClose)\n    return\n  if (isOpen) {\n    providerContext.onOpen()\n    // as `onChange` is called within a lifecycle method we\n    // avoid dispatching via `dispatchDiscreteCustomEvent`.\n    document.dispatchEvent(new CustomEvent(TOOLTIP_OPEN))\n  }\n  else {\n    providerContext.onClose()\n  }\n})\n\nconst wasOpenDelayedRef = ref(false)\nconst trigger = ref<HTMLElement>()\n\nconst stateAttribute = computed(() => {\n  if (!open.value)\n    return 'closed'\n  return wasOpenDelayedRef.value ? 'delayed-open' : 'instant-open'\n})\n\nconst { start: startTimer, stop: clearTimer } = useTimeoutFn(() => {\n  wasOpenDelayedRef.value = true\n  open.value = true\n}, delayDuration, { immediate: false })\n\nfunction handleOpen() {\n  clearTimer()\n  wasOpenDelayedRef.value = false\n  open.value = true\n}\nfunction handleClose() {\n  clearTimer()\n  open.value = false\n}\nfunction handleDelayedOpen() {\n  startTimer()\n}\n\nprovideTooltipRootContext({\n  contentId: '',\n  open,\n  stateAttribute,\n  trigger,\n  onTriggerChange(el) {\n    trigger.value = el\n  },\n  onTriggerEnter() {\n    if (providerContext.isOpenDelayed.value)\n      handleDelayedOpen()\n    else handleOpen()\n  },\n  onTriggerLeave() {\n    if (disableHoverableContent.value) {\n      handleClose()\n    }\n    else {\n      // Clear the timer in case the pointer leaves the trigger before the tooltip is opened.\n      clearTimer()\n    }\n  },\n  onOpen: handleOpen,\n  onClose: handleClose,\n  disableHoverableContent,\n  disableClosingTrigger,\n  disabled: disableTooltip,\n  ignoreNonKeyboardFocus,\n})\n</script>\n\n<template>\n  <PopperRoot>\n    <slot :open=\"open\" />\n  </PopperRoot>\n</template>\n"], "names": [], "mappings": ";;;;;;;;AAoEO,MAAM,CAAC,wBAAA,EAA0B,yBAAyB,CAAA,GAC7D,cAA8B,aAAa;;;;;;;;;;;;;;AAU/C,IAAA,MAAM,KAAQ,GAAA,OAAA;AAUd,IAAA,MAAM,IAAO,GAAA,MAAA;AASb,IAAiB,gBAAA,EAAA;AACjB,IAAA,MAAM,kBAAkB,4BAA6B,EAAA;AAErD,IAAA,MAAM,0BAA0B,QAAS,CAAA,MAAM,MAAM,uBAA2B,IAAA,eAAA,CAAgB,wBAAwB,KAAK,CAAA;AAC7H,IAAA,MAAM,wBAAwB,QAAS,CAAA,MAAM,MAAM,qBAAyB,IAAA,eAAA,CAAgB,sBAAsB,KAAK,CAAA;AACvH,IAAA,MAAM,iBAAiB,QAAS,CAAA,MAAM,MAAM,QAAY,IAAA,eAAA,CAAgB,SAAS,KAAK,CAAA;AAEtF,IAAA,MAAM,gBAAgB,QAAS,CAAA,MAAM,MAAM,aAAiB,IAAA,eAAA,CAAgB,cAAc,KAAK,CAAA;AAC/F,IAAA,MAAM,yBAAyB,QAAS,CAAA,MAAM,MAAM,sBAA0B,IAAA,eAAA,CAAgB,uBAAuB,KAAK,CAAA;AAE1H,IAAA,MAAM,IAAO,GAAA,SAAA,CAAU,KAAO,EAAA,MAAA,EAAQ,IAAM,EAAA;AAAA,MAC1C,cAAc,KAAM,CAAA,WAAA;AAAA,MACpB,OAAA,EAAU,MAAM,IAAS,KAAA;AAAA,KAC1B,CAAA;AAED,IAAM,KAAA,CAAA,IAAA,EAAM,CAAC,MAAW,KAAA;AACtB,MAAA,IAAI,CAAC,eAAgB,CAAA,OAAA;AACnB,QAAA;AACF,MAAA,IAAI,MAAQ,EAAA;AACV,QAAA,eAAA,CAAgB,MAAO,EAAA;AAGvB,QAAA,QAAA,CAAS,aAAc,CAAA,IAAI,WAAY,CAAA,YAAY,CAAC,CAAA;AAAA,OAEjD,MAAA;AACH,QAAA,eAAA,CAAgB,OAAQ,EAAA;AAAA;AAC1B,KACD,CAAA;AAED,IAAM,MAAA,iBAAA,GAAoB,IAAI,KAAK,CAAA;AACnC,IAAA,MAAM,UAAU,GAAiB,EAAA;AAEjC,IAAM,MAAA,cAAA,GAAiB,SAAS,MAAM;AACpC,MAAA,IAAI,CAAC,IAAK,CAAA,KAAA;AACR,QAAO,OAAA,QAAA;AACT,MAAO,OAAA,iBAAA,CAAkB,QAAQ,cAAiB,GAAA,cAAA;AAAA,KACnD,CAAA;AAED,IAAA,MAAM,EAAE,KAAO,EAAA,UAAA,EAAY,MAAM,UAAW,EAAA,GAAI,aAAa,MAAM;AACjE,MAAA,iBAAA,CAAkB,KAAQ,GAAA,IAAA;AAC1B,MAAA,IAAA,CAAK,KAAQ,GAAA,IAAA;AAAA,KACZ,EAAA,aAAA,EAAe,EAAE,SAAA,EAAW,OAAO,CAAA;AAEtC,IAAA,SAAS,UAAa,GAAA;AACpB,MAAW,UAAA,EAAA;AACX,MAAA,iBAAA,CAAkB,KAAQ,GAAA,KAAA;AAC1B,MAAA,IAAA,CAAK,KAAQ,GAAA,IAAA;AAAA;AAEf,IAAA,SAAS,WAAc,GAAA;AACrB,MAAW,UAAA,EAAA;AACX,MAAA,IAAA,CAAK,KAAQ,GAAA,KAAA;AAAA;AAEf,IAAA,SAAS,iBAAoB,GAAA;AAC3B,MAAW,UAAA,EAAA;AAAA;AAGb,IAA0B,yBAAA,CAAA;AAAA,MACxB,SAAW,EAAA,EAAA;AAAA,MACX,IAAA;AAAA,MACA,cAAA;AAAA,MACA,OAAA;AAAA,MACA,gBAAgB,EAAI,EAAA;AAClB,QAAA,OAAA,CAAQ,KAAQ,GAAA,EAAA;AAAA,OAClB;AAAA,MACA,cAAiB,GAAA;AACf,QAAA,IAAI,gBAAgB,aAAc,CAAA,KAAA;AAChC,UAAkB,iBAAA,EAAA;AAAA,aACJ,UAAA,EAAA;AAAA,OAClB;AAAA,MACA,cAAiB,GAAA;AACf,QAAA,IAAI,wBAAwB,KAAO,EAAA;AACjC,UAAY,WAAA,EAAA;AAAA,SAET,MAAA;AAEH,UAAW,UAAA,EAAA;AAAA;AACb,OACF;AAAA,MACA,MAAQ,EAAA,UAAA;AAAA,MACR,OAAS,EAAA,WAAA;AAAA,MACT,uBAAA;AAAA,MACA,qBAAA;AAAA,MACA,QAAU,EAAA,cAAA;AAAA,MACV;AAAA,KACD,CAAA;;;;;;;;;;;;;;"}