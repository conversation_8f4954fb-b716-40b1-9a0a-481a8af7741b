{"version": 3, "file": "useLocale.js", "sources": ["../../src/shared/useLocale.ts"], "sourcesContent": ["import type { Ref } from 'vue'\nimport { computed, ref } from 'vue'\nimport { injectConfigProviderContext } from '@/ConfigProvider/ConfigProvider.vue'\n\nexport function useLocale(locale?: Ref<string | undefined>) {\n  const context = injectConfigProviderContext({\n    locale: ref('en'),\n  })\n  return computed(() => locale?.value || context.locale?.value || 'en')\n}\n"], "names": [], "mappings": ";;;AAIO,SAAS,UAAU,MAAkC,EAAA;AAC1D,EAAA,MAAM,UAAU,2BAA4B,CAAA;AAAA,IAC1C,MAAA,EAAQ,IAAI,IAAI;AAAA,GACjB,CAAA;AACD,EAAA,OAAO,SAAS,MAAM,MAAA,EAAQ,SAAS,OAAQ,CAAA,MAAA,EAAQ,SAAS,IAAI,CAAA;AACtE;;;;"}