{"version": 3, "file": "Viewport.js", "sources": ["../../src/Viewport/Viewport.vue"], "sourcesContent": ["<script lang=\"ts\">\nimport type { PrimitiveProps } from '@/Primitive'\nimport { toRefs } from 'vue'\nimport { useForwardExpose } from '@/shared'\nimport { useNonce } from '@/shared/useNonce'\n\nexport interface ViewportProps extends PrimitiveProps {\n  /**\n   * Will add `nonce` attribute to the style tag which can be used by Content Security Policy. <br> If omitted, inherits globally from `ConfigProvider`.\n   */\n  nonce?: string\n}\n</script>\n\n<script setup lang=\"ts\">\nimport { Primitive } from '@/Primitive'\n\nconst props = defineProps<ViewportProps>()\nconst { forwardRef } = useForwardExpose()\n\nconst { nonce: propNonce } = toRefs(props)\nconst nonce = useNonce(propNonce)\n</script>\n\n<template>\n  <Primitive\n    v-bind=\"{ ...$attrs, ...props }\"\n    :ref=\"forwardRef\"\n    data-reka-viewport\n    role=\"presentation\"\n    :style=\"{\n      // we use position: 'relative' here on the `viewport` so that when we call\n      // `selectedItem.offsetTop` in calculations, the offset is relative to the viewport\n      // (independent of the scrollUpButton).\n      position: 'relative',\n      flex: 1,\n      overflow: 'auto',\n    }\"\n  >\n    <slot />\n  </Primitive>\n  <Primitive\n    as=\"style\"\n    :nonce=\"nonce\"\n  >\n    /* Hide scrollbars cross-browser and enable momentum scroll for touch\n    devices */ [data-reka-viewport] { scrollbar-width:none; -ms-overflow-style: none;\n    -webkit-overflow-scrolling: touch; }\n    [data-reka-viewport]::-webkit-scrollbar { display: none; }\n  </Primitive>\n</template>\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAiBA,IAAA,MAAM,KAAQ,GAAA,OAAA;AACd,IAAM,MAAA,EAAE,UAAW,EAAA,GAAI,gBAAiB,EAAA;AAExC,IAAA,MAAM,EAAE,KAAA,EAAO,SAAU,EAAA,GAAI,OAAO,KAAK,CAAA;AACzC,IAAM,MAAA,KAAA,GAAQ,SAAS,SAAS,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}