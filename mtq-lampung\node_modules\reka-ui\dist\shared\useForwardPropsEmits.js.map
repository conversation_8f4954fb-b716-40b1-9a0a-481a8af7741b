{"version": 3, "file": "useForwardPropsEmits.js", "sources": ["../../src/shared/useForwardPropsEmits.ts"], "sourcesContent": ["import type { MaybeRefOrGetter } from 'vue'\nimport { computed } from 'vue'\nimport { useEmitAsProps } from './useEmitAsProps'\nimport { useForwardProps } from './useForwardProps'\n\n/**\n * The function `useForwardPropsEmits` takes in props and an optional emit function, and returns a\n * computed object that combines the parsed props and emits as props.\n * @param {T} props - The `props` parameter is of type `T`, which is a generic type that extends the\n * parameters of the `useForwardProps` function. It represents the props object that is passed to the\n * `useForwardProps` function.\n * @param [emit] - The `emit` parameter is a function that can be used to emit events. It takes two\n * arguments: `name`, which is the name of the event to be emitted, and `args`, which are the arguments\n * to be passed along with the event.\n * @returns a computed property that combines the parsed\n * props and emits as props.\n */\nexport function useForwardPropsEmits<T extends Record<string, any>, Name extends string>(props: MaybeRefOrGetter<T>, emit?: (name: Name, ...args: any[]) => void) {\n  const parsedProps = useForwardProps(props)\n  const emitsAsProps = emit ? useEmitAsProps(emit) : {}\n\n  return computed(() => ({\n    ...parsedProps.value,\n    ...emitsAsProps,\n  }))\n}\n"], "names": [], "mappings": ";;;;AAiBgB,SAAA,oBAAA,CAAyE,OAA4B,IAA6C,EAAA;AAChK,EAAM,MAAA,WAAA,GAAc,gBAAgB,KAAK,CAAA;AACzC,EAAA,MAAM,YAAe,GAAA,IAAA,GAAO,cAAe,CAAA,IAAI,IAAI,EAAC;AAEpD,EAAA,OAAO,SAAS,OAAO;AAAA,IACrB,GAAG,WAAY,CAAA,KAAA;AAAA,IACf,GAAG;AAAA,GACH,CAAA,CAAA;AACJ;;;;"}