{"version": 3, "file": "useDirection.js", "sources": ["../../src/shared/useDirection.ts"], "sourcesContent": ["import type { Ref } from 'vue'\nimport type { Direction } from './types'\nimport { computed, ref } from 'vue'\nimport { injectConfigProviderContext } from '@/ConfigProvider/ConfigProvider.vue'\n\nexport function useDirection(dir?: Ref<Direction | undefined>) {\n  const context = injectConfigProviderContext({\n    dir: ref('ltr'),\n  })\n  return computed(() => dir?.value || context.dir?.value || 'ltr')\n}\n"], "names": [], "mappings": ";;;AAKO,SAAS,aAAa,GAAkC,EAAA;AAC7D,EAAA,MAAM,UAAU,2BAA4B,CAAA;AAAA,IAC1C,GAAA,EAAK,IAAI,KAAK;AAAA,GACf,CAAA;AACD,EAAA,OAAO,SAAS,MAAM,GAAA,EAAK,SAAS,OAAQ,CAAA,GAAA,EAAK,SAAS,KAAK,CAAA;AACjE;;;;"}