{"version": 3, "file": "TooltipContentImpl.cjs", "sources": ["../../src/Tooltip/TooltipContentImpl.vue"], "sourcesContent": ["<script lang=\"ts\">\nimport type { VNode } from 'vue'\nimport type { PopperContentProps } from '@/Popper'\nimport type { PrimitiveProps } from '@/Primitive'\nimport { useForwardExpose } from '@/shared'\n\nexport type TooltipContentImplEmits = {\n  /** Event handler called when focus moves to the destructive action after opening. It can be prevented by calling `event.preventDefault` */\n  escapeKeyDown: [event: KeyboardEvent]\n  /** Event handler called when a pointer event occurs outside the bounds of the component. It can be prevented by calling `event.preventDefault`. */\n  pointerDownOutside: [event: Event]\n}\n\nexport interface TooltipContentImplProps\n  extends PrimitiveProps,\n  Pick<\n    PopperContentProps,\n    | 'side'\n    | 'sideOffset'\n    | 'align'\n    | 'alignOffset'\n    | 'avoidCollisions'\n    | 'collisionBoundary'\n    | 'collisionPadding'\n    | 'arrowPadding'\n    | 'sticky'\n    | 'hideWhenDetached'\n    | 'positionStrategy'\n    | 'updatePositionStrategy'\n  > {\n  /**\n   * By default, screenreaders will announce the content inside\n   * the component. If this is not descriptive enough, or you have\n   * content that cannot be announced, use aria-label as a more\n   * descriptive label.\n   *\n   * @defaultValue String\n   */\n  ariaLabel?: string\n}\n</script>\n\n<script setup lang=\"ts\">\nimport { useEventListener } from '@vueuse/core'\nimport { Comment, computed, onMounted, useSlots } from 'vue'\nimport { DismissableLayer } from '@/DismissableLayer'\nimport { PopperContent } from '@/Popper'\nimport { VisuallyHidden } from '@/VisuallyHidden'\nimport { injectTooltipRootContext } from './TooltipRoot.vue'\nimport { TOOLTIP_OPEN } from './utils'\n\nconst props = withDefaults(defineProps<TooltipContentImplProps>(), {\n  side: 'top',\n  sideOffset: 0,\n  align: 'center',\n  avoidCollisions: true,\n  collisionBoundary: () => [],\n  collisionPadding: 0,\n  arrowPadding: 0,\n  sticky: 'partial',\n  hideWhenDetached: false,\n})\nconst emits = defineEmits<TooltipContentImplEmits>()\n\nconst rootContext = injectTooltipRootContext()\n\nconst { forwardRef } = useForwardExpose()\nconst slot = useSlots()\nconst defaultSlot = computed(() => slot.default?.({}))\nconst ariaLabel = computed(() => {\n  if (props.ariaLabel)\n    return props.ariaLabel\n  let content = ''\n\n  function recursiveTextSearch(node: VNode) {\n    if (typeof node.children === 'string' && node.type !== Comment)\n      content += node.children\n    else if (Array.isArray(node.children))\n      node.children.forEach(child => recursiveTextSearch(child as VNode))\n  }\n\n  defaultSlot.value?.forEach((node: VNode) => recursiveTextSearch(node))\n  return content\n})\n\nconst popperContentProps = computed(() => {\n  const { ariaLabel: _, ...restProps } = props\n  return restProps\n})\n\nonMounted(() => {\n  // Close the tooltip if the trigger is scrolled\n  useEventListener(window, 'scroll', (event) => {\n    const target = event.target as HTMLElement\n    if (target?.contains(rootContext.trigger.value!))\n      rootContext.onClose()\n  })\n  // Close this tooltip if another one opens\n  useEventListener(window, TOOLTIP_OPEN, rootContext.onClose)\n})\n</script>\n\n<template>\n  <DismissableLayer\n    as-child\n    :disable-outside-pointer-events=\"false\"\n    @escape-key-down=\"emits('escapeKeyDown', $event)\"\n    @pointer-down-outside=\"(event) => {\n      if (rootContext.disableClosingTrigger.value && rootContext.trigger.value?.contains(event.target as HTMLElement))\n        event.preventDefault()\n\n      emits('pointerDownOutside', event)\n    }\"\n    @focus-outside.prevent\n    @dismiss=\"rootContext.onClose()\"\n  >\n    <PopperContent\n      :ref=\"forwardRef\"\n      :data-state=\"rootContext.stateAttribute.value\"\n      v-bind=\"{ ...$attrs, ...popperContentProps }\"\n      :style=\"{\n        '--reka-tooltip-content-transform-origin': 'var(--reka-popper-transform-origin)',\n        '--reka-tooltip-content-available-width': 'var(--reka-popper-available-width)',\n        '--reka-tooltip-content-available-height': 'var(--reka-popper-available-height)',\n        '--reka-tooltip-trigger-width': 'var(--reka-popper-anchor-width)',\n        '--reka-tooltip-trigger-height': 'var(--reka-popper-anchor-height)',\n      }\"\n    >\n      <slot />\n      <VisuallyHidden\n        :id=\"rootContext.contentId\"\n        role=\"tooltip\"\n      >\n        {{ ariaLabel }}\n      </VisuallyHidden>\n    </PopperContent>\n  </DismissableLayer>\n</template>\n"], "names": ["injectTooltipRootContext", "useForwardExpose", "useSlots", "computed", "Comment", "onMounted", "useEventListener", "TOOLTIP_OPEN"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmDA,IAAA,MAAM,KAAQ,GAAA,OAAA;AAWd,IAAA,MAAM,KAAQ,GAAA,MAAA;AAEd,IAAA,MAAM,cAAcA,4CAAyB,EAAA;AAE7C,IAAM,MAAA,EAAE,UAAW,EAAA,GAAIC,wCAAiB,EAAA;AACxC,IAAA,MAAM,OAAOC,YAAS,EAAA;AACtB,IAAA,MAAM,cAAcC,YAAS,CAAA,MAAM,KAAK,OAAU,GAAA,EAAE,CAAC,CAAA;AACrD,IAAM,MAAA,SAAA,GAAYA,aAAS,MAAM;AAC/B,MAAA,IAAI,KAAM,CAAA,SAAA;AACR,QAAA,OAAO,KAAM,CAAA,SAAA;AACf,MAAA,IAAI,OAAU,GAAA,EAAA;AAEd,MAAA,SAAS,oBAAoB,IAAa,EAAA;AACxC,QAAA,IAAI,OAAO,IAAA,CAAK,QAAa,KAAA,QAAA,IAAY,KAAK,IAAS,KAAAC,WAAA;AACrD,UAAA,OAAA,IAAW,IAAK,CAAA,QAAA;AAAA,aACT,IAAA,KAAA,CAAM,OAAQ,CAAA,IAAA,CAAK,QAAQ,CAAA;AAClC,UAAA,IAAA,CAAK,QAAS,CAAA,OAAA,CAAQ,CAAS,KAAA,KAAA,mBAAA,CAAoB,KAAc,CAAC,CAAA;AAAA;AAGtE,MAAA,WAAA,CAAY,OAAO,OAAQ,CAAA,CAAC,IAAgB,KAAA,mBAAA,CAAoB,IAAI,CAAC,CAAA;AACrE,MAAO,OAAA,OAAA;AAAA,KACR,CAAA;AAED,IAAM,MAAA,kBAAA,GAAqBD,aAAS,MAAM;AACxC,MAAA,MAAM,EAAE,SAAA,EAAW,CAAG,EAAA,GAAG,WAAc,GAAA,KAAA;AACvC,MAAO,OAAA,SAAA;AAAA,KACR,CAAA;AAED,IAAAE,aAAA,CAAU,MAAM;AAEd,MAAiBC,qBAAA,CAAA,MAAA,EAAQ,QAAU,EAAA,CAAC,KAAU,KAAA;AAC5C,QAAA,MAAM,SAAS,KAAM,CAAA,MAAA;AACrB,QAAA,IAAI,MAAQ,EAAA,QAAA,CAAS,WAAY,CAAA,OAAA,CAAQ,KAAM,CAAA;AAC7C,UAAA,WAAA,CAAY,OAAQ,EAAA;AAAA,OACvB,CAAA;AAED,MAAiBA,qBAAA,CAAA,MAAA,EAAQC,0BAAc,EAAA,WAAA,CAAY,OAAO,CAAA;AAAA,KAC3D,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}