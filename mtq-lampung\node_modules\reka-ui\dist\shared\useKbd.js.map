{"version": 3, "file": "useKbd.js", "sources": ["../../src/shared/useKbd.ts"], "sourcesContent": ["export function useKbd() {\n  return {\n    ALT: 'Alt',\n    ARROW_DOWN: 'ArrowDown',\n    ARROW_LEFT: 'ArrowLeft',\n    ARROW_RIGHT: 'ArrowRight',\n    ARROW_UP: 'ArrowUp',\n    BACKSPACE: 'Backspace',\n    CAPS_LOCK: 'CapsLock',\n    CONTROL: 'Control',\n    DELETE: 'Delete',\n    END: 'End',\n    ENTER: 'Enter',\n    ESCAPE: 'Escape',\n    F1: 'F1',\n    F10: 'F10',\n    F11: 'F11',\n    F12: 'F12',\n    F2: 'F2',\n    F3: 'F3',\n    F4: 'F4',\n    F5: 'F5',\n    F6: 'F6',\n    F7: 'F7',\n    F8: 'F8',\n    F9: 'F9',\n    HOME: 'Home',\n    META: 'Meta',\n    PAGE_DOWN: 'PageDown',\n    PAGE_UP: 'PageUp',\n    SHIFT: 'Shift',\n    SPACE: ' ',\n    TAB: 'Tab',\n    CTRL: 'Control',\n    ASTERISK: '*',\n    SPACE_CODE: 'Space',\n  }\n}\n\n/**\n * A wrapper around the internal kbd object to make it easier to use in tests\n * which require the key names to be wrapped in curly braces.\n */\nexport type KbdKeys = keyof ReturnType<typeof useKbd>\n\nexport function useTestKbd() {\n  const kbd = useKbd()\n\n  const initTestKbd: Record<KbdKeys, string> = Object.entries(kbd).reduce((acc, [key, value]) => {\n    acc[key as KbdKeys] = `{${value}}`\n    return acc\n  }, {} as Record<KbdKeys, string>)\n\n  return {\n    ...initTestKbd,\n    SHIFT_TAB: `{Shift>}{${kbd.TAB}}`,\n  }\n}\n"], "names": [], "mappings": "AAAO,SAAS,MAAS,GAAA;AACvB,EAAO,OAAA;AAAA,IACL,GAAK,EAAA,KAAA;AAAA,IACL,UAAY,EAAA,WAAA;AAAA,IACZ,UAAY,EAAA,WAAA;AAAA,IACZ,WAAa,EAAA,YAAA;AAAA,IACb,QAAU,EAAA,SAAA;AAAA,IACV,SAAW,EAAA,WAAA;AAAA,IACX,SAAW,EAAA,UAAA;AAAA,IACX,OAAS,EAAA,SAAA;AAAA,IACT,MAAQ,EAAA,QAAA;AAAA,IACR,GAAK,EAAA,KAAA;AAAA,IACL,KAAO,EAAA,OAAA;AAAA,IACP,MAAQ,EAAA,QAAA;AAAA,IACR,EAAI,EAAA,IAAA;AAAA,IACJ,GAAK,EAAA,KAAA;AAAA,IACL,GAAK,EAAA,KAAA;AAAA,IACL,GAAK,EAAA,KAAA;AAAA,IACL,EAAI,EAAA,IAAA;AAAA,IACJ,EAAI,EAAA,IAAA;AAAA,IACJ,EAAI,EAAA,IAAA;AAAA,IACJ,EAAI,EAAA,IAAA;AAAA,IACJ,EAAI,EAAA,IAAA;AAAA,IACJ,EAAI,EAAA,IAAA;AAAA,IACJ,EAAI,EAAA,IAAA;AAAA,IACJ,EAAI,EAAA,IAAA;AAAA,IACJ,IAAM,EAAA,MAAA;AAAA,IACN,IAAM,EAAA,MAAA;AAAA,IACN,SAAW,EAAA,UAAA;AAAA,IACX,OAAS,EAAA,QAAA;AAAA,IACT,KAAO,EAAA,OAAA;AAAA,IACP,KAAO,EAAA,GAAA;AAAA,IACP,GAAK,EAAA,KAAA;AAAA,IACL,IAAM,EAAA,SAAA;AAAA,IACN,QAAU,EAAA,GAAA;AAAA,IACV,UAAY,EAAA;AAAA,GACd;AACF;;;;"}