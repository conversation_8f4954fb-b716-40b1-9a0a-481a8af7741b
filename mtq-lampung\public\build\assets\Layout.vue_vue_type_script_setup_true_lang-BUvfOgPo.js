import{d as c,c as f,h as m,o as s,w as _,q as g,m as y,u as r,I as w,J as $,a as l,b as n,k as x,t as p,E as z,e as u,F as B,r as P,S,i as b,A as k}from"./app-BxByyVXe.js";import{P as C,a as N,_ as O}from"./AppLogoIcon.vue_vue_type_script_setup_true_lang-43VByX6w.js";import{r as V}from"./useForwardExpose-67BWFZEI.js";const I=c({__name:"BaseSeparator",props:{orientation:{default:"horizontal"},decorative:{type:Boolean},asChild:{type:Boolean},as:{}},setup(o){const e=o,t=["horizontal","vertical"];function d(a){return t.includes(a)}const i=f(()=>d(e.orientation)?e.orientation:"horizontal"),h=f(()=>i.value==="vertical"?e.orientation:void 0),v=f(()=>e.decorative?{role:"none"}:{"aria-orientation":h.value,role:"separator"});return(a,K)=>(s(),m(r(C),y({as:a.as,"as-child":a.asChild,"data-orientation":i.value},v.value),{default:_(()=>[g(a.$slots,"default")]),_:3},16,["as","as-child","data-orientation"]))}}),A=c({__name:"Separator",props:{orientation:{default:"horizontal"},decorative:{type:Boolean},asChild:{type:Boolean},as:{}},setup(o){const e=o;return(t,d)=>(s(),m(I,w($(e)),{default:_(()=>[g(t.$slots,"default")]),_:3},16))}}),E=c({__name:"Separator",props:{orientation:{default:"horizontal"},decorative:{type:Boolean,default:!0},asChild:{type:Boolean},as:{},class:{}},setup(o){const e=o,t=V(e,"class");return(d,i)=>(s(),m(r(A),y({"data-slot":"separator-root"},r(t),{class:r(N)("bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px",e.class)}),null,16,["class"]))}}),L={class:"mb-0.5 text-base font-medium"},R={key:0,class:"text-sm text-muted-foreground"},Y=c({__name:"HeadingSmall",props:{title:{},description:{}},setup(o){return(e,t)=>(s(),l("header",null,[n("h3",L,p(e.title),1),e.description?(s(),l("p",R,p(e.description),1)):x("",!0)]))}}),T={class:"mb-8 space-y-0.5"},F={class:"text-xl font-semibold tracking-tight"},H={key:0,class:"text-sm text-muted-foreground"},j=c({__name:"Heading",props:{title:{},description:{}},setup(o){return(e,t)=>(s(),l("div",T,[n("h2",F,p(e.title),1),e.description?(s(),l("p",H,p(e.description),1)):x("",!0)]))}}),q={class:"px-4 py-6"},D={class:"flex flex-col space-y-8 md:space-y-0 lg:flex-row lg:space-y-0 lg:space-x-12"},J={class:"w-full max-w-xl lg:w-48"},M={class:"flex flex-col space-y-1 space-x-0"},U={class:"flex-1 md:max-w-2xl"},G={class:"max-w-xl space-y-12"},Z=c({__name:"Layout",setup(o){var i;const e=[{title:"Profile",href:"/settings/profile"},{title:"Password",href:"/settings/password"},{title:"Appearance",href:"/settings/appearance"}],t=z(),d=(i=t.props.ziggy)!=null&&i.location?new URL(t.props.ziggy.location).pathname:"";return(h,v)=>(s(),l("div",q,[u(j,{title:"Settings",description:"Manage your profile and account settings"}),n("div",D,[n("aside",J,[n("nav",M,[(s(),l(B,null,P(e,a=>u(r(O),{key:a.href,variant:"ghost",class:k(["w-full justify-start",{"bg-muted":r(d)===a.href}]),"as-child":""},{default:_(()=>[u(r(S),{href:a.href},{default:_(()=>[b(p(a.title),1)]),_:2},1032,["href"])]),_:2},1032,["class"])),64))])]),u(r(E),{class:"my-6 md:hidden"}),n("div",U,[n("section",G,[g(h.$slots,"default")])])])]))}});export{Z as _,Y as a};
