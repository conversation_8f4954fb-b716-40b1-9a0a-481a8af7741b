{"version": 3, "file": "ToastPortal.js", "sources": ["../../src/Toast/ToastPortal.vue"], "sourcesContent": ["<script lang=\"ts\">\nimport type { TeleportProps } from '@/Teleport'\n\nexport interface ToastPortalProps extends TeleportProps {}\n</script>\n\n<script setup lang=\"ts\">\nimport { TeleportPrimitive } from '@/Teleport'\n\nconst props = defineProps<ToastPortalProps>()\n</script>\n\n<template>\n  <TeleportPrimitive v-bind=\"props\">\n    <slot />\n  </TeleportPrimitive>\n</template>\n"], "names": [], "mappings": ";;;;;;;;;;;;AASA,IAAA,MAAM,KAAQ,GAAA,OAAA;;;;;;;;;;;;;;"}