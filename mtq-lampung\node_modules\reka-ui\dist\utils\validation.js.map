{"version": 3, "file": "validation.js", "sources": ["../../src/Splitter/utils/validation.ts"], "sourcesContent": ["import type { PanelConstraints } from '../SplitterPanel.vue'\nimport { assert } from './assert'\nimport { fuzzyNumbersEqual } from './compare'\nimport { resizePanel } from './resizePanel'\n\n// All units must be in percentages; pixel values should be pre-converted\nexport function validatePanelGroupLayout({\n  layout: prevLayout,\n  panelConstraints,\n}: {\n  layout: number[]\n  panelConstraints: PanelConstraints[]\n}): number[] {\n  const nextLayout = [...prevLayout]\n  const nextLayoutTotalSize = nextLayout.reduce(\n    (accumulated, current) => accumulated + current,\n    0,\n  )\n\n  // Validate layout expectations\n  if (nextLayout.length !== panelConstraints.length) {\n    throw new Error(\n      `Invalid ${panelConstraints.length} panel layout: ${nextLayout\n        .map(size => `${size}%`)\n        .join(', ')}`,\n    )\n  }\n  else if (!fuzzyNumbersEqual(nextLayoutTotalSize, 100)) {\n    // This is not ideal so we should warn about it, but it may be recoverable in some cases\n    // (especially if the amount is small)\n\n    if (true) {\n      console.warn(\n        `WARNING: Invalid layout total size: ${nextLayout\n          .map(size => `${size}%`)\n          .join(', ')}. Layout normalization will be applied.`,\n      )\n    }\n    for (let index = 0; index < panelConstraints.length; index++) {\n      const unsafeSize = nextLayout[index]\n      assert(unsafeSize != null)\n      const safeSize = (100 / nextLayoutTotalSize) * unsafeSize\n      nextLayout[index] = safeSize\n    }\n  }\n\n  let remainingSize = 0\n\n  // First pass: Validate the proposed layout given each panel's constraints\n  for (let index = 0; index < panelConstraints.length; index++) {\n    const unsafeSize = nextLayout[index]\n    assert(unsafeSize != null)\n\n    const safeSize = resizePanel({\n      panelConstraints,\n      panelIndex: index,\n      size: unsafeSize,\n    })\n\n    if (unsafeSize !== safeSize) {\n      remainingSize += unsafeSize - safeSize\n\n      nextLayout[index] = safeSize\n    }\n  }\n\n  // If there is additional, left over space, assign it to any panel(s) that permits it\n  // (It's not worth taking multiple additional passes to evenly distribute)\n  if (!fuzzyNumbersEqual(remainingSize, 0)) {\n    for (let index = 0; index < panelConstraints.length; index++) {\n      const prevSize = nextLayout[index]\n      assert(prevSize != null)\n      const unsafeSize = prevSize + remainingSize\n      const safeSize = resizePanel({\n        panelConstraints,\n        panelIndex: index,\n        size: unsafeSize,\n      })\n\n      if (prevSize !== safeSize) {\n        remainingSize -= safeSize - prevSize\n        nextLayout[index] = safeSize\n\n        // Once we've used up the remainder, bail\n        if (fuzzyNumbersEqual(remainingSize, 0))\n          break\n      }\n    }\n  }\n\n  return nextLayout\n}\n\nexport function validatePanelConstraints({\n  panelConstraints: panelConstraintsArray,\n  panelId,\n  panelIndex,\n}: {\n  panelConstraints: PanelConstraints[]\n  panelId: string | undefined\n  panelIndex: number\n}): boolean {\n  if (import.meta.env.DEV) {\n    const warnings = []\n\n    const panelConstraints = panelConstraintsArray[panelIndex]\n    assert(panelConstraints)\n\n    const {\n      collapsedSize = 0,\n      collapsible = false,\n      defaultSize,\n      maxSize = 100,\n      minSize = 0,\n    } = panelConstraints\n\n    if (minSize > maxSize) {\n      warnings.push(\n        `min size (${minSize}%) should not be greater than max size (${maxSize}%)`,\n      )\n    }\n\n    if (defaultSize != null) {\n      if (defaultSize < 0) {\n        warnings.push('default size should not be less than 0')\n      }\n      else if (\n        defaultSize < minSize\n        && (!collapsible || defaultSize !== collapsedSize)\n      ) {\n        warnings.push('default size should not be less than min size')\n      }\n\n      if (defaultSize > 100)\n        warnings.push('default size should not be greater than 100')\n      else if (defaultSize > maxSize)\n        warnings.push('default size should not be greater than max size')\n    }\n\n    if (collapsedSize > minSize)\n      warnings.push('collapsed size should not be greater than min size')\n\n    if (warnings.length > 0) {\n      const name = panelId != null ? `Panel \"${panelId}\"` : 'Panel'\n      console.warn(\n        `${name} has an invalid configuration:\\n\\n${warnings.join('\\n')}`,\n      )\n\n      return false\n    }\n  }\n\n  return true\n}\n"], "names": [], "mappings": ";;;;AAMO,SAAS,wBAAyB,CAAA;AAAA,EACvC,MAAQ,EAAA,UAAA;AAAA,EACR;AACF,CAGa,EAAA;AACL,EAAA,MAAA,UAAA,GAAa,CAAC,GAAG,UAAU,CAAA;AACjC,EAAA,MAAM,sBAAsB,UAAW,CAAA,MAAA;AAAA,IACrC,CAAC,WAAa,EAAA,OAAA,KAAY,WAAc,GAAA,OAAA;AAAA,IACxC;AAAA,GACF;AAGI,EAAA,IAAA,UAAA,CAAW,MAAW,KAAA,gBAAA,CAAiB,MAAQ,EAAA;AACjD,IAAA,MAAM,IAAI,KAAA;AAAA,MACR,CAAW,QAAA,EAAA,gBAAA,CAAiB,MAAM,CAAA,eAAA,EAAkB,WACjD,GAAI,CAAA,CAAQ,IAAA,KAAA,CAAA,EAAG,IAAI,CAAA,CAAA,CAAG,CACtB,CAAA,IAAA,CAAK,IAAI,CAAC,CAAA;AAAA,KACf;AAAA,GAEO,MAAA,IAAA,CAAC,iBAAkB,CAAA,mBAAA,EAAqB,GAAG,CAAG,EAAA;AAIrD,IAAU;AACA,MAAA,OAAA,CAAA,IAAA;AAAA,QACN,CAAA,oCAAA,EAAuC,UACpC,CAAA,GAAA,CAAI,CAAQ,IAAA,KAAA,CAAG,EAAA,IAAI,CAAG,CAAA,CAAA,CAAA,CACtB,IAAK,CAAA,IAAI,CAAC,CAAA,uCAAA;AAAA,OACf;AAAA;AAEF,IAAA,KAAA,IAAS,KAAQ,GAAA,CAAA,EAAG,KAAQ,GAAA,gBAAA,CAAiB,QAAQ,KAAS,EAAA,EAAA;AACtD,MAAA,MAAA,UAAA,GAAa,WAAW,KAAK,CAAA;AACnC,MAAA,MAAA,CAAO,cAAc,IAAI,CAAA;AACnB,MAAA,MAAA,QAAA,GAAY,MAAM,mBAAuB,GAAA,UAAA;AAC/C,MAAA,UAAA,CAAW,KAAK,CAAI,GAAA,QAAA;AAAA;AACtB;AAGF,EAAA,IAAI,aAAgB,GAAA,CAAA;AAGpB,EAAA,KAAA,IAAS,KAAQ,GAAA,CAAA,EAAG,KAAQ,GAAA,gBAAA,CAAiB,QAAQ,KAAS,EAAA,EAAA;AACtD,IAAA,MAAA,UAAA,GAAa,WAAW,KAAK,CAAA;AACnC,IAAA,MAAA,CAAO,cAAc,IAAI,CAAA;AAEzB,IAAA,MAAM,WAAW,WAAY,CAAA;AAAA,MAC3B,gBAAA;AAAA,MACA,UAAY,EAAA,KAAA;AAAA,MACZ,IAAM,EAAA;AAAA,KACP,CAAA;AAED,IAAA,IAAI,eAAe,QAAU,EAAA;AAC3B,MAAA,aAAA,IAAiB,UAAa,GAAA,QAAA;AAE9B,MAAA,UAAA,CAAW,KAAK,CAAI,GAAA,QAAA;AAAA;AACtB;AAKF,EAAA,IAAI,CAAC,iBAAA,CAAkB,aAAe,EAAA,CAAC,CAAG,EAAA;AACxC,IAAA,KAAA,IAAS,KAAQ,GAAA,CAAA,EAAG,KAAQ,GAAA,gBAAA,CAAiB,QAAQ,KAAS,EAAA,EAAA;AACtD,MAAA,MAAA,QAAA,GAAW,WAAW,KAAK,CAAA;AACjC,MAAA,MAAA,CAAO,YAAY,IAAI,CAAA;AACvB,MAAA,MAAM,aAAa,QAAW,GAAA,aAAA;AAC9B,MAAA,MAAM,WAAW,WAAY,CAAA;AAAA,QAC3B,gBAAA;AAAA,QACA,UAAY,EAAA,KAAA;AAAA,QACZ,IAAM,EAAA;AAAA,OACP,CAAA;AAED,MAAA,IAAI,aAAa,QAAU,EAAA;AACzB,QAAA,aAAA,IAAiB,QAAW,GAAA,QAAA;AAC5B,QAAA,UAAA,CAAW,KAAK,CAAI,GAAA,QAAA;AAGhB,QAAA,IAAA,iBAAA,CAAkB,eAAe,CAAC,CAAA;AACpC,UAAA;AAAA;AACJ;AACF;AAGK,EAAA,OAAA,UAAA;AACT;;;;"}