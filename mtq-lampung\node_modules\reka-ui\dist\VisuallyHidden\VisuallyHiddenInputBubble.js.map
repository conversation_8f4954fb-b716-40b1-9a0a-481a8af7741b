{"version": 3, "file": "VisuallyHiddenInputBubble.js", "sources": ["../../src/VisuallyHidden/VisuallyHiddenInputBubble.vue"], "sourcesContent": ["<script lang=\"ts\">\nexport interface VisuallyHiddenInputBubbleProps<T> {\n  name: string\n  value: T\n  checked?: boolean\n  required?: boolean\n  disabled?: boolean\n  feature?: VisuallyHiddenProps['feature']\n}\n</script>\n\n<script setup lang=\"ts\" generic=\"T\">\nimport type { VisuallyHiddenProps } from './VisuallyHidden.vue'\nimport { computed, watch } from 'vue'\nimport { usePrimitiveElement } from '@/Primitive'\nimport VisuallyHidden from './VisuallyHidden.vue'\n\ndefineOptions({\n  inheritAttrs: false,\n})\n\nconst props = withDefaults(defineProps<VisuallyHiddenInputBubbleProps<T>>(), {\n  feature: 'fully-hidden',\n  checked: undefined,\n})\n\nconst { primitiveElement, currentElement } = usePrimitiveElement()\nconst valueState = computed(() => props.checked ?? props.value)\n\nwatch(valueState, (cur, prev) => {\n  if (!currentElement.value)\n    return\n\n  const input = currentElement.value as HTMLInputElement\n  const inputProto = window.HTMLInputElement.prototype\n  const descriptor = Object.getOwnPropertyDescriptor(inputProto, 'value') as PropertyDescriptor\n  const setValue = descriptor.set\n  if (setValue && cur !== prev) {\n    const inputEvent = new Event('input', { bubbles: true })\n    const changeEvent = new Event('change', { bubbles: true })\n    setValue.call(input, cur)\n    input.dispatchEvent(inputEvent)\n    input.dispatchEvent(changeEvent)\n  }\n})\n</script>\n\n<template>\n  <VisuallyHidden\n    ref=\"primitiveElement\"\n    v-bind=\"{ ...props, ...$attrs }\"\n    as=\"input\"\n  />\n</template>\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAqBA,IAAA,MAAM,KAAQ,GAAA,OAAA;AAKd,IAAA,MAAM,EAAE,gBAAA,EAAkB,cAAe,EAAA,GAAI,mBAAoB,EAAA;AACjE,IAAA,MAAM,aAAa,QAAS,CAAA,MAAM,KAAM,CAAA,OAAA,IAAW,MAAM,KAAK,CAAA;AAE9D,IAAM,KAAA,CAAA,UAAA,EAAY,CAAC,GAAA,EAAK,IAAS,KAAA;AAC/B,MAAA,IAAI,CAAC,cAAe,CAAA,KAAA;AAClB,QAAA;AAEF,MAAA,MAAM,QAAQ,cAAe,CAAA,KAAA;AAC7B,MAAM,MAAA,UAAA,GAAa,OAAO,gBAAiB,CAAA,SAAA;AAC3C,MAAA,MAAM,UAAa,GAAA,MAAA,CAAO,wBAAyB,CAAA,UAAA,EAAY,OAAO,CAAA;AACtE,MAAA,MAAM,WAAW,UAAW,CAAA,GAAA;AAC5B,MAAI,IAAA,QAAA,IAAY,QAAQ,IAAM,EAAA;AAC5B,QAAA,MAAM,aAAa,IAAI,KAAA,CAAM,SAAS,EAAE,OAAA,EAAS,MAAM,CAAA;AACvD,QAAA,MAAM,cAAc,IAAI,KAAA,CAAM,UAAU,EAAE,OAAA,EAAS,MAAM,CAAA;AACzD,QAAS,QAAA,CAAA,IAAA,CAAK,OAAO,GAAG,CAAA;AACxB,QAAA,KAAA,CAAM,cAAc,UAAU,CAAA;AAC9B,QAAA,KAAA,CAAM,cAAc,WAAW,CAAA;AAAA;AACjC,KACD,CAAA;;;;;;;;;;;;"}