{"version": 3, "file": "utils.cjs", "sources": ["../../src/NavigationMenu/utils.ts"], "sourcesContent": ["import { getActiveElement } from '@/shared'\n\nexport type Orientation = 'vertical' | 'horizontal'\nexport type Direction = 'ltr' | 'rtl'\n\nexport function getOpenState(open: boolean) {\n  return open ? 'open' : 'closed'\n}\n\nexport function makeTriggerId(baseId: string, value: string) {\n  return `${baseId}-trigger-${value}`\n}\n\nexport function makeContentId(baseId: string, value: string) {\n  return `${baseId}-content-${value}`\n}\n\nexport const LINK_SELECT = 'navigationMenu.linkSelect'\nexport const EVENT_ROOT_CONTENT_DISMISS = 'navigationMenu.rootContentDismiss'\n\n/**\n * Returns a list of potential tabbable candidates.\n *\n * NOTE: This is only a close approximation. For example it doesn't take into account cases like when\n * elements are not visible. This cannot be worked out easily by just reading a property, but rather\n * necessitate runtime knowledge (computed styles, etc). We deal with these cases separately.\n *\n * See: https://developer.mozilla.org/en-US/docs/Web/API/TreeWalker\n * Credit: https://github.com/discord/focus-layers/blob/master/src/util/wrapFocus.tsx#L1\n */\nexport function getTabbableCandidates(container: HTMLElement) {\n  const nodes: HTMLElement[] = []\n  const walker = document.createTreeWalker(container, NodeFilter.SHOW_ELEMENT, {\n    acceptNode: (node: any) => {\n      const isHiddenInput = node.tagName === 'INPUT' && node.type === 'hidden'\n      if (node.disabled || node.hidden || isHiddenInput)\n        return NodeFilter.FILTER_SKIP\n      // `.tabIndex` is not the same as the `tabindex` attribute. It works on the\n      // runtime's understanding of tabbability, so this automatically accounts\n      // for any kind of element that could be tabbed to.\n      return node.tabIndex >= 0\n        ? NodeFilter.FILTER_ACCEPT\n        : NodeFilter.FILTER_SKIP\n    },\n  })\n  while (walker.nextNode()) nodes.push(walker.currentNode as HTMLElement)\n  // we do not take into account the order of nodes with positive `tabIndex` as it\n  // hinders accessibility to have tab order different from visual order.\n  return nodes\n}\n\nexport function focusFirst(candidates: HTMLElement[]) {\n  const previouslyFocusedElement = getActiveElement()\n  return candidates.some((candidate) => {\n    // if focus is already where we want to go, we don't want to keep going through the candidates\n    if (candidate === previouslyFocusedElement)\n      return true\n    candidate.focus()\n    return getActiveElement() !== previouslyFocusedElement\n  })\n}\n\nexport function removeFromTabOrder(candidates: HTMLElement[]) {\n  candidates.forEach((candidate) => {\n    candidate.dataset.tabindex = candidate.getAttribute('tabindex') || ''\n    candidate.setAttribute('tabindex', '-1')\n  })\n  return () => {\n    candidates.forEach((candidate) => {\n      const prevTabIndex = candidate.dataset.tabindex as string\n      candidate.setAttribute('tabindex', prevTabIndex)\n    })\n  }\n}\n\nexport function whenMouse<E extends PointerEvent>(handler: (event?: E) => void) {\n  return (event: E) => (event.pointerType === 'mouse' ? handler(event) : undefined)\n}\n"], "names": ["getActiveElement"], "mappings": ";;;;AAKO,SAAS,aAAa,IAAe,EAAA;AAC1C,EAAA,OAAO,OAAO,MAAS,GAAA,QAAA;AACzB;AAEgB,SAAA,aAAA,CAAc,QAAgB,KAAe,EAAA;AAC3D,EAAO,OAAA,CAAA,EAAG,MAAM,CAAA,SAAA,EAAY,KAAK,CAAA,CAAA;AACnC;AAEgB,SAAA,aAAA,CAAc,QAAgB,KAAe,EAAA;AAC3D,EAAO,OAAA,CAAA,EAAG,MAAM,CAAA,SAAA,EAAY,KAAK,CAAA,CAAA;AACnC;AAEO,MAAM,WAAc,GAAA;AACpB,MAAM,0BAA6B,GAAA;AAYnC,SAAS,sBAAsB,SAAwB,EAAA;AAC5D,EAAA,MAAM,QAAuB,EAAC;AAC9B,EAAA,MAAM,MAAS,GAAA,QAAA,CAAS,gBAAiB,CAAA,SAAA,EAAW,WAAW,YAAc,EAAA;AAAA,IAC3E,UAAA,EAAY,CAAC,IAAc,KAAA;AACzB,MAAA,MAAM,aAAgB,GAAA,IAAA,CAAK,OAAY,KAAA,OAAA,IAAW,KAAK,IAAS,KAAA,QAAA;AAChE,MAAI,IAAA,IAAA,CAAK,QAAY,IAAA,IAAA,CAAK,MAAU,IAAA,aAAA;AAClC,QAAA,OAAO,UAAW,CAAA,WAAA;AAIpB,MAAA,OAAO,IAAK,CAAA,QAAA,IAAY,CACpB,GAAA,UAAA,CAAW,gBACX,UAAW,CAAA,WAAA;AAAA;AACjB,GACD,CAAA;AACD,EAAA,OAAO,OAAO,QAAS,EAAA,EAAS,KAAA,CAAA,IAAA,CAAK,OAAO,WAA0B,CAAA;AAGtE,EAAO,OAAA,KAAA;AACT;AAEO,SAAS,WAAW,UAA2B,EAAA;AACpD,EAAA,MAAM,2BAA2BA,wCAAiB,EAAA;AAClD,EAAO,OAAA,UAAA,CAAW,IAAK,CAAA,CAAC,SAAc,KAAA;AAEpC,IAAA,IAAI,SAAc,KAAA,wBAAA;AAChB,MAAO,OAAA,IAAA;AACT,IAAA,SAAA,CAAU,KAAM,EAAA;AAChB,IAAA,OAAOA,0CAAuB,KAAA,wBAAA;AAAA,GAC/B,CAAA;AACH;AAEO,SAAS,mBAAmB,UAA2B,EAAA;AAC5D,EAAW,UAAA,CAAA,OAAA,CAAQ,CAAC,SAAc,KAAA;AAChC,IAAA,SAAA,CAAU,OAAQ,CAAA,QAAA,GAAW,SAAU,CAAA,YAAA,CAAa,UAAU,CAAK,IAAA,EAAA;AACnE,IAAU,SAAA,CAAA,YAAA,CAAa,YAAY,IAAI,CAAA;AAAA,GACxC,CAAA;AACD,EAAA,OAAO,MAAM;AACX,IAAW,UAAA,CAAA,OAAA,CAAQ,CAAC,SAAc,KAAA;AAChC,MAAM,MAAA,YAAA,GAAe,UAAU,OAAQ,CAAA,QAAA;AACvC,MAAU,SAAA,CAAA,YAAA,CAAa,YAAY,YAAY,CAAA;AAAA,KAChD,CAAA;AAAA,GACH;AACF;AAEO,SAAS,UAAkC,OAA8B,EAAA;AAC9E,EAAA,OAAO,CAAC,KAAc,KAAA,KAAA,CAAM,gBAAgB,OAAU,GAAA,OAAA,CAAQ,KAAK,CAAI,GAAA,MAAA;AACzE;;;;;;;;;;;;"}