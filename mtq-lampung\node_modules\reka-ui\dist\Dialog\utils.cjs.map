{"version": 3, "file": "utils.cjs", "sources": ["../../src/Dialog/utils.ts"], "sourcesContent": ["import type { Ref } from 'vue'\nimport { onMounted } from 'vue'\n\nconst DEFAULT_TITLE_NAME = 'DialogTitle'\nconst DEFAULT_CONTENT_NAME = 'DialogContent'\n\nexport type WarningProps = {\n  titleName?: string\n  contentName?: string\n  componentLink?: string\n  titleId: string\n  descriptionId: string\n  contentElement: Ref<HTMLElement | undefined>\n}\n\nexport function useWarning({\n  titleName = DEFAULT_TITLE_NAME,\n  contentName = DEFAULT_CONTENT_NAME,\n  componentLink = 'dialog.html#title',\n  titleId,\n  descriptionId,\n  contentElement,\n}: WarningProps) {\n  const TITLE_MESSAGE = `Warning: \\`${contentName}\\` requires a \\`${titleName}\\` for the component to be accessible for screen reader users.\n\nIf you want to hide the \\`${titleName}\\`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://www.reka-ui.com/docs/components/${componentLink}`\n\n  const DESCRIPTION_MESSAGE = `Warning: Missing \\`Description\\` or \\`aria-describedby=\"undefined\"\\` for ${contentName}.`\n\n  onMounted(() => {\n    const hasTitle = document.getElementById(titleId)\n    if (!hasTitle)\n      console.warn(TITLE_MESSAGE)\n\n    const describedById = contentElement.value?.getAttribute('aria-describedby')\n    // if we have an id and the user hasn't set aria-describedby=\"undefined\"\n    if (descriptionId && describedById) {\n      const hasDescription = document.getElementById(descriptionId)\n      if (!hasDescription)\n        console.warn(DESCRIPTION_MESSAGE)\n    }\n  })\n}\n"], "names": ["onMounted"], "mappings": ";;;;AAGA,MAAM,kBAAqB,GAAA,aAAA;AAC3B,MAAM,oBAAuB,GAAA,eAAA;AAWtB,SAAS,UAAW,CAAA;AAAA,EACzB,SAAY,GAAA,kBAAA;AAAA,EACZ,WAAc,GAAA,oBAAA;AAAA,EACd,aAAgB,GAAA,mBAAA;AAAA,EAChB,OAAA;AAAA,EACA,aAAA;AAAA,EACA;AACF,CAAiB,EAAA;AACf,EAAA,MAAM,aAAgB,GAAA,CAAA,WAAA,EAAc,WAAW,CAAA,gBAAA,EAAmB,SAAS,CAAA;;AAAA,0BAAA,EAEjD,SAAS,CAAA;;AAAA,kEAAA,EAE+B,aAAa,CAAA,CAAA;AAE/E,EAAM,MAAA,mBAAA,GAAsB,4EAA4E,WAAW,CAAA,CAAA,CAAA;AAEnH,EAAAA,aAAA,CAAU,MAAM;AACd,IAAM,MAAA,QAAA,GAAW,QAAS,CAAA,cAAA,CAAe,OAAO,CAAA;AAChD,IAAA,IAAI,CAAC,QAAA;AACH,MAAA,OAAA,CAAQ,KAAK,aAAa,CAAA;AAE5B,IAAA,MAAM,aAAgB,GAAA,cAAA,CAAe,KAAO,EAAA,YAAA,CAAa,kBAAkB,CAAA;AAE3E,IAAA,IAAI,iBAAiB,aAAe,EAAA;AAClC,MAAM,MAAA,cAAA,GAAiB,QAAS,CAAA,cAAA,CAAe,aAAa,CAAA;AAC5D,MAAA,IAAI,CAAC,cAAA;AACH,QAAA,OAAA,CAAQ,KAAK,mBAAmB,CAAA;AAAA;AACpC,GACD,CAAA;AACH;;;;"}