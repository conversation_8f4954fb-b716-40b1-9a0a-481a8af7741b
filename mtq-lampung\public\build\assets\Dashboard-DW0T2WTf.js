import{_ as c}from"./AppLayout.vue_vue_type_script_setup_true_lang-va1QG6ga.js";import{d as s,c as b,a,o as i,b as e,e as r,u as _,g as u,w as h,F as p}from"./app-BxByyVXe.js";import"./AppLogoIcon.vue_vue_type_script_setup_true_lang-43VByX6w.js";import"./useForwardExpose-67BWFZEI.js";import"./RovingFocusGroup-hazQWYmS.js";const f={class:"absolute inset-0 size-full stroke-neutral-900/20 dark:stroke-neutral-100/20",fill:"none"},m=["id"],v=["fill"],d=s({__name:"PlaceholderPattern",setup(n){const o=b(()=>`pattern-${Math.random().toString(36).substring(2,9)}`);return(l,t)=>(i(),a("svg",f,[e("defs",null,[e("pattern",{id:o.value,x:"0",y:"0",width:"8",height:"8",patternUnits:"userSpaceOnUse"},t[0]||(t[0]=[e("path",{d:"M-1 5L5 -1M3 9L8.5 3.5","stroke-width":"0.5"},null,-1)]),8,m)]),e("rect",{stroke:"none",fill:`url(#${o.value})`,width:"100%",height:"100%"},null,8,v)]))}}),x={class:"flex h-full flex-1 flex-col gap-4 rounded-xl p-4 overflow-x-auto"},g={class:"grid auto-rows-min gap-4 md:grid-cols-3"},k={class:"relative aspect-video overflow-hidden rounded-xl border border-sidebar-border/70 dark:border-sidebar-border"},w={class:"relative aspect-video overflow-hidden rounded-xl border border-sidebar-border/70 dark:border-sidebar-border"},$={class:"relative aspect-video overflow-hidden rounded-xl border border-sidebar-border/70 dark:border-sidebar-border"},B={class:"relative min-h-[100vh] flex-1 rounded-xl border border-sidebar-border/70 md:min-h-min dark:border-sidebar-border"},N=s({__name:"Dashboard",setup(n){const o=[{title:"Dashboard",href:"/dashboard"}];return(l,t)=>(i(),a(p,null,[r(_(u),{title:"Dashboard"}),r(c,{breadcrumbs:o},{default:h(()=>[e("div",x,[e("div",g,[e("div",k,[r(d)]),e("div",w,[r(d)]),e("div",$,[r(d)])]),e("div",B,[r(d)])])]),_:1})],64))}});export{N as default};
