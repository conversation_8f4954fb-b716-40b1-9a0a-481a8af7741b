{"version": 3, "file": "utils.js", "sources": ["../../src/Avatar/utils.ts"], "sourcesContent": ["import type { ImgHTMLAttributes, Ref } from 'vue'\nimport { isClient } from '@vueuse/shared'\nimport { computed, onMounted, onUnmounted, ref, watchEffect } from 'vue'\n\nexport type ImageLoadingStatus = 'idle' | 'loading' | 'loaded' | 'error'\n\nfunction resolveLoadingStatus(image: HTMLImageElement | null, src?: string): ImageLoadingStatus {\n  if (!image) {\n    return 'idle'\n  }\n  if (!src) {\n    return 'error'\n  }\n  if (image.src !== src) {\n    image.src = src\n  }\n  return image.complete && image.naturalWidth > 0 ? 'loaded' : 'loading'\n}\n\nexport function useImageLoadingStatus(src: Ref<string>, { referrerPolicy, crossOrigin }: { referrerPolicy?: Ref<ImgHTMLAttributes['referrerpolicy']>, crossOrigin?: Ref<ImgHTMLAttributes['crossorigin']> } = {}) {\n  const isMounted = ref(false)\n  const imageRef = ref<HTMLImageElement | null>(null)\n  const image = computed(() => {\n    if (!isMounted.value) {\n      return null\n    }\n    if (!imageRef.value && isClient) {\n      imageRef.value = new window.Image()\n    }\n    return imageRef.value\n  })\n\n  const loadingStatus = ref<ImageLoadingStatus>(resolveLoadingStatus(image.value, src.value))\n\n  const updateStatus = (status: ImageLoadingStatus) => () => {\n    if (isMounted.value)\n      loadingStatus.value = status\n  }\n\n  onMounted(() => {\n    isMounted.value = true\n\n    watchEffect((onCleanup) => {\n      const img = image.value\n      if (!img)\n        return\n\n      loadingStatus.value = resolveLoadingStatus(img, src.value)\n\n      const handleLoad = updateStatus('loaded')\n      const handleError = updateStatus('error')\n\n      img.addEventListener('load', handleLoad)\n      img.addEventListener('error', handleError)\n\n      if (referrerPolicy?.value)\n        img.referrerPolicy = referrerPolicy.value\n      if (typeof crossOrigin?.value === 'string')\n        img.crossOrigin = crossOrigin.value\n\n      onCleanup(() => {\n        img.removeEventListener('load', handleLoad)\n        img.removeEventListener('error', handleError)\n      })\n    })\n  })\n\n  onUnmounted(() => {\n    isMounted.value = false\n  })\n\n  return loadingStatus\n}\n"], "names": [], "mappings": ";;;AAMA,SAAS,oBAAA,CAAqB,OAAgC,GAAkC,EAAA;AAC9F,EAAA,IAAI,CAAC,KAAO,EAAA;AACV,IAAO,OAAA,MAAA;AAAA;AAET,EAAA,IAAI,CAAC,GAAK,EAAA;AACR,IAAO,OAAA,OAAA;AAAA;AAET,EAAI,IAAA,KAAA,CAAM,QAAQ,GAAK,EAAA;AACrB,IAAA,KAAA,CAAM,GAAM,GAAA,GAAA;AAAA;AAEd,EAAA,OAAO,KAAM,CAAA,QAAA,IAAY,KAAM,CAAA,YAAA,GAAe,IAAI,QAAW,GAAA,SAAA;AAC/D;AAEO,SAAS,sBAAsB,GAAkB,EAAA,EAAE,gBAAgB,WAAY,EAAA,GAAwH,EAAI,EAAA;AAChN,EAAM,MAAA,SAAA,GAAY,IAAI,KAAK,CAAA;AAC3B,EAAM,MAAA,QAAA,GAAW,IAA6B,IAAI,CAAA;AAClD,EAAM,MAAA,KAAA,GAAQ,SAAS,MAAM;AAC3B,IAAI,IAAA,CAAC,UAAU,KAAO,EAAA;AACpB,MAAO,OAAA,IAAA;AAAA;AAET,IAAI,IAAA,CAAC,QAAS,CAAA,KAAA,IAAS,QAAU,EAAA;AAC/B,MAAS,QAAA,CAAA,KAAA,GAAQ,IAAI,MAAA,CAAO,KAAM,EAAA;AAAA;AAEpC,IAAA,OAAO,QAAS,CAAA,KAAA;AAAA,GACjB,CAAA;AAED,EAAA,MAAM,gBAAgB,GAAwB,CAAA,oBAAA,CAAqB,MAAM,KAAO,EAAA,GAAA,CAAI,KAAK,CAAC,CAAA;AAE1F,EAAM,MAAA,YAAA,GAAe,CAAC,MAAA,KAA+B,MAAM;AACzD,IAAA,IAAI,SAAU,CAAA,KAAA;AACZ,MAAA,aAAA,CAAc,KAAQ,GAAA,MAAA;AAAA,GAC1B;AAEA,EAAA,SAAA,CAAU,MAAM;AACd,IAAA,SAAA,CAAU,KAAQ,GAAA,IAAA;AAElB,IAAA,WAAA,CAAY,CAAC,SAAc,KAAA;AACzB,MAAA,MAAM,MAAM,KAAM,CAAA,KAAA;AAClB,MAAA,IAAI,CAAC,GAAA;AACH,QAAA;AAEF,MAAA,aAAA,CAAc,KAAQ,GAAA,oBAAA,CAAqB,GAAK,EAAA,GAAA,CAAI,KAAK,CAAA;AAEzD,MAAM,MAAA,UAAA,GAAa,aAAa,QAAQ,CAAA;AACxC,MAAM,MAAA,WAAA,GAAc,aAAa,OAAO,CAAA;AAExC,MAAI,GAAA,CAAA,gBAAA,CAAiB,QAAQ,UAAU,CAAA;AACvC,MAAI,GAAA,CAAA,gBAAA,CAAiB,SAAS,WAAW,CAAA;AAEzC,MAAA,IAAI,cAAgB,EAAA,KAAA;AAClB,QAAA,GAAA,CAAI,iBAAiB,cAAe,CAAA,KAAA;AACtC,MAAI,IAAA,OAAO,aAAa,KAAU,KAAA,QAAA;AAChC,QAAA,GAAA,CAAI,cAAc,WAAY,CAAA,KAAA;AAEhC,MAAA,SAAA,CAAU,MAAM;AACd,QAAI,GAAA,CAAA,mBAAA,CAAoB,QAAQ,UAAU,CAAA;AAC1C,QAAI,GAAA,CAAA,mBAAA,CAAoB,SAAS,WAAW,CAAA;AAAA,OAC7C,CAAA;AAAA,KACF,CAAA;AAAA,GACF,CAAA;AAED,EAAA,WAAA,CAAY,MAAM;AAChB,IAAA,SAAA,CAAU,KAAQ,GAAA,KAAA;AAAA,GACnB,CAAA;AAED,EAAO,OAAA,aAAA;AACT;;;;"}