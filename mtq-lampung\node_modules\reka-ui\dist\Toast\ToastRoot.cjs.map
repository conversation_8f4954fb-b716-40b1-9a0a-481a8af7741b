{"version": 3, "file": "ToastRoot.cjs", "sources": ["../../src/Toast/ToastRoot.vue"], "sourcesContent": ["<script lang=\"ts\">\nimport type { Ref } from 'vue'\nimport type { ToastRootImplEmits, ToastRootImplProps } from './ToastRootImpl.vue'\nimport { useForwardExpose } from '@/shared'\n\nexport type ToastRootEmits = Omit<ToastRootImplEmits, 'close'> & {\n  /** Event handler called when the open state changes */\n  'update:open': [value: boolean]\n}\n\nexport interface ToastRootProps extends ToastRootImplProps {\n  /** The open state of the dialog when it is initially rendered. Use when you do not need to control its open state. */\n  defaultOpen?: boolean\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with Vue animation libraries.\n   */\n  forceMount?: boolean\n}\n</script>\n\n<script setup lang=\"ts\">\nimport { useVModel } from '@vueuse/core'\nimport { Presence } from '@/Presence'\nimport ToastRootImpl from './ToastRootImpl.vue'\n\nconst props = withDefaults(defineProps<ToastRootProps>(), {\n  type: 'foreground',\n  open: undefined,\n  defaultOpen: true,\n  as: 'li',\n})\n\nconst emits = defineEmits<ToastRootEmits>()\n\ndefineSlots<{\n  default?: (props: {\n    /** Current open state */\n    open: typeof open.value\n    /** Remaining time (in ms) */\n    remaining: number\n    /** Total time the toast will remain visible for (in ms) */\n    duration: number\n  }) => any\n}>()\n\nconst { forwardRef } = useForwardExpose()\nconst open = useVModel(props, 'open', emits, {\n  defaultValue: props.defaultOpen,\n  passive: (props.open === undefined) as false,\n}) as Ref<boolean>\n</script>\n\n<template>\n  <Presence :present=\"forceMount || open\">\n    <ToastRootImpl\n      :ref=\"forwardRef\"\n      v-slot=\"{ remaining, duration: _duration }\"\n      :open=\"open\"\n      :type=\"type\"\n      :as=\"as\"\n      :as-child=\"asChild\"\n      :duration=\"duration\"\n      v-bind=\"$attrs\"\n      @close=\"open = false\"\n      @pause=\"emits('pause')\"\n      @resume=\"emits('resume')\"\n      @escape-key-down=\"emits('escapeKeyDown', $event)\"\n      @swipe-start=\"(event) => {\n        emits('swipeStart', event);\n        if (!event.defaultPrevented) {\n          (event.currentTarget as HTMLElement).setAttribute('data-swipe', 'start');\n        }\n      }\"\n      @swipe-move=\"(event) => {\n        emits('swipeMove', event);\n        if (!event.defaultPrevented) {\n          const { x, y } = event.detail.delta;\n          const target = event.currentTarget as HTMLElement\n          target.setAttribute('data-swipe', 'move');\n          target.style.setProperty('--reka-toast-swipe-move-x', `${x}px`);\n          target.style.setProperty('--reka-toast-swipe-move-y', `${y}px`);\n        }\n      }\"\n      @swipe-cancel=\"(event) => {\n        emits('swipeCancel', event);\n        if (!event.defaultPrevented) {\n          const target = event.currentTarget as HTMLElement\n          target.setAttribute('data-swipe', 'cancel');\n          target.style.removeProperty('--reka-toast-swipe-move-x');\n          target.style.removeProperty('--reka-toast-swipe-move-y');\n          target.style.removeProperty('--reka-toast-swipe-end-x');\n          target.style.removeProperty('--reka-toast-swipe-end-y');\n        }\n      }\"\n      @swipe-end=\"(event) => {\n        emits('swipeEnd', event);\n        if (!event.defaultPrevented) {\n          const { x, y } = event.detail.delta;\n          const target = event.currentTarget as HTMLElement\n          target.setAttribute('data-swipe', 'end');\n          target.style.removeProperty('--reka-toast-swipe-move-x');\n          target.style.removeProperty('--reka-toast-swipe-move-y');\n          target.style.setProperty('--reka-toast-swipe-end-x', `${x}px`);\n          target.style.setProperty('--reka-toast-swipe-end-y', `${y}px`);\n          open = false;\n        }\n      }\"\n    >\n      <slot\n        :remaining=\"remaining\"\n        :duration=\"_duration\"\n        :open=\"open\"\n      />\n    </ToastRootImpl>\n  </Presence>\n</template>\n"], "names": ["useForwardExpose", "useVModel"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AA0BA,IAAA,MAAM,KAAQ,GAAA,OAAA;AAOd,IAAA,MAAM,KAAQ,GAAA,MAAA;AAad,IAAM,MAAA,EAAE,UAAW,EAAA,GAAIA,wCAAiB,EAAA;AACxC,IAAA,MAAM,IAAO,GAAAC,cAAA,CAAU,KAAO,EAAA,MAAA,EAAQ,KAAO,EAAA;AAAA,MAC3C,cAAc,KAAM,CAAA,WAAA;AAAA,MACpB,OAAA,EAAU,MAAM,IAAS,KAAA;AAAA,KAC1B,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}