import{P as z,r as ne,S as B}from"./AppLogoIcon.vue_vue_type_script_setup_true_lang-43VByX6w.js";import{a0 as se,a1 as oe,Z as re,a2 as ie,a3 as le,a4 as ue,a5 as ce,F as de,a6 as fe,a7 as me,a8 as pe,a9 as ve,V as be,aa as he,ab as ye,T as ge,ac as Ee,ad as Se,ae as we,af as Ae,ag as Ce,ah as Te,ai as _,aj as Ie,ak as Re,al as Me,c as A,am as Pe,h as D,k as Oe,a as Fe,b as xe,an as Ne,ao as _e,ap as De,aq as ke,ar as Ue,f as $e,i as Ve,e as G,as as je,at as Be,d as I,au as Le,av as He,aw as Ke,ax as ze,ay as Ge,az as qe,aA as We,aB as Ye,aC as Qe,aD as Je,aE as Xe,aF as x,L as Ze,aG as et,aH as tt,J as at,aI as O,aJ as nt,aK as st,aL as ot,aM as rt,aN as it,aO as lt,aP as ut,aQ as ct,aR as dt,aS as k,aT as ft,aU as mt,aV as pt,aW as vt,H as bt,aX as ht,aY as yt,aZ as gt,a_ as q,X as Et,a$ as St,m as wt,s as W,A as At,I as Ct,U as Tt,b0 as It,b1 as Rt,b2 as Mt,b3 as Pt,b4 as Ot,b5 as Ft,n as xt,b6 as Nt,b7 as _t,M as Dt,b8 as kt,p as Y,b9 as Ut,ba as $t,o as U,bb as Vt,bc as $,bd as jt,be as Bt,bf as Lt,R as Ht,bg as Kt,B as v,bh as zt,bi as Gt,r as qt,q as V,bj as Wt,bk as Yt,y as Qt,bl as Jt,bm as Xt,bn as Zt,bo as ea,bp as ta,bq as aa,N as na,K as sa,br as oa,bs as ra,bt as ia,t as la,bu as Q,_ as ua,bv as ca,bw as J,O as N,Q as da,bx as fa,by as ma,u as T,bz as pa,bA as va,bB as ba,bC as ha,bD as X,bE as ya,bF as ga,bG as Ea,Y as Sa,bH as wa,bI as Aa,bJ as Ca,bK as Ta,bL as Ia,bM as Ra,G as Ma,D as Pa,bN as Oa,bO as Fa,l as M,P as Z,W as xa,bP as Na,bQ as _a,w as F,bR as Da,C as ka,v as Ua,bS as $a,j as Va,bT as ja}from"./app-BxByyVXe.js";import{p as Ba,i as La,u as ee,b as Ha}from"./useForwardExpose-67BWFZEI.js";/**
* vue v3.5.16
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/const Ka=()=>{},za=Object.freeze(Object.defineProperty({__proto__:null,BaseTransition:se,BaseTransitionPropsValidators:oe,Comment:re,DeprecationTypes:ie,EffectScope:le,ErrorCodes:ue,ErrorTypeStrings:ce,Fragment:de,KeepAlive:fe,ReactiveEffect:me,Static:pe,Suspense:ve,Teleport:be,Text:he,TrackOpTypes:ye,Transition:ge,TransitionGroup:Ee,TriggerOpTypes:Se,VueElement:we,assertNumber:Ae,callWithAsyncErrorHandling:Ce,callWithErrorHandling:Te,camelize:_,capitalize:Ie,cloneVNode:Re,compatUtils:Me,compile:Ka,computed:A,createApp:Pe,createBlock:D,createCommentVNode:Oe,createElementBlock:Fe,createElementVNode:xe,createHydrationRenderer:Ne,createPropsRestProxy:_e,createRenderer:De,createSSRApp:ke,createSlots:Ue,createStaticVNode:$e,createTextVNode:Ve,createVNode:G,customRef:je,defineAsyncComponent:Be,defineComponent:I,defineCustomElement:Le,defineEmits:He,defineExpose:Ke,defineModel:ze,defineOptions:Ge,defineProps:qe,defineSSRCustomElement:We,defineSlots:Ye,devtools:Qe,effect:Je,effectScope:Xe,getCurrentInstance:x,getCurrentScope:Ze,getCurrentWatcher:et,getTransitionRawChildren:tt,guardReactiveProps:at,h:O,handleError:nt,hasInjectionContext:st,hydrate:ot,hydrateOnIdle:rt,hydrateOnInteraction:it,hydrateOnMediaQuery:lt,hydrateOnVisible:ut,initCustomFormatter:ct,initDirectivesForSSR:dt,inject:k,isMemoSame:ft,isProxy:mt,isReactive:pt,isReadonly:vt,isRef:bt,isRuntimeOnly:ht,isShallow:yt,isVNode:gt,markRaw:q,mergeDefaults:Et,mergeModels:St,mergeProps:wt,nextTick:W,normalizeClass:At,normalizeProps:Ct,normalizeStyle:Tt,onActivated:It,onBeforeMount:Rt,onBeforeUnmount:Mt,onBeforeUpdate:Pt,onDeactivated:Ot,onErrorCaptured:Ft,onMounted:xt,onRenderTracked:Nt,onRenderTriggered:_t,onScopeDispose:Dt,onServerPrefetch:kt,onUnmounted:Y,onUpdated:Ut,onWatcherCleanup:$t,openBlock:U,popScopeId:Vt,provide:$,proxyRefs:jt,pushScopeId:Bt,queuePostFlushCb:Lt,reactive:Ht,readonly:Kt,ref:v,registerRuntimeCompiler:zt,render:Gt,renderList:qt,renderSlot:V,resolveComponent:Wt,resolveDirective:Yt,resolveDynamicComponent:Qt,resolveFilter:Jt,resolveTransitionHooks:Xt,setBlockTracking:Zt,setDevtoolsHook:ea,setTransitionHooks:ta,shallowReactive:aa,shallowReadonly:na,shallowRef:sa,ssrContextKey:oa,ssrUtils:ra,stop:ia,toDisplayString:la,toHandlerKey:Q,toHandlers:ua,toRaw:ca,toRef:J,toRefs:N,toValue:da,transformVNodeArgs:fa,triggerRef:ma,unref:T,useAttrs:pa,useCssModule:va,useCssVars:ba,useHost:ha,useId:X,useModel:ya,useSSRContext:ga,useShadowRoot:Ea,useSlots:Sa,useTemplateRef:wa,useTransitionState:Aa,vModelCheckbox:Ca,vModelDynamic:Ta,vModelRadio:Ia,vModelSelect:Ra,vModelText:Ma,vShow:Pa,version:Oa,warn:Fa,watch:M,watchEffect:Z,watchPostEffect:xa,watchSyncEffect:Na,withAsyncContext:_a,withCtx:F,withDefaults:Da,withDirectives:ka,withKeys:Ua,withMemo:$a,withModifiers:Va,withScopeId:ja},Symbol.toStringTag,{value:"Module"})),un=I({__name:"VisuallyHidden",props:{feature:{default:"focusable"},asChild:{type:Boolean},as:{default:"span"}},setup(t){return(e,n)=>(U(),D(T(z),{as:e.as,"as-child":e.asChild,"aria-hidden":e.feature==="focusable"?"true":void 0,"data-hidden":e.feature==="fully-hidden"?"":void 0,tabindex:e.feature==="fully-hidden"?"-1":void 0,style:{position:"absolute",border:0,width:"1px",height:"1px",padding:0,margin:"-1px",overflow:"hidden",clip:"rect(0, 0, 0, 0)",clipPath:"inset(50%)",whiteSpace:"nowrap",wordWrap:"normal"}},{default:F(()=>[V(e.$slots,"default")]),_:3},8,["as","as-child","aria-hidden","data-hidden","tabindex"]))}});function te(t,e){const n=typeof t=="string"&&!e?`${t}Context`:e,a=Symbol(n);return[u=>{const r=k(a,u);if(r||r===null)return r;throw new Error(`Injection \`${a.toString()}\` not found. Component must be used within ${Array.isArray(t)?`one of the following components: ${t.join(", ")}`:`\`${t}\``}`)},u=>($(a,u),u)]}const[ae,cn]=te("ConfigProvider");function Ga(t){const e=ae({dir:v("ltr")});return A(()=>{var n;return(t==null?void 0:t.value)||((n=e.dir)==null?void 0:n.value)||"ltr"})}let qa=0;function dn(t,e="reka"){var a;const n=ae({useId:void 0});return Object.hasOwn(za,"useId")?`${e}-${(a=X)==null?void 0:a()}`:n.useId?`${e}-${n.useId()}`:`${e}-${++qa}`}function Wa(t,e){const n=v(t);function a(d){return e[n.value][d]??n.value}return{state:n,dispatch:d=>{n.value=a(d)}}}function Ya(t,e){var g;const n=v({}),a=v("none"),i=v(t),d=t.value?"mounted":"unmounted";let u;const r=((g=e.value)==null?void 0:g.ownerDocument.defaultView)??Ba,{state:m,dispatch:f}=Wa(d,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}}),l=o=>{var c;if(La){const b=new CustomEvent(o,{bubbles:!1,cancelable:!1});(c=e.value)==null||c.dispatchEvent(b)}};M(t,async(o,c)=>{var w;const b=c!==o;if(await W(),b){const R=a.value,E=P(e.value);o?(f("MOUNT"),l("enter"),E==="none"&&l("after-enter")):E==="none"||E==="undefined"||((w=n.value)==null?void 0:w.display)==="none"?(f("UNMOUNT"),l("leave"),l("after-leave")):c&&R!==E?(f("ANIMATION_OUT"),l("leave")):(f("UNMOUNT"),l("after-leave"))}},{immediate:!0});const s=o=>{const c=P(e.value),b=c.includes(o.animationName),w=m.value==="mounted"?"enter":"leave";if(o.target===e.value&&b&&(l(`after-${w}`),f("ANIMATION_END"),!i.value)){const R=e.value.style.animationFillMode;e.value.style.animationFillMode="forwards",u=r==null?void 0:r.setTimeout(()=>{var E;((E=e.value)==null?void 0:E.style.animationFillMode)==="forwards"&&(e.value.style.animationFillMode=R)})}o.target===e.value&&c==="none"&&f("ANIMATION_END")},p=o=>{o.target===e.value&&(a.value=P(e.value))},h=M(e,(o,c)=>{o?(n.value=getComputedStyle(o),o.addEventListener("animationstart",p),o.addEventListener("animationcancel",s),o.addEventListener("animationend",s)):(f("ANIMATION_END"),u!==void 0&&(r==null||r.clearTimeout(u)),c==null||c.removeEventListener("animationstart",p),c==null||c.removeEventListener("animationcancel",s),c==null||c.removeEventListener("animationend",s))},{immediate:!0}),y=M(m,()=>{const o=P(e.value);a.value=m.value==="mounted"?o:"none"});return Y(()=>{h(),y()}),{isPresent:A(()=>["mounted","unmountSuspended"].includes(m.value))}}function P(t){return t&&getComputedStyle(t).animationName||"none"}const fn=I({name:"Presence",props:{present:{type:Boolean,required:!0},forceMount:{type:Boolean}},slots:{},setup(t,{slots:e,expose:n}){var f;const{present:a,forceMount:i}=N(t),d=v(),{isPresent:u}=Ya(a,d);n({present:u});let r=e.default({present:u.value});r=ne(r||[]);const m=x();if(r&&(r==null?void 0:r.length)>1){const l=(f=m==null?void 0:m.parent)!=null&&f.type.name?`<${m.parent.type.name} />`:"component";throw new Error([`Detected an invalid children for \`${l}\` for  \`Presence\` component.`,"","Note: Presence works similarly to `v-if` directly, but it waits for animation/transition to finished before unmounting. So it expect only one direct child of valid VNode type.","You can apply a few solutions:",["Provide a single child element so that `presence` directive attach correctly.","Ensure the first child is an actual element instead of a raw text node or comment node."].map(s=>`  - ${s}`).join(`
`)].join(`
`))}return()=>i.value||a.value||u.value?O(e.default({present:u.value})[0],{ref:l=>{const s=ee(l);return typeof(s==null?void 0:s.hasAttribute)>"u"||(s!=null&&s.hasAttribute("data-reka-popper-content-wrapper")?d.value=s.firstElementChild:d.value=s),s}}):null}});function Qa(t){const e=x(),n=e==null?void 0:e.type.emits,a={};return n!=null&&n.length||console.warn(`No emitted event found. Please check component: ${e==null?void 0:e.type.__name}`),n==null||n.forEach(i=>{a[Q(_(i))]=(...d)=>t(i,...d)}),a}function L(){let t=document.activeElement;if(t==null)return null;for(;t!=null&&t.shadowRoot!=null&&t.shadowRoot.activeElement!=null;)t=t.shadowRoot.activeElement;return t}function Ja(t){const e=x(),n=Object.keys((e==null?void 0:e.type.props)??{}).reduce((i,d)=>{const u=(e==null?void 0:e.type.props[d]).default;return u!==void 0&&(i[d]=u),i},{}),a=J(t);return A(()=>{const i=N(a.value),d={},u=(e==null?void 0:e.vnode.props)??{};return Object.keys(u).forEach(r=>{d[_(r)]=u[r]}),Object.keys({...n,...d}).reduce((r,m)=>{var l;const f=(l=i[m])==null?void 0:l.value;return f!==void 0&&(r[m]=f),r},{})})}function mn(t,e){const n=Ja(t),a=e?Qa(e):{};return A(()=>({...n.value,...a}))}function H(){const t=v(),e=A(()=>{var n,a;return["#text","#comment"].includes((n=t.value)==null?void 0:n.$el.nodeName)?(a=t.value)==null?void 0:a.$el.nextElementSibling:ee(t)});return{primitiveElement:t,currentElement:e}}const K="data-reka-collection-item";function Xa(t={}){const{key:e="",isProvider:n=!1}=t,a=`${e}CollectionProvider`;let i;if(n){const l=v(new Map);i={collectionRef:v(),itemMap:l},$(a,i)}else i=k(a);const d=(l=!1)=>{const s=i.collectionRef.value;if(!s)return[];const p=Array.from(s.querySelectorAll(`[${K}]`)),y=Array.from(i.itemMap.value.values()).sort((S,g)=>p.indexOf(S.ref)-p.indexOf(g.ref));return l?y:y.filter(S=>S.ref.dataset.disabled!=="")},u=I({name:"CollectionSlot",setup(l,{slots:s}){const{primitiveElement:p,currentElement:h}=H();return M(h,()=>{i.collectionRef.value=h.value}),()=>O(B,{ref:p},s)}}),r=I({name:"CollectionItem",inheritAttrs:!1,props:{value:{validator:()=>!0}},setup(l,{slots:s,attrs:p}){const{primitiveElement:h,currentElement:y}=H();return Z(S=>{if(y.value){const g=q(y.value);i.itemMap.value.set(g,{ref:y.value,value:l.value}),S(()=>i.itemMap.value.delete(g))}}),()=>O(B,{...p,[K]:"",ref:h},s)}}),m=A(()=>Array.from(i.itemMap.value.values())),f=A(()=>i.itemMap.value.size);return{getItems:d,reactiveItems:m,itemMapSize:f,CollectionSlot:u,CollectionItem:r}}const Za="rovingFocusGroup.onEntryFocus",en={bubbles:!1,cancelable:!0},tn={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function an(t,e){return e!=="rtl"?t:t==="ArrowLeft"?"ArrowRight":t==="ArrowRight"?"ArrowLeft":t}function pn(t,e,n){const a=an(t.key,n);if(!(e==="vertical"&&["ArrowLeft","ArrowRight"].includes(a))&&!(e==="horizontal"&&["ArrowUp","ArrowDown"].includes(a)))return tn[a]}function nn(t,e=!1){const n=L();for(const a of t)if(a===n||(a.focus({preventScroll:e}),L()!==n))return}function vn(t,e){return t.map((n,a)=>t[(e+a)%t.length])}const[bn,sn]=te("RovingFocusGroup"),hn=I({__name:"RovingFocusGroup",props:{orientation:{default:void 0},dir:{},loop:{type:Boolean,default:!1},currentTabStopId:{},defaultCurrentTabStopId:{},preventScrollOnEntryFocus:{type:Boolean,default:!1},asChild:{type:Boolean},as:{}},emits:["entryFocus","update:currentTabStopId"],setup(t,{expose:e,emit:n}){const a=t,i=n,{loop:d,orientation:u,dir:r}=N(a),m=Ga(r),f=Ha(a,"currentTabStopId",i,{defaultValue:a.defaultCurrentTabStopId,passive:a.currentTabStopId===void 0}),l=v(!1),s=v(!1),p=v(0),{getItems:h,CollectionSlot:y}=Xa({isProvider:!0});function S(o){const c=!s.value;if(o.currentTarget&&o.target===o.currentTarget&&c&&!l.value){const b=new CustomEvent(Za,en);if(o.currentTarget.dispatchEvent(b),i("entryFocus",b),!b.defaultPrevented){const w=h().map(C=>C.ref).filter(C=>C.dataset.disabled!==""),R=w.find(C=>C.getAttribute("data-active")===""),E=w.find(C=>C.id===f.value),j=[R,E,...w].filter(Boolean);nn(j,a.preventScrollOnEntryFocus)}}s.value=!1}function g(){setTimeout(()=>{s.value=!1},1)}return e({getItems:h}),sn({loop:d,dir:m,orientation:u,currentTabStopId:f,onItemFocus:o=>{f.value=o},onItemShiftTab:()=>{l.value=!0},onFocusableItemAdd:()=>{p.value++},onFocusableItemRemove:()=>{p.value--}}),(o,c)=>(U(),D(T(y),null,{default:F(()=>[G(T(z),{tabindex:l.value||p.value===0?-1:0,"data-orientation":T(u),as:o.as,"as-child":o.asChild,dir:T(m),style:{outline:"none"},onMousedown:c[0]||(c[0]=b=>s.value=!0),onMouseup:g,onFocus:S,onBlur:c[1]||(c[1]=b=>l.value=!1)},{default:F(()=>[V(o.$slots,"default")]),_:3},8,["tabindex","data-orientation","as","as-child","dir"])]),_:3}))}});export{fn as P,un as _,dn as a,Xa as b,te as c,mn as d,Ja as e,nn as f,pn as g,L as h,bn as i,Qa as j,ae as k,Ga as l,hn as m,H as u,vn as w};
