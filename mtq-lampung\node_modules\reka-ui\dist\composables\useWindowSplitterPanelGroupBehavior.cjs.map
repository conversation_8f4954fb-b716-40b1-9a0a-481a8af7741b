{"version": 3, "file": "useWindowSplitterPanelGroupBehavior.cjs", "sources": ["../../src/Splitter/utils/composables/useWindowSplitterPanelGroupBehavior.ts"], "sourcesContent": ["import type { Ref } from 'vue'\nimport type { PanelData } from '../../SplitterPanel.vue'\nimport { watchEffect } from 'vue'\nimport { assert } from '../assert'\nimport { calculateAriaValues } from '../calculate'\nimport { fuzzyNumbersEqual } from '../compare'\nimport { getPanelGroupElement, getResizeHandleElementsForGroup, getResizeHandlePanelIds } from '../dom'\nimport { adjustLayoutByDelta } from '../layout'\nimport { determinePivotIndices } from '../pivot'\n\n// https://www.w3.org/WAI/ARIA/apg/patterns/windowsplitter/\n\nexport function useWindowSplitterPanelGroupBehavior({\n  eagerValuesRef,\n  groupId,\n  layout,\n  panelDataArray,\n  panelGroupElement,\n  setLayout,\n}: {\n  eagerValuesRef: Ref<{\n    panelDataArray: PanelData[]\n  }>\n  groupId: string\n  layout: Ref<number[]>\n  panelDataArray: PanelData[]\n  panelGroupElement: Ref<ParentNode | null>\n  setLayout: (sizes: number[]) => void\n}): void {\n  watchEffect((onCleanup) => {\n    const _panelGroupElement = panelGroupElement.value\n    if (!_panelGroupElement)\n      return\n\n    const resizeHandleElements = getResizeHandleElementsForGroup(\n      groupId,\n      _panelGroupElement,\n    )\n\n    for (let index = 0; index < panelDataArray.length - 1; index++) {\n      const { valueMax, valueMin, valueNow } = calculateAriaValues({\n        layout: layout.value,\n        panelsArray: panelDataArray,\n        pivotIndices: [index, index + 1],\n      })\n\n      const resizeHandleElement = resizeHandleElements[index]\n      if (resizeHandleElement == null) {\n        if (import.meta.env.DEV)\n          console.warn(`WARNING: Missing resize handle for PanelGroup \"${groupId}\"`)\n      }\n      else {\n        const panelData = panelDataArray[index]\n        assert(panelData)\n\n        resizeHandleElement.setAttribute('aria-controls', panelData.id)\n        resizeHandleElement.setAttribute(\n          'aria-valuemax',\n          `${Math.round(valueMax)}`,\n        )\n        resizeHandleElement.setAttribute(\n          'aria-valuemin',\n          `${Math.round(valueMin)}`,\n        )\n        resizeHandleElement.setAttribute(\n          'aria-valuenow',\n          valueNow != null ? `${Math.round(valueNow)}` : '',\n        )\n      }\n    }\n\n    onCleanup(() => {\n      resizeHandleElements.forEach((resizeHandleElement) => {\n        resizeHandleElement.removeAttribute('aria-controls')\n        resizeHandleElement.removeAttribute('aria-valuemax')\n        resizeHandleElement.removeAttribute('aria-valuemin')\n        resizeHandleElement.removeAttribute('aria-valuenow')\n      })\n    })\n  })\n\n  watchEffect((onCleanup) => {\n    const _panelGroupElement = panelGroupElement.value\n    if (!_panelGroupElement)\n      return\n\n    const eagerValues = eagerValuesRef.value\n    assert(eagerValues)\n\n    const { panelDataArray } = eagerValues\n    const groupElement = getPanelGroupElement(groupId, _panelGroupElement)\n    assert(groupElement != null, `No group found for id \"${groupId}\"`)\n\n    const handles = getResizeHandleElementsForGroup(groupId, _panelGroupElement)\n    assert(handles)\n\n    const cleanupFunctions = handles.map((handle) => {\n      const handleId = handle.getAttribute('data-panel-resize-handle-id')\n      assert(handleId)\n\n      const [idBefore, idAfter] = getResizeHandlePanelIds(\n        groupId,\n        handleId,\n        panelDataArray,\n        _panelGroupElement,\n      )\n      if (idBefore == null || idAfter == null)\n        return () => {}\n\n      const onKeyDown = (event: KeyboardEvent) => {\n        if (event.defaultPrevented)\n          return\n\n        switch (event.key) {\n          case 'Enter': {\n            event.preventDefault()\n\n            const index = panelDataArray.findIndex(\n              panelData => panelData.id === idBefore,\n            )\n            if (index >= 0) {\n              const panelData = panelDataArray[index]\n              assert(panelData)\n\n              const size = layout.value[index]\n\n              const {\n                collapsedSize = 0,\n                collapsible,\n                minSize = 0,\n              } = panelData.constraints\n\n              if (size != null && collapsible) {\n                const nextLayout = adjustLayoutByDelta({\n                  delta: fuzzyNumbersEqual(size, collapsedSize)\n                    ? minSize - collapsedSize\n                    : collapsedSize - size,\n                  layout: layout.value,\n                  panelConstraints: panelDataArray.map(\n                    panelData => panelData.constraints,\n                  ),\n                  pivotIndices: determinePivotIndices(\n                    groupId,\n                    handleId,\n                    _panelGroupElement,\n                  ),\n                  trigger: 'keyboard',\n                })\n                if (layout.value !== nextLayout)\n                  setLayout(nextLayout)\n              }\n            }\n            break\n          }\n        }\n      }\n\n      handle.addEventListener('keydown', onKeyDown)\n      return () => {\n        handle.removeEventListener('keydown', onKeyDown)\n      }\n    })\n\n    onCleanup(() => {\n      cleanupFunctions.forEach(cleanupFunction => cleanupFunction())\n    })\n  })\n}\n"], "names": ["watchEffect", "getResizeHandleElementsForGroup", "calculateAriaValues", "assert", "panelDataArray", "getPanelGroupElement", "getResizeHandlePanelIds", "adjustLayoutByDelta", "fuzzyNumbersEqual", "panelData", "determinePivotIndices"], "mappings": ";;;;;;;;;;AAYO,SAAS,mCAAoC,CAAA;AAAA,EAClD,cAAA;AAAA,EACA,OAAA;AAAA,EACA,MAAA;AAAA,EACA,cAAA;AAAA,EACA,iBAAA;AAAA,EACA;AACF,CASS,EAAA;AACP,EAAAA,eAAA,CAAY,CAAC,SAAc,KAAA;AACzB,IAAA,MAAM,qBAAqB,iBAAkB,CAAA,KAAA;AAC7C,IAAA,IAAI,CAAC,kBAAA;AACH,MAAA;AAEF,IAAA,MAAM,oBAAuB,GAAAC,yCAAA;AAAA,MAC3B,OAAA;AAAA,MACA;AAAA,KACF;AAEA,IAAA,KAAA,IAAS,QAAQ,CAAG,EAAA,KAAA,GAAQ,cAAe,CAAA,MAAA,GAAS,GAAG,KAAS,EAAA,EAAA;AAC9D,MAAA,MAAM,EAAE,QAAA,EAAU,QAAU,EAAA,QAAA,KAAaC,mCAAoB,CAAA;AAAA,QAC3D,QAAQ,MAAO,CAAA,KAAA;AAAA,QACf,WAAa,EAAA,cAAA;AAAA,QACb,YAAc,EAAA,CAAC,KAAO,EAAA,KAAA,GAAQ,CAAC;AAAA,OAChC,CAAA;AAEK,MAAA,MAAA,mBAAA,GAAsB,qBAAqB,KAAK,CAAA;AACtD,MAAA,IAAI,uBAAuB,IAAM,EAAA,CAI5B,MAAA;AACG,QAAA,MAAA,SAAA,GAAY,eAAe,KAAK,CAAA;AACtC,QAAAC,mBAAA,CAAO,SAAS,CAAA;AAEI,QAAA,mBAAA,CAAA,YAAA,CAAa,eAAiB,EAAA,SAAA,CAAU,EAAE,CAAA;AAC1C,QAAA,mBAAA,CAAA,YAAA;AAAA,UAClB,eAAA;AAAA,UACA,CAAG,EAAA,IAAA,CAAK,KAAM,CAAA,QAAQ,CAAC,CAAA;AAAA,SACzB;AACoB,QAAA,mBAAA,CAAA,YAAA;AAAA,UAClB,eAAA;AAAA,UACA,CAAG,EAAA,IAAA,CAAK,KAAM,CAAA,QAAQ,CAAC,CAAA;AAAA,SACzB;AACoB,QAAA,mBAAA,CAAA,YAAA;AAAA,UAClB,eAAA;AAAA,UACA,YAAY,IAAO,GAAA,CAAA,EAAG,KAAK,KAAM,CAAA,QAAQ,CAAC,CAAK,CAAA,GAAA;AAAA,SACjD;AAAA;AACF;AAGF,IAAA,SAAA,CAAU,MAAM;AACO,MAAA,oBAAA,CAAA,OAAA,CAAQ,CAAC,mBAAwB,KAAA;AACpD,QAAA,mBAAA,CAAoB,gBAAgB,eAAe,CAAA;AACnD,QAAA,mBAAA,CAAoB,gBAAgB,eAAe,CAAA;AACnD,QAAA,mBAAA,CAAoB,gBAAgB,eAAe,CAAA;AACnD,QAAA,mBAAA,CAAoB,gBAAgB,eAAe,CAAA;AAAA,OACpD,CAAA;AAAA,KACF,CAAA;AAAA,GACF,CAAA;AAED,EAAAH,eAAA,CAAY,CAAC,SAAc,KAAA;AACzB,IAAA,MAAM,qBAAqB,iBAAkB,CAAA,KAAA;AAC7C,IAAA,IAAI,CAAC,kBAAA;AACH,MAAA;AAEF,IAAA,MAAM,cAAc,cAAe,CAAA,KAAA;AACnC,IAAAG,mBAAA,CAAO,WAAW,CAAA;AAEZ,IAAA,MAAA,EAAE,cAAAC,EAAAA,eAAAA,EAAmB,GAAA,WAAA;AACrB,IAAA,MAAA,YAAA,GAAeC,8BAAqB,CAAA,OAAA,EAAS,kBAAkB,CAAA;AACrE,IAAAF,mBAAA,CAAO,YAAgB,IAAA,IAAA,EAAM,CAA0B,uBAAA,EAAA,OAAO,CAAG,CAAA,CAAA,CAAA;AAE3D,IAAA,MAAA,OAAA,GAAUF,yCAAgC,CAAA,OAAA,EAAS,kBAAkB,CAAA;AAC3E,IAAAE,mBAAA,CAAO,OAAO,CAAA;AAEd,IAAA,MAAM,gBAAmB,GAAA,OAAA,CAAQ,GAAI,CAAA,CAAC,MAAW,KAAA;AACzC,MAAA,MAAA,QAAA,GAAW,MAAO,CAAA,YAAA,CAAa,6BAA6B,CAAA;AAClE,MAAAA,mBAAA,CAAO,QAAQ,CAAA;AAET,MAAA,MAAA,CAAC,QAAU,EAAA,OAAO,CAAI,GAAAG,iCAAA;AAAA,QAC1B,OAAA;AAAA,QACA,QAAA;AAAA,QACAF,eAAAA;AAAAA,QACA;AAAA,OACF;AACI,MAAA,IAAA,QAAA,IAAY,QAAQ,OAAW,IAAA,IAAA;AACjC,QAAA,OAAO,MAAM;AAAA,SAAC;AAEV,MAAA,MAAA,SAAA,GAAY,CAAC,KAAyB,KAAA;AAC1C,QAAA,IAAI,KAAM,CAAA,gBAAA;AACR,UAAA;AAEF,QAAA,QAAQ,MAAM,GAAK;AAAA,UACjB,KAAK,OAAS,EAAA;AACZ,YAAA,KAAA,CAAM,cAAe,EAAA;AAErB,YAAA,MAAM,QAAQA,eAAe,CAAA,SAAA;AAAA,cAC3B,CAAA,SAAa,KAAA,SAAA,CAAU,EAAO,KAAA;AAAA,aAChC;AACA,YAAA,IAAI,SAAS,CAAG,EAAA;AACR,cAAA,MAAA,SAAA,GAAYA,gBAAe,KAAK,CAAA;AACtC,cAAAD,mBAAA,CAAO,SAAS,CAAA;AAEV,cAAA,MAAA,IAAA,GAAO,MAAO,CAAA,KAAA,CAAM,KAAK,CAAA;AAEzB,cAAA,MAAA;AAAA,gBACJ,aAAgB,GAAA,CAAA;AAAA,gBAChB,WAAA;AAAA,gBACA,OAAU,GAAA;AAAA,kBACR,SAAU,CAAA,WAAA;AAEV,cAAA,IAAA,IAAA,IAAQ,QAAQ,WAAa,EAAA;AAC/B,gBAAA,MAAM,aAAaI,gCAAoB,CAAA;AAAA,kBACrC,OAAOC,+BAAkB,CAAA,IAAA,EAAM,aAAa,CACxC,GAAA,OAAA,GAAU,gBACV,aAAgB,GAAA,IAAA;AAAA,kBACpB,QAAQ,MAAO,CAAA,KAAA;AAAA,kBACf,kBAAkBJ,eAAe,CAAA,GAAA;AAAA,oBAC/B,CAAAK,eAAaA,UAAU,CAAA;AAAA,mBACzB;AAAA,kBACA,YAAc,EAAAC,iCAAA;AAAA,oBACZ,OAAA;AAAA,oBACA,QAAA;AAAA,oBACA;AAAA,mBACF;AAAA,kBACA,OAAS,EAAA;AAAA,iBACV,CAAA;AACD,gBAAA,IAAI,OAAO,KAAU,KAAA,UAAA;AACnB,kBAAA,SAAA,CAAU,UAAU,CAAA;AAAA;AACxB;AAEF,YAAA;AAAA;AACF;AACF,OACF;AAEO,MAAA,MAAA,CAAA,gBAAA,CAAiB,WAAW,SAAS,CAAA;AAC5C,MAAA,OAAO,MAAM;AACJ,QAAA,MAAA,CAAA,mBAAA,CAAoB,WAAW,SAAS,CAAA;AAAA,OACjD;AAAA,KACD,CAAA;AAED,IAAA,SAAA,CAAU,MAAM;AACG,MAAA,gBAAA,CAAA,OAAQ,CAAA,CAAmB,eAAA,KAAA,eAAA,EAAiB,CAAA;AAAA,KAC9D,CAAA;AAAA,GACF,CAAA;AACH;;;;"}