{"version": 3, "file": "ToastProvider.cjs", "sources": ["../../src/Toast/ToastProvider.vue"], "sourcesContent": ["<script lang=\"ts\">\nimport type { Ref } from 'vue'\nimport type { SwipeDirection } from './utils'\nimport { useCollection } from '@/Collection'\nimport { createContext } from '@/shared'\n\ntype ToastProviderContext = {\n  label: Ref<string>\n  duration: Ref<number>\n  swipeDirection: Ref<SwipeDirection>\n  swipeThreshold: Ref<number>\n  toastCount: Ref<number>\n  viewport: Ref<HTMLElement | undefined>\n  onViewportChange: (viewport: HTMLElement) => void\n  onToastAdd: () => void\n  onToastRemove: () => void\n  isFocusedToastEscapeKeyDownRef: Ref<boolean>\n  isClosePausedRef: Ref<boolean>\n}\n\nexport interface ToastProviderProps {\n  /**\n   * An author-localized label for each toast. Used to help screen reader users\n   * associate the interruption with a toast.\n   * @defaultValue 'Notification'\n   */\n  label?: string\n  /**\n   * Time in milliseconds that each toast should remain visible for.\n   * @defaultValue 5000\n   */\n  duration?: number\n  /**\n   * Direction of pointer swipe that should close the toast.\n   * @defaultValue 'right'\n   */\n  swipeDirection?: SwipeDirection\n  /**\n   * Distance in pixels that the swipe must pass before a close is triggered.\n   * @defaultValue 50\n   */\n  swipeThreshold?: number\n}\n\nexport const [injectToastProviderContext, provideToastProviderContext]\n  = createContext<ToastProviderContext>('ToastProvider')\n</script>\n\n<script setup lang=\"ts\">\nimport { ref, toRefs } from 'vue'\n\ndefineOptions({\n  inheritAttrs: false,\n})\n\nconst props = withDefaults(defineProps<ToastProviderProps>(), {\n  label: 'Notification',\n  duration: 5000,\n  swipeDirection: 'right',\n  swipeThreshold: 50,\n})\nconst { label, duration, swipeDirection, swipeThreshold } = toRefs(props)\nuseCollection({ isProvider: true })\n\nconst viewport = ref<HTMLElement>()\nconst toastCount = ref(0)\nconst isFocusedToastEscapeKeyDownRef = ref(false)\nconst isClosePausedRef = ref(false)\n\nif (props.label && typeof props.label === 'string' && !props.label.trim()) {\n  const error = 'Invalid prop `label` supplied to `ToastProvider`. Expected non-empty `string`.'\n  throw new Error(error)\n}\n\nprovideToastProviderContext({\n  label,\n  duration,\n  swipeDirection,\n  swipeThreshold,\n  toastCount,\n  viewport,\n  onViewportChange(el) {\n    viewport.value = el\n  },\n  onToastAdd() {\n    toastCount.value++\n  },\n  onToastRemove() {\n    toastCount.value--\n  },\n  isFocusedToastEscapeKeyDownRef,\n  isClosePausedRef,\n\n})\n</script>\n\n<template>\n  <slot />\n</template>\n"], "names": ["createContext", "toRefs", "useCollection", "ref"], "mappings": ";;;;;;AA4CO,MAAM,CAAC,0BAAA,EAA4B,2BAA2B,CAAA,GACjEA,mCAAoC,eAAe;;;;;;;;;;;;;AAUvD,IAAA,MAAM,KAAQ,GAAA,OAAA;AAMd,IAAA,MAAM,EAAE,KAAO,EAAA,QAAA,EAAU,gBAAgB,cAAe,EAAA,GAAIC,WAAO,KAAK,CAAA;AACxE,IAAcC,mCAAA,CAAA,EAAE,UAAY,EAAA,IAAA,EAAM,CAAA;AAElC,IAAA,MAAM,WAAWC,OAAiB,EAAA;AAClC,IAAM,MAAA,UAAA,GAAaA,QAAI,CAAC,CAAA;AACxB,IAAM,MAAA,8BAAA,GAAiCA,QAAI,KAAK,CAAA;AAChD,IAAM,MAAA,gBAAA,GAAmBA,QAAI,KAAK,CAAA;AAElC,IAAI,IAAA,KAAA,CAAM,KAAS,IAAA,OAAO,KAAM,CAAA,KAAA,KAAU,YAAY,CAAC,KAAA,CAAM,KAAM,CAAA,IAAA,EAAQ,EAAA;AACzE,MAAA,MAAM,KAAQ,GAAA,gFAAA;AACd,MAAM,MAAA,IAAI,MAAM,KAAK,CAAA;AAAA;AAGvB,IAA4B,2BAAA,CAAA;AAAA,MAC1B,KAAA;AAAA,MACA,QAAA;AAAA,MACA,cAAA;AAAA,MACA,cAAA;AAAA,MACA,UAAA;AAAA,MACA,QAAA;AAAA,MACA,iBAAiB,EAAI,EAAA;AACnB,QAAA,QAAA,CAAS,KAAQ,GAAA,EAAA;AAAA,OACnB;AAAA,MACA,UAAa,GAAA;AACX,QAAW,UAAA,CAAA,KAAA,EAAA;AAAA,OACb;AAAA,MACA,aAAgB,GAAA;AACd,QAAW,UAAA,CAAA,KAAA,EAAA;AAAA,OACb;AAAA,MACA,8BAAA;AAAA,MACA;AAAA,KAED,CAAA;;;;;;;;;;"}