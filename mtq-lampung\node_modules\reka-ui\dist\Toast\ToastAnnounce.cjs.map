{"version": 3, "file": "ToastAnnounce.cjs", "sources": ["../../src/Toast/ToastAnnounce.vue"], "sourcesContent": ["<script setup lang=\"ts\">\nimport { useRafFn } from '@vueuse/core'\nimport { useTimeout } from '@vueuse/shared'\nimport { ref } from 'vue'\nimport { VisuallyHidden } from '@/VisuallyHidden'\nimport { injectToastProviderContext } from './ToastProvider.vue'\n\nconst providerContext = injectToastProviderContext()\n\nconst isAnnounced = useTimeout(1000)\nconst renderAnnounceText = ref(false)\n\nuseRafFn(() => {\n  renderAnnounceText.value = true\n})\n</script>\n\n<template>\n  <VisuallyHidden v-if=\"isAnnounced || renderAnnounceText\">\n    {{ providerContext.label.value }}\n    <slot />\n  </VisuallyHidden>\n</template>\n"], "names": ["injectToastProviderContext", "useTimeout", "ref", "useRafFn"], "mappings": ";;;;;;;;;;;AAOA,IAAA,MAAM,kBAAkBA,8CAA2B,EAAA;AAEnD,IAAM,MAAA,WAAA,GAAcC,kBAAW,GAAI,CAAA;AACnC,IAAM,MAAA,kBAAA,GAAqBC,QAAI,KAAK,CAAA;AAEpC,IAAAC,aAAA,CAAS,MAAM;AACb,MAAA,kBAAA,CAAmB,KAAQ,GAAA,IAAA;AAAA,KAC5B,CAAA;;;;;;;;;;;;;;;"}