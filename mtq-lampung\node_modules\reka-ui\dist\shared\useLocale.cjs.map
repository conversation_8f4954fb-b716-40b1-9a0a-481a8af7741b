{"version": 3, "file": "useLocale.cjs", "sources": ["../../src/shared/useLocale.ts"], "sourcesContent": ["import type { Ref } from 'vue'\nimport { computed, ref } from 'vue'\nimport { injectConfigProviderContext } from '@/ConfigProvider/ConfigProvider.vue'\n\nexport function useLocale(locale?: Ref<string | undefined>) {\n  const context = injectConfigProviderContext({\n    locale: ref('en'),\n  })\n  return computed(() => locale?.value || context.locale?.value || 'en')\n}\n"], "names": ["injectConfigProviderContext", "ref", "computed"], "mappings": ";;;;;AAIO,SAAS,UAAU,MAAkC,EAAA;AAC1D,EAAA,MAAM,UAAUA,yDAA4B,CAAA;AAAA,IAC1C,MAAA,EAAQC,QAAI,IAAI;AAAA,GACjB,CAAA;AACD,EAAA,OAAOC,aAAS,MAAM,MAAA,EAAQ,SAAS,OAAQ,CAAA,MAAA,EAAQ,SAAS,IAAI,CAAA;AACtE;;;;"}