{"version": 3, "file": "utils.js", "sources": ["../../src/Listbox/utils.ts"], "sourcesContent": ["import { isEqual } from 'ohash'\n\nexport function queryCheckedElement(parentEl: HTMLElement | null) {\n  return parentEl?.querySelector('[data-state=checked]') as HTMLElement | null\n}\n\nexport function valueComparator<T>(value: T | T[] | undefined, currentValue: T, comparator?: string | ((a: T, b: T) => boolean)) {\n  if (value === undefined)\n    return false\n  else if (Array.isArray(value))\n    return value.some(val => compare(val, currentValue, comparator))\n  else\n    return compare(value, currentValue, comparator)\n}\n\nexport function compare<T>(value?: T, currentValue?: T, comparator?: string | ((a: T, b: T) => boolean)) {\n  if (value === undefined || currentValue === undefined)\n    return false\n\n  if (typeof value === 'string')\n    return value === currentValue\n\n  if (typeof comparator === 'function')\n    return comparator(value, currentValue)\n\n  if (typeof comparator === 'string')\n    return value?.[comparator as keyof T] === currentValue?.[comparator as keyof T]\n\n  return isEqual(value, currentValue)\n}\n"], "names": [], "mappings": ";;AAEO,SAAS,oBAAoB,QAA8B,EAAA;AAChE,EAAO,OAAA,QAAA,EAAU,cAAc,sBAAsB,CAAA;AACvD;AAEgB,SAAA,eAAA,CAAmB,KAA4B,EAAA,YAAA,EAAiB,UAAiD,EAAA;AAC/H,EAAA,IAAI,KAAU,KAAA,MAAA;AACZ,IAAO,OAAA,KAAA;AAAA,OACA,IAAA,KAAA,CAAM,QAAQ,KAAK,CAAA;AAC1B,IAAA,OAAO,MAAM,IAAK,CAAA,CAAA,GAAA,KAAO,QAAQ,GAAK,EAAA,YAAA,EAAc,UAAU,CAAC,CAAA;AAAA;AAE/D,IAAO,OAAA,OAAA,CAAQ,KAAO,EAAA,YAAA,EAAc,UAAU,CAAA;AAClD;AAEgB,SAAA,OAAA,CAAW,KAAW,EAAA,YAAA,EAAkB,UAAiD,EAAA;AACvG,EAAI,IAAA,KAAA,KAAU,UAAa,YAAiB,KAAA,MAAA;AAC1C,IAAO,OAAA,KAAA;AAET,EAAA,IAAI,OAAO,KAAU,KAAA,QAAA;AACnB,IAAA,OAAO,KAAU,KAAA,YAAA;AAEnB,EAAA,IAAI,OAAO,UAAe,KAAA,UAAA;AACxB,IAAO,OAAA,UAAA,CAAW,OAAO,YAAY,CAAA;AAEvC,EAAA,IAAI,OAAO,UAAe,KAAA,QAAA;AACxB,IAAA,OAAO,KAAQ,GAAA,UAAqB,CAAM,KAAA,YAAA,GAAe,UAAqB,CAAA;AAEhF,EAAO,OAAA,OAAA,CAAQ,OAAO,YAAY,CAAA;AACpC;;;;"}