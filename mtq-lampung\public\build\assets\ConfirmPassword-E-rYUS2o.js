import{d as p,x as c,h as i,o as d,w as t,e as a,b as r,u as s,g as f,j as u,i as n,k as _}from"./app-BxByyVXe.js";import{_ as w,a as g,b}from"./Label.vue_vue_type_script_setup_true_lang-Dc3XvMbX.js";import{_ as C}from"./AppLogoIcon.vue_vue_type_script_setup_true_lang-43VByX6w.js";import{L as V,_ as h}from"./AuthLayout.vue_vue_type_script_setup_true_lang-DidZ5Moo.js";import"./useForwardExpose-67BWFZEI.js";const x={class:"space-y-6"},k={class:"grid gap-2"},y={class:"flex items-center"},T=p({__name:"ConfirmPassword",setup($){const e=c({password:""}),m=()=>{e.post(route("password.confirm"),{onFinish:()=>{e.reset()}})};return(v,o)=>(d(),i(h,{title:"Confirm your password",description:"This is a secure area of the application. Please confirm your password before continuing."},{default:t(()=>[a(s(f),{title:"Confirm password"}),r("form",{onSubmit:u(m,["prevent"])},[r("div",x,[r("div",k,[a(s(w),{htmlFor:"password"},{default:t(()=>o[1]||(o[1]=[n("Password")])),_:1,__:[1]}),a(s(g),{id:"password",type:"password",class:"mt-1 block w-full",modelValue:s(e).password,"onUpdate:modelValue":o[0]||(o[0]=l=>s(e).password=l),required:"",autocomplete:"current-password",autofocus:""},null,8,["modelValue"]),a(b,{message:s(e).errors.password},null,8,["message"])]),r("div",y,[a(s(C),{class:"w-full",disabled:s(e).processing},{default:t(()=>[s(e).processing?(d(),i(s(V),{key:0,class:"h-4 w-4 animate-spin"})):_("",!0),o[2]||(o[2]=n(" Confirm Password "))]),_:1,__:[2]},8,["disabled"])])])],32)]),_:1}))}});export{T as default};
