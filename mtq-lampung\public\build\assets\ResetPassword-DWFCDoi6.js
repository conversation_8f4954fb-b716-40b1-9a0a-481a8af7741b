import{d as c,x as g,h as w,o as u,w as t,e as a,b as l,u as s,g as V,j as b,i as d,k}from"./app-BxByyVXe.js";import{_ as i,a as m,b as n}from"./Label.vue_vue_type_script_setup_true_lang-Dc3XvMbX.js";import{_ as y}from"./AppLogoIcon.vue_vue_type_script_setup_true_lang-43VByX6w.js";import{L as C,_ as v}from"./AuthLayout.vue_vue_type_script_setup_true_lang-DidZ5Moo.js";import"./useForwardExpose-67BWFZEI.js";const x={class:"grid gap-6"},P={class:"grid gap-2"},$={class:"grid gap-2"},N={class:"grid gap-2"},F=c({__name:"ResetPassword",props:{token:{},email:{}},setup(f){const p=f,e=g({token:p.token,email:p.email,password:"",password_confirmation:""}),_=()=>{e.post(route("password.store"),{onFinish:()=>{e.reset("password","password_confirmation")}})};return(R,o)=>(u(),w(v,{title:"Reset password",description:"Please enter your new password below"},{default:t(()=>[a(s(V),{title:"Reset password"}),l("form",{onSubmit:b(_,["prevent"])},[l("div",x,[l("div",P,[a(s(i),{for:"email"},{default:t(()=>o[3]||(o[3]=[d("Email")])),_:1,__:[3]}),a(s(m),{id:"email",type:"email",name:"email",autocomplete:"email",modelValue:s(e).email,"onUpdate:modelValue":o[0]||(o[0]=r=>s(e).email=r),class:"mt-1 block w-full",readonly:""},null,8,["modelValue"]),a(n,{message:s(e).errors.email,class:"mt-2"},null,8,["message"])]),l("div",$,[a(s(i),{for:"password"},{default:t(()=>o[4]||(o[4]=[d("Password")])),_:1,__:[4]}),a(s(m),{id:"password",type:"password",name:"password",autocomplete:"new-password",modelValue:s(e).password,"onUpdate:modelValue":o[1]||(o[1]=r=>s(e).password=r),class:"mt-1 block w-full",autofocus:"",placeholder:"Password"},null,8,["modelValue"]),a(n,{message:s(e).errors.password},null,8,["message"])]),l("div",N,[a(s(i),{for:"password_confirmation"},{default:t(()=>o[5]||(o[5]=[d(" Confirm Password ")])),_:1,__:[5]}),a(s(m),{id:"password_confirmation",type:"password",name:"password_confirmation",autocomplete:"new-password",modelValue:s(e).password_confirmation,"onUpdate:modelValue":o[2]||(o[2]=r=>s(e).password_confirmation=r),class:"mt-1 block w-full",placeholder:"Confirm password"},null,8,["modelValue"]),a(n,{message:s(e).errors.password_confirmation},null,8,["message"])]),a(s(y),{type:"submit",class:"mt-4 w-full",disabled:s(e).processing},{default:t(()=>[s(e).processing?(u(),w(s(C),{key:0,class:"h-4 w-4 animate-spin"})):k("",!0),o[6]||(o[6]=d(" Reset password "))]),_:1,__:[6]},8,["disabled"])])],32)]),_:1}))}});export{F as default};
