{"version": 3, "file": "useDirection.cjs", "sources": ["../../src/shared/useDirection.ts"], "sourcesContent": ["import type { Ref } from 'vue'\nimport type { Direction } from './types'\nimport { computed, ref } from 'vue'\nimport { injectConfigProviderContext } from '@/ConfigProvider/ConfigProvider.vue'\n\nexport function useDirection(dir?: Ref<Direction | undefined>) {\n  const context = injectConfigProviderContext({\n    dir: ref('ltr'),\n  })\n  return computed(() => dir?.value || context.dir?.value || 'ltr')\n}\n"], "names": ["injectConfigProviderContext", "ref", "computed"], "mappings": ";;;;;AAKO,SAAS,aAAa,GAAkC,EAAA;AAC7D,EAAA,MAAM,UAAUA,yDAA4B,CAAA;AAAA,IAC1C,GAAA,EAAKC,QAAI,KAAK;AAAA,GACf,CAAA;AACD,EAAA,OAAOC,aAAS,MAAM,GAAA,EAAK,SAAS,OAAQ,CAAA,GAAA,EAAK,SAAS,KAAK,CAAA;AACjE;;;;"}