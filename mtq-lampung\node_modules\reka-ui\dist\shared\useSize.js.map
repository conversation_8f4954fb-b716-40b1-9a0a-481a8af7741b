{"version": 3, "file": "useSize.js", "sources": ["../../src/shared/useSize.ts"], "sourcesContent": ["import type { MaybeElementRef } from '@vueuse/core'\nimport { unrefElement } from '@vueuse/core'\nimport { computed, onMounted, ref } from 'vue'\n\nexport function useSize(element: MaybeElementRef) {\n  const size = ref<{ width: number, height: number }>()\n  const width = computed(() => size.value?.width ?? 0)\n  const height = computed(() => size.value?.height ?? 0)\n\n  onMounted(() => {\n    const el = unrefElement(element) as HTMLElement\n    if (el) {\n      // provide size as early as possible\n      size.value = { width: el.offsetWidth, height: el.offsetHeight }\n\n      const resizeObserver = new ResizeObserver((entries) => {\n        if (!Array.isArray(entries))\n          return\n\n        // Since we only observe the one element, we don't need to loop over the\n        // array\n        if (!entries.length)\n          return\n\n        const entry = entries[0]\n        let width: number\n        let height: number\n\n        if ('borderBoxSize' in entry) {\n          const borderSizeEntry = entry.borderBoxSize\n          // iron out differences between browsers\n          const borderSize = Array.isArray(borderSizeEntry)\n            ? borderSizeEntry[0]\n            : borderSizeEntry\n          width = borderSize.inlineSize\n          height = borderSize.blockSize\n        }\n        else {\n          // for browsers that don't support `borderBoxSize`\n          // we calculate it ourselves to get the correct border box.\n          width = el.offsetWidth\n          height = el.offsetHeight\n        }\n\n        // temporary disable width/height from resize observer. borderSizeEntry seems to be incorrect\n        size.value = { width, height }\n      })\n\n      resizeObserver.observe(el, { box: 'border-box' })\n\n      return () => resizeObserver.unobserve(el)\n    }\n    else {\n      // We only want to reset to `undefined` when the element becomes `null`,\n      // not if it changes to another element.\n      size.value = undefined\n    }\n  })\n\n  return {\n    width,\n    height,\n  }\n}\n"], "names": ["width", "height"], "mappings": ";;;AAIO,SAAS,QAAQ,OAA0B,EAAA;AAChD,EAAA,MAAM,OAAO,GAAuC,EAAA;AACpD,EAAA,MAAM,QAAQ,QAAS,CAAA,MAAM,IAAK,CAAA,KAAA,EAAO,SAAS,CAAC,CAAA;AACnD,EAAA,MAAM,SAAS,QAAS,CAAA,MAAM,IAAK,CAAA,KAAA,EAAO,UAAU,CAAC,CAAA;AAErD,EAAA,SAAA,CAAU,MAAM;AACd,IAAM,MAAA,EAAA,GAAK,aAAa,OAAO,CAAA;AAC/B,IAAA,IAAI,EAAI,EAAA;AAEN,MAAA,IAAA,CAAK,QAAQ,EAAE,KAAA,EAAO,GAAG,WAAa,EAAA,MAAA,EAAQ,GAAG,YAAa,EAAA;AAE9D,MAAA,MAAM,cAAiB,GAAA,IAAI,cAAe,CAAA,CAAC,OAAY,KAAA;AACrD,QAAI,IAAA,CAAC,KAAM,CAAA,OAAA,CAAQ,OAAO,CAAA;AACxB,UAAA;AAIF,QAAA,IAAI,CAAC,OAAQ,CAAA,MAAA;AACX,UAAA;AAEF,QAAM,MAAA,KAAA,GAAQ,QAAQ,CAAC,CAAA;AACvB,QAAIA,IAAAA,MAAAA;AACJ,QAAIC,IAAAA,OAAAA;AAEJ,QAAA,IAAI,mBAAmB,KAAO,EAAA;AAC5B,UAAA,MAAM,kBAAkB,KAAM,CAAA,aAAA;AAE9B,UAAA,MAAM,aAAa,KAAM,CAAA,OAAA,CAAQ,eAAe,CAC5C,GAAA,eAAA,CAAgB,CAAC,CACjB,GAAA,eAAA;AACJ,UAAAD,SAAQ,UAAW,CAAA,UAAA;AACnB,UAAAC,UAAS,UAAW,CAAA,SAAA;AAAA,SAEjB,MAAA;AAGH,UAAAD,SAAQ,EAAG,CAAA,WAAA;AACX,UAAAC,UAAS,EAAG,CAAA,YAAA;AAAA;AAId,QAAA,IAAA,CAAK,KAAQ,GAAA,EAAE,KAAAD,EAAAA,MAAAA,EAAO,QAAAC,OAAO,EAAA;AAAA,OAC9B,CAAA;AAED,MAAA,cAAA,CAAe,OAAQ,CAAA,EAAA,EAAI,EAAE,GAAA,EAAK,cAAc,CAAA;AAEhD,MAAO,OAAA,MAAM,cAAe,CAAA,SAAA,CAAU,EAAE,CAAA;AAAA,KAErC,MAAA;AAGH,MAAA,IAAA,CAAK,KAAQ,GAAA,MAAA;AAAA;AACf,GACD,CAAA;AAED,EAAO,OAAA;AAAA,IACL,KAAA;AAAA,IACA;AAAA,GACF;AACF;;;;"}