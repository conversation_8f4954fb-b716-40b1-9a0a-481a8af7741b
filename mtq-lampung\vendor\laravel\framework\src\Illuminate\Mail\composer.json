{"name": "illuminate/mail", "description": "The Illuminate Mail package.", "license": "MIT", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": "^8.2", "illuminate/collections": "^12.0", "illuminate/container": "^12.0", "illuminate/contracts": "^12.0", "illuminate/macroable": "^12.0", "illuminate/support": "^12.0", "league/commonmark": "^2.7", "psr/log": "^1.0|^2.0|^3.0", "symfony/mailer": "^7.2.0", "tijsverkoyen/css-to-inline-styles": "^2.2.5"}, "autoload": {"psr-4": {"Illuminate\\Mail\\": ""}}, "extra": {"branch-alias": {"dev-master": "12.x-dev"}}, "suggest": {"aws/aws-sdk-php": "Required to use the SES mail driver (^3.322.9).", "illuminate/http": "Required to create an attachment from an UploadedFile instance (^12.0).", "resend/resend-php": "Required to enable support for the Resend mail transport (^0.10.0).", "symfony/http-client": "Required to use the Symfony API mail transports (^7.2).", "symfony/mailgun-mailer": "Required to enable support for the Mailgun mail transport (^7.2).", "symfony/postmark-mailer": "Required to enable support for the Postmark mail transport (^7.2)."}, "config": {"sort-packages": true}, "minimum-stability": "dev"}