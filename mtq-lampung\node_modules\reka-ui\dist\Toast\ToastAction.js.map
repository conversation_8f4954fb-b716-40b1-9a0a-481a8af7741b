{"version": 3, "file": "ToastAction.js", "sources": ["../../src/Toast/ToastAction.vue"], "sourcesContent": ["<script lang=\"ts\">\nimport type { ToastCloseProps } from './ToastClose.vue'\n\nexport interface ToastActionProps extends ToastCloseProps {\n  /**\n   * A short description for an alternate way to carry out the action. For screen reader users\n   * who will not be able to navigate to the button easily/quickly.\n   * @example <ToastAction altText=\"Goto account settings to upgrade\">Upgrade</ToastAction>\n   * @example <ToastAction altText=\"Undo (Alt+U)\">Undo</ToastAction>\n   */\n  altText: string\n}\n</script>\n\n<script setup lang=\"ts\">\nimport { useForwardExpose } from '@/shared'\nimport ToastAnnounceExclude from './ToastAnnounceExclude.vue'\nimport ToastClose from './ToastClose.vue'\n\nconst props = defineProps<ToastActionProps>()\n\nif (!props.altText)\n  throw new Error('Missing prop `altText` expected on `ToastAction`')\n\nconst { forwardRef } = useForwardExpose()\n</script>\n\n<template>\n  <ToastAnnounceExclude\n    v-if=\"altText\"\n    :alt-text=\"altText\"\n    as-child\n  >\n    <ToastClose\n      :ref=\"forwardRef\"\n      :as=\"as\"\n      :as-child=\"asChild\"\n    >\n      <slot />\n    </ToastClose>\n  </ToastAnnounceExclude>\n</template>\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAmBA,IAAA,MAAM,KAAQ,GAAA,OAAA;AAEd,IAAA,IAAI,CAAC,KAAM,CAAA,OAAA;AACT,MAAM,MAAA,IAAI,MAAM,kDAAkD,CAAA;AAEpE,IAAM,MAAA,EAAE,UAAW,EAAA,GAAI,gBAAiB,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;"}