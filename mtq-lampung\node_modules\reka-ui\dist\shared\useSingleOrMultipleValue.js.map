{"version": 3, "file": "useSingleOrMultipleValue.js", "sources": ["../../src/shared/useSingleOrMultipleValue.ts"], "sourcesContent": ["import type { Ref } from 'vue'\nimport type { AcceptableValue, SingleOrMultipleProps } from './types'\nimport { useVModel } from '@vueuse/core'\nimport { isEqual } from 'ohash'\nimport { computed } from 'vue'\nimport { isValueEqualOrExist } from './isValueEqualOrExist'\n\n/**\n * Validates the props and it makes sure that the types are coherent with each other\n *\n * 1. If type, defaultValue, and modelValue are all undefined, throw an error.\n * 2. If modelValue and defaultValue are defined and not of the same type, throw an error.\n * 3. If type is defined:\n *    a. If type is 'single' and either modelValue or defaultValue is an array, log an error and return 'multiple'.\n *    b. If type is 'multiple' and neither modelValue nor defaultValue is an array, log an error and return 'single'.\n * 4. Return 'multiple' if modelValue is an array, else return 'single'.\n */\nfunction validateProps({ type, defaultValue, modelValue }: SingleOrMultipleProps) {\n  const value = modelValue || defaultValue\n  const canTypeBeInferred = modelValue !== undefined || defaultValue !== undefined\n\n  if (canTypeBeInferred)\n    return Array.isArray(value) ? 'multiple' : 'single'\n  else\n    return type ?? 'single' // always fallback to `single`\n}\n\nfunction getDefaultType({ type, defaultValue, modelValue }: SingleOrMultipleProps) {\n  if (type)\n    return type\n\n  return validateProps({ type, defaultValue, modelValue })\n}\n\nfunction getDefaultValue({ type, defaultValue }: SingleOrMultipleProps) {\n  if (defaultValue !== undefined)\n    return defaultValue\n\n  return (type === 'single') ? undefined : []\n}\n\nexport function useSingleOrMultipleValue<P extends SingleOrMultipleProps, Name extends string>(\n  props: P,\n  emits: (name: Name, ...args: any[]) => void,\n) {\n  const type = computed(() => getDefaultType(props))\n  const modelValue = useVModel(props, 'modelValue', emits, {\n    defaultValue: getDefaultValue(props),\n    passive: (props.modelValue === undefined) as false,\n    deep: true,\n  }) as Ref<AcceptableValue | AcceptableValue[] | undefined>\n\n  function changeModelValue(value: AcceptableValue) {\n    if (type.value === 'single') {\n      modelValue.value = isEqual(value, modelValue.value) ? undefined : value\n    }\n    else {\n      const modelValueArray = Array.isArray(modelValue.value) ? [...(modelValue.value as AcceptableValue[] || [])] : [modelValue.value].filter(Boolean)\n      if (isValueEqualOrExist(modelValueArray, value)) {\n        const index = modelValueArray.findIndex(i => isEqual(i, value))\n        modelValueArray.splice(index, 1)\n      }\n      else {\n        modelValueArray.push(value)\n      }\n      modelValue.value = modelValueArray\n    }\n  }\n\n  const isSingle = computed(() => type.value === 'single')\n\n  return {\n    modelValue,\n    changeModelValue,\n    isSingle,\n  }\n}\n"], "names": [], "mappings": ";;;;;AAiBA,SAAS,aAAc,CAAA,EAAE,IAAM,EAAA,YAAA,EAAc,YAAqC,EAAA;AAChF,EAAA,MAAM,QAAQ,UAAc,IAAA,YAAA;AAC5B,EAAM,MAAA,iBAAA,GAAoB,UAAe,KAAA,MAAA,IAAa,YAAiB,KAAA,MAAA;AAEvE,EAAI,IAAA,iBAAA;AACF,IAAA,OAAO,KAAM,CAAA,OAAA,CAAQ,KAAK,CAAA,GAAI,UAAa,GAAA,QAAA;AAAA;AAE3C,IAAA,OAAO,IAAQ,IAAA,QAAA;AACnB;AAEA,SAAS,cAAe,CAAA,EAAE,IAAM,EAAA,YAAA,EAAc,YAAqC,EAAA;AACjF,EAAI,IAAA,IAAA;AACF,IAAO,OAAA,IAAA;AAET,EAAA,OAAO,aAAc,CAAA,EAAE,IAAM,EAAA,YAAA,EAAc,YAAY,CAAA;AACzD;AAEA,SAAS,eAAgB,CAAA,EAAE,IAAM,EAAA,YAAA,EAAuC,EAAA;AACtE,EAAA,IAAI,YAAiB,KAAA,MAAA;AACnB,IAAO,OAAA,YAAA;AAET,EAAQ,OAAA,IAAA,KAAS,QAAY,GAAA,MAAA,GAAY,EAAC;AAC5C;AAEgB,SAAA,wBAAA,CACd,OACA,KACA,EAAA;AACA,EAAA,MAAM,IAAO,GAAA,QAAA,CAAS,MAAM,cAAA,CAAe,KAAK,CAAC,CAAA;AACjD,EAAA,MAAM,UAAa,GAAA,SAAA,CAAU,KAAO,EAAA,YAAA,EAAc,KAAO,EAAA;AAAA,IACvD,YAAA,EAAc,gBAAgB,KAAK,CAAA;AAAA,IACnC,OAAA,EAAU,MAAM,UAAe,KAAA,MAAA;AAAA,IAC/B,IAAM,EAAA;AAAA,GACP,CAAA;AAED,EAAA,SAAS,iBAAiB,KAAwB,EAAA;AAChD,IAAI,IAAA,IAAA,CAAK,UAAU,QAAU,EAAA;AAC3B,MAAA,UAAA,CAAW,QAAQ,OAAQ,CAAA,KAAA,EAAO,UAAW,CAAA,KAAK,IAAI,MAAY,GAAA,KAAA;AAAA,KAE/D,MAAA;AACH,MAAA,MAAM,kBAAkB,KAAM,CAAA,OAAA,CAAQ,WAAW,KAAK,CAAA,GAAI,CAAC,GAAI,UAAA,CAAW,KAA8B,IAAA,EAAG,CAAI,GAAA,CAAC,WAAW,KAAK,CAAA,CAAE,OAAO,OAAO,CAAA;AAChJ,MAAI,IAAA,mBAAA,CAAoB,eAAiB,EAAA,KAAK,CAAG,EAAA;AAC/C,QAAA,MAAM,QAAQ,eAAgB,CAAA,SAAA,CAAU,OAAK,OAAQ,CAAA,CAAA,EAAG,KAAK,CAAC,CAAA;AAC9D,QAAgB,eAAA,CAAA,MAAA,CAAO,OAAO,CAAC,CAAA;AAAA,OAE5B,MAAA;AACH,QAAA,eAAA,CAAgB,KAAK,KAAK,CAAA;AAAA;AAE5B,MAAA,UAAA,CAAW,KAAQ,GAAA,eAAA;AAAA;AACrB;AAGF,EAAA,MAAM,QAAW,GAAA,QAAA,CAAS,MAAM,IAAA,CAAK,UAAU,QAAQ,CAAA;AAEvD,EAAO,OAAA;AAAA,IACL,UAAA;AAAA,IACA,gBAAA;AAAA,IACA;AAAA,GACF;AACF;;;;"}