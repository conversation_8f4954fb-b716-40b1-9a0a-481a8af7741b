{"version": 3, "file": "useIsUsingKeyboard.cjs", "sources": ["../../src/shared/useIsUsingKeyboard.ts"], "sourcesContent": ["import { createSharedComposable, useEventListener } from '@vueuse/core'\nimport { onMounted, ref } from 'vue'\n\nfunction useIsUsingKeyboardImpl() {\n  const isUsingKeyboard = ref(false)\n\n  onMounted(() => {\n    // Capture phase ensures we set the boolean before any side effects execute\n    // in response to the key or pointer event as they might depend on this value.\n    useEventListener('keydown', () => {\n      isUsingKeyboard.value = true\n    }, { capture: true, passive: true })\n\n    useEventListener(['pointerdown', 'pointermove'], () => {\n      isUsingKeyboard.value = false\n    }, { capture: true, passive: true })\n  })\n\n  return isUsingKeyboard\n}\n\nexport const useIsUsingKeyboard = createSharedComposable(useIsUsingKeyboardImpl)\n"], "names": ["ref", "onMounted", "useEventListener", "createSharedComposable"], "mappings": ";;;;;AAGA,SAAS,sBAAyB,GAAA;AAChC,EAAM,MAAA,eAAA,GAAkBA,QAAI,KAAK,CAAA;AAEjC,EAAAC,aAAA,CAAU,MAAM;AAGd,IAAAC,qBAAA,CAAiB,WAAW,MAAM;AAChC,MAAA,eAAA,CAAgB,KAAQ,GAAA,IAAA;AAAA,OACvB,EAAE,OAAA,EAAS,IAAM,EAAA,OAAA,EAAS,MAAM,CAAA;AAEnC,IAAAA,qBAAA,CAAiB,CAAC,aAAA,EAAe,aAAa,CAAA,EAAG,MAAM;AACrD,MAAA,eAAA,CAAgB,KAAQ,GAAA,KAAA;AAAA,OACvB,EAAE,OAAA,EAAS,IAAM,EAAA,OAAA,EAAS,MAAM,CAAA;AAAA,GACpC,CAAA;AAED,EAAO,OAAA,eAAA;AACT;AAEa,MAAA,kBAAA,GAAqBC,4BAAuB,sBAAsB;;;;"}