{"version": 3, "file": "TooltipArrow.js", "sources": ["../../src/Tooltip/TooltipArrow.vue"], "sourcesContent": ["<script lang=\"ts\">\nimport type { PrimitiveProps } from '@/Primitive'\nimport { useForwardExpose } from '@/shared'\n\nexport interface TooltipArrowProps extends PrimitiveProps {\n  /**\n   * The width of the arrow in pixels.\n   *\n   * @defaultValue 10\n   */\n  width?: number\n\n  /**\n   * The height of the arrow in pixels.\n   *\n   * @defaultValue 5\n   */\n  height?: number\n}\n</script>\n\n<script setup lang=\"ts\">\nimport { PopperArrow } from '@/Popper'\n\nconst props = withDefaults(defineProps<TooltipArrowProps>(), {\n  width: 10,\n  height: 5,\n  as: 'svg',\n})\nuseForwardExpose()\n</script>\n\n<template>\n  <PopperArrow v-bind=\"props\">\n    <slot />\n  </PopperArrow>\n</template>\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAwBA,IAAA,MAAM,KAAQ,GAAA,OAAA;AAKd,IAAiB,gBAAA,EAAA;;;;;;;;;;;;;;"}