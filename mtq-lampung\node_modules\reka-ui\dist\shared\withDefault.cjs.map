{"version": 3, "file": "withDefault.cjs", "sources": ["../../src/shared/withDefault.ts"], "sourcesContent": ["import type { ComponentOptionsBase, DefineComponent, VNodeProps } from 'vue'\nimport type { ComponentProps } from 'vue-component-type-helpers'\nimport { h, mergeProps } from 'vue'\nimport { useForwardExpose } from './useForwardExpose'\n\n// TODO: TEST\n\n// From vue next\n// https://github.com/vuejs/core/blob/1f2a652a9d2e3bec472fb1786a4c16d6ccfa1fb1/packages/runtime-core/src/h.ts#L53-L58\ntype RawProps = VNodeProps & {\n  // used to differ from a single VNode object as children\n  __v_isVNode?: never\n  // used to differ from Array children\n  [Symbol.iterator]?: never\n} & Record<string, any>\n\n// types inspired from vue-test-utils\n// https://github.com/vuejs/test-utils/blob/main/src/mount.ts#L36\ninterface MountingOptions<Props> {\n  /**\n   * Default props for the component\n   */\n  props?: (RawProps & Props) | ({} extends Props ? null : never) | ((attrs: Record<string, any>) => (RawProps & Props))\n  /**\n   * Pass attributes into the component\n   */\n  attrs?: Record<string, unknown>\n}\n\nexport function withDefault<\n  T,\n  C = T extends ((...args: any) => any) | (new (...args: any) => any)\n    ? T\n    : T extends { props?: infer Props }\n      ? DefineComponent<\n        Props extends Readonly<(infer PropNames)[]> | (infer PropNames)[]\n          ? { [key in PropNames extends string ? PropNames : string]?: any }\n          : Props\n      >\n      : DefineComponent,\n  P extends ComponentProps<C> = ComponentProps<C>,\n>(\n  originalComponent: T,\n  options?: MountingOptions<P>\n): T\n\nexport function withDefault<T extends ComponentOptionsBase<{}, {}, {}, any, any, any, any, any>>(WrappedComponent: T, options?: MountingOptions<any>) {\n  return ({\n    inheritAttrs: false,\n    name: `${WrappedComponent.__name ?? ''}Wrapper`,\n    setup(_, ctx) {\n      return () => {\n        const optionProps = typeof options?.props === 'function' ? options?.props(ctx.attrs) : options?.props\n        const { forwardRef } = useForwardExpose()\n\n        const mergedProps = mergeProps(optionProps, ctx.attrs)\n        return h(WrappedComponent, { ...mergedProps, ref: forwardRef }, ctx.slots)\n      }\n    },\n  }) as T\n}\n"], "names": ["useForwardExpose", "mergeProps", "h"], "mappings": ";;;;;AA8CgB,SAAA,WAAA,CAAiF,kBAAqB,OAAgC,EAAA;AACpJ,EAAQ,OAAA;AAAA,IACN,YAAc,EAAA,KAAA;AAAA,IACd,IAAM,EAAA,CAAA,EAAG,gBAAiB,CAAA,MAAA,IAAU,EAAE,CAAA,OAAA,CAAA;AAAA,IACtC,KAAA,CAAM,GAAG,GAAK,EAAA;AACZ,MAAA,OAAO,MAAM;AACX,QAAM,MAAA,WAAA,GAAc,OAAO,OAAA,EAAS,KAAU,KAAA,UAAA,GAAa,SAAS,KAAM,CAAA,GAAA,CAAI,KAAK,CAAA,GAAI,OAAS,EAAA,KAAA;AAChG,QAAM,MAAA,EAAE,UAAW,EAAA,GAAIA,wCAAiB,EAAA;AAExC,QAAA,MAAM,WAAc,GAAAC,cAAA,CAAW,WAAa,EAAA,GAAA,CAAI,KAAK,CAAA;AACrD,QAAO,OAAAC,KAAA,CAAE,kBAAkB,EAAE,GAAG,aAAa,GAAK,EAAA,UAAA,EAAc,EAAA,GAAA,CAAI,KAAK,CAAA;AAAA,OAC3E;AAAA;AACF,GACF;AACF;;;;"}