{"version": 3, "file": "utils.js", "sources": ["../../src/Menu/utils.ts"], "sourcesContent": ["import { getActiveElement } from '@/shared'\n\nexport type CheckedState = boolean | 'indeterminate'\nexport type Direction = 'ltr' | 'rtl'\n\nexport const ITEM_NAME = 'MenuItem'\nexport const ITEM_SELECT = 'menu.itemSelect'\nexport const SELECTION_KEYS = ['Enter', ' ']\nexport const FIRST_KEYS = ['ArrowDown', 'PageUp', 'Home']\nexport const LAST_KEYS = ['ArrowUp', 'PageDown', 'End']\nexport const FIRST_LAST_KEYS = [...FIRST_KEYS, ...LAST_KEYS]\nexport const SUB_OPEN_KEYS: Record<Direction, string[]> = {\n  ltr: [...SELECTION_KEYS, 'ArrowRight'],\n  rtl: [...SELECTION_KEYS, 'ArrowLeft'],\n}\nexport const SUB_CLOSE_KEYS: Record<Direction, string[]> = {\n  ltr: ['ArrowLeft'],\n  rtl: ['ArrowRight'],\n}\n\nexport function getOpenState(open: boolean) {\n  return open ? 'open' : 'closed'\n}\n\nexport function isIndeterminate(\n  checked?: CheckedState,\n): checked is 'indeterminate' {\n  return checked === 'indeterminate'\n}\n\nexport function getCheckedState(checked: CheckedState) {\n  return isIndeterminate(checked)\n    ? 'indeterminate'\n    : checked\n      ? 'checked'\n      : 'unchecked'\n}\n\nexport function focusFirst(candidates: HTMLElement[]) {\n  const PREVIOUSLY_FOCUSED_ELEMENT = getActiveElement()\n  for (const candidate of candidates) {\n    // if focus is already where we want to go, we don't want to keep going through the candidates\n    if (candidate === PREVIOUSLY_FOCUSED_ELEMENT)\n      return\n    candidate.focus()\n    if (getActiveElement() !== PREVIOUSLY_FOCUSED_ELEMENT)\n      return\n  }\n}\n\nexport interface Point {\n  x: number\n  y: number\n}\nexport type Polygon = Point[]\nexport type Side = 'left' | 'right'\nexport interface GraceIntent {\n  area: Polygon\n  side: Side\n}\n\n// Determine if a point is inside of a polygon.\n// Based on https://github.com/substack/point-in-polygon\nexport function isPointInPolygon(point: Point, polygon: Polygon) {\n  const { x, y } = point\n  let inside = false\n  for (let i = 0, j = polygon.length - 1; i < polygon.length; j = i++) {\n    const xi = polygon[i].x\n    const yi = polygon[i].y\n    const xj = polygon[j].x\n    const yj = polygon[j].y\n\n    const intersect = ((yi > y) !== (yj > y)) && (x < (xj - xi) * (y - yi) / (yj - yi) + xi)\n    if (intersect)\n      inside = !inside\n  }\n\n  return inside\n}\n\nexport function isPointerInGraceArea(event: PointerEvent, area?: Polygon) {\n  if (!area)\n    return false\n  const cursorPos = { x: event.clientX, y: event.clientY }\n  return isPointInPolygon(cursorPos, area)\n}\n\nexport function isMouseEvent(event: PointerEvent) {\n  return event.pointerType === 'mouse'\n}\n"], "names": [], "mappings": ";;AAMO,MAAM,WAAc,GAAA;AACd,MAAA,cAAA,GAAiB,CAAC,OAAA,EAAS,GAAG;AACpC,MAAM,UAAa,GAAA,CAAC,WAAa,EAAA,QAAA,EAAU,MAAM,CAAA;AACjD,MAAM,SAAY,GAAA,CAAC,SAAW,EAAA,UAAA,EAAY,KAAK;AAC/C,MAAM,eAAkB,GAAA,CAAC,GAAG,UAAA,EAAY,GAAG,SAAS;AACpD,MAAM,aAA6C,GAAA;AAAA,EACxD,GAAK,EAAA,CAAC,GAAG,cAAA,EAAgB,YAAY,CAAA;AAAA,EACrC,GAAK,EAAA,CAAC,GAAG,cAAA,EAAgB,WAAW;AACtC;AACO,MAAM,cAA8C,GAAA;AAAA,EACzD,GAAA,EAAK,CAAC,WAAW,CAAA;AAAA,EACjB,GAAA,EAAK,CAAC,YAAY;AACpB;AAEO,SAAS,aAAa,IAAe,EAAA;AAC1C,EAAA,OAAO,OAAO,MAAS,GAAA,QAAA;AACzB;AAEO,SAAS,gBACd,OAC4B,EAAA;AAC5B,EAAA,OAAO,OAAY,KAAA,eAAA;AACrB;AAEO,SAAS,gBAAgB,OAAuB,EAAA;AACrD,EAAA,OAAO,eAAgB,CAAA,OAAO,CAC1B,GAAA,eAAA,GACA,UACE,SACA,GAAA,WAAA;AACR;AAEO,SAAS,WAAW,UAA2B,EAAA;AACpD,EAAA,MAAM,6BAA6B,gBAAiB,EAAA;AACpD,EAAA,KAAA,MAAW,aAAa,UAAY,EAAA;AAElC,IAAA,IAAI,SAAc,KAAA,0BAAA;AAChB,MAAA;AACF,IAAA,SAAA,CAAU,KAAM,EAAA;AAChB,IAAA,IAAI,kBAAuB,KAAA,0BAAA;AACzB,MAAA;AAAA;AAEN;AAegB,SAAA,gBAAA,CAAiB,OAAc,OAAkB,EAAA;AAC/D,EAAM,MAAA,EAAE,CAAG,EAAA,CAAA,EAAM,GAAA,KAAA;AACjB,EAAA,IAAI,MAAS,GAAA,KAAA;AACb,EAAS,KAAA,IAAA,CAAA,GAAI,CAAG,EAAA,CAAA,GAAI,OAAQ,CAAA,MAAA,GAAS,GAAG,CAAI,GAAA,OAAA,CAAQ,MAAQ,EAAA,CAAA,GAAI,CAAK,EAAA,EAAA;AACnE,IAAM,MAAA,EAAA,GAAK,OAAQ,CAAA,CAAC,CAAE,CAAA,CAAA;AACtB,IAAM,MAAA,EAAA,GAAK,OAAQ,CAAA,CAAC,CAAE,CAAA,CAAA;AACtB,IAAM,MAAA,EAAA,GAAK,OAAQ,CAAA,CAAC,CAAE,CAAA,CAAA;AACtB,IAAM,MAAA,EAAA,GAAK,OAAQ,CAAA,CAAC,CAAE,CAAA,CAAA;AAEtB,IAAM,MAAA,SAAA,GAAc,EAAK,GAAA,CAAA,KAAQ,EAAK,GAAA,CAAA,IAAQ,CAAK,GAAA,CAAA,EAAA,GAAK,EAAO,KAAA,CAAA,GAAI,EAAO,CAAA,IAAA,EAAA,GAAK,EAAM,CAAA,GAAA,EAAA;AACrF,IAAI,IAAA,SAAA;AACF,MAAA,MAAA,GAAS,CAAC,MAAA;AAAA;AAGd,EAAO,OAAA,MAAA;AACT;AAEgB,SAAA,oBAAA,CAAqB,OAAqB,IAAgB,EAAA;AACxE,EAAA,IAAI,CAAC,IAAA;AACH,IAAO,OAAA,KAAA;AACT,EAAA,MAAM,YAAY,EAAE,CAAA,EAAG,MAAM,OAAS,EAAA,CAAA,EAAG,MAAM,OAAQ,EAAA;AACvD,EAAO,OAAA,gBAAA,CAAiB,WAAW,IAAI,CAAA;AACzC;AAEO,SAAS,aAAa,KAAqB,EAAA;AAChD,EAAA,OAAO,MAAM,WAAgB,KAAA,OAAA;AAC/B;;;;"}